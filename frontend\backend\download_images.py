#!/usr/bin/env python3
"""
圖片下載和轉換腳本
將外部圖片 URL 下載到本地並轉換為 webp 格式以提升網頁效能
"""

import os
import requests
from PIL import Image
import hashlib
from urllib.parse import urlparse
import json

# 圖片保存目錄
IMAGES_DIR = "../public/images"
WEBP_QUALITY = 85  # WebP 品質設定 (0-100)

def ensure_directory_exists(directory):
    """確保目錄存在"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"創建目錄: {directory}")

def get_image_hash(url):
    """根據 URL 生成唯一的檔案名稱"""
    return hashlib.md5(url.encode()).hexdigest()

def download_and_convert_image(url, filename):
    """下載圖片並轉換為 webp 格式"""
    try:
        print(f"正在下載: {url}")
        
        # 下載圖片
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 暫存原始圖片
        temp_path = f"{IMAGES_DIR}/temp_{filename}"
        with open(temp_path, 'wb') as f:
            f.write(response.content)
        
        # 轉換為 webp
        webp_filename = filename.replace('.png', '.webp').replace('.jpg', '.webp').replace('.jpeg', '.webp')
        webp_path = f"{IMAGES_DIR}/{webp_filename}"
        
        with Image.open(temp_path) as img:
            # 轉換為 RGB 模式（webp 不支援 RGBA 的某些情況）
            if img.mode in ('RGBA', 'LA', 'P'):
                # 創建白色背景
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            # 保存為 webp
            img.save(webp_path, 'WEBP', quality=WEBP_QUALITY, optimize=True)
        
        # 刪除暫存檔案
        os.remove(temp_path)
        
        print(f"✅ 轉換完成: {webp_filename}")
        return webp_filename
        
    except Exception as e:
        print(f"❌ 下載失敗 {url}: {str(e)}")
        return None

def main():
    """主函數"""
    print("🚀 開始下載和轉換圖片...")
    
    # 確保目錄存在
    ensure_directory_exists(IMAGES_DIR)
    
    # 從 main.py 中提取的圖片 URL 列表
    image_urls = [
        "https://patchwiki.biligame.com/images/arknights/2/29/jy9yyw1bgybmtv2bn42baa8j40adec0.png",
        "https://patchwiki.biligame.com/images/arknights/9/93/kr8tt4hg5054sis5zxr42wv8zxi9qjw.png",
        "https://patchwiki.biligame.com/images/arknights/8/8b/4i2hgwhr4skkxplwp2d8cgp1oxcupb9.png",
        "https://patchwiki.biligame.com/images/arknights/a/ab/m5f61pxzpt5dgdrwn5o7x57ewgbuc2q.png",
        "https://patchwiki.biligame.com/images/arknights/9/9a/16rtjpdqv4qnh2jttqcrnoivtmetckh.png",
        "https://patchwiki.biligame.com/images/arknights/2/23/5i2ma1rk8hju4d5a46aexmusvagojnk.png",
    ]
    
    # URL 到本地檔案的映射
    url_mapping = {}
    
    for url in image_urls:
        # 生成唯一檔案名
        hash_name = get_image_hash(url)
        original_ext = os.path.splitext(urlparse(url).path)[1] or '.png'
        filename = f"{hash_name}{original_ext}"
        
        # 下載並轉換
        webp_filename = download_and_convert_image(url, filename)
        
        if webp_filename:
            url_mapping[url] = f"/images/{webp_filename}"
    
    # 保存映射關係到 JSON 檔案
    mapping_file = f"{IMAGES_DIR}/url_mapping.json"
    with open(mapping_file, 'w', encoding='utf-8') as f:
        json.dump(url_mapping, f, ensure_ascii=False, indent=2)
    
    print(f"\n📋 URL 映射已保存到: {mapping_file}")
    print(f"✨ 完成！共處理 {len([v for v in url_mapping.values() if v])} 張圖片")
    
    # 顯示映射結果
    print("\n🔗 URL 映射結果:")
    for original_url, local_path in url_mapping.items():
        if local_path:
            print(f"  {original_url}")
            print(f"  -> {local_path}")
            print()

if __name__ == "__main__":
    main()
