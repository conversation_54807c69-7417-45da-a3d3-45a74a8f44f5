{".class": "MypyFile", "_fullname": "app.security", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACCESS_TOKEN_EXPIRE_MINUTES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.security.ACCESS_TOKEN_EXPIRE_MINUTES", "name": "ACCESS_TOKEN_EXPIRE_MINUTES", "type": "builtins.int"}}, "ALGORITHM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.security.ALGORITHM", "name": "ALGORITHM", "type": "builtins.str"}}, "CryptContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.security.CryptContext", "name": "CryptContext", "type": {".class": "AnyType", "missing_import_name": "app.security.CryptContext", "source_any": null, "type_of_any": 3}}}, "GOOGLE_CLIENT_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.security.GOOGLE_CLIENT_ID", "name": "GOOGLE_CLIENT_ID", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SECRET_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.security.SECRET_KEY", "name": "SECRET_KEY", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "Session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.security.Session", "name": "Session", "type": {".class": "AnyType", "missing_import_name": "app.security.Session", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.security.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.security.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.security.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.security.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.security.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.security.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "authenticate_user": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["db", "username", "password"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.security.authenticate_user", "name": "authenticate_user", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["db", "username", "password"], "arg_types": [{".class": "AnyType", "missing_import_name": "app.security.Session", "source_any": null, "type_of_any": 3}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authenticate_user", "ret_type": {".class": "UnionType", "items": ["app.models.User", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_access_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["data", "expires_delta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.security.create_access_token", "name": "create_access_token", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "expires_delta"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_access_token", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "crud": {".class": "SymbolTableNode", "cross_ref": "app.crud", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "get_password_hash": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["password"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.security.get_password_hash", "name": "get_password_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["password"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_password_hash", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "id_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.security.id_token", "name": "id_token", "type": {".class": "AnyType", "missing_import_name": "app.security.id_token", "source_any": null, "type_of_any": 3}}}, "jwt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.security.jwt", "name": "jwt", "type": {".class": "AnyType", "missing_import_name": "app.security.jwt", "source_any": null, "type_of_any": 3}}}, "load_dotenv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.security.load_dotenv", "name": "load_dotenv", "type": {".class": "AnyType", "missing_import_name": "app.security.load_dotenv", "source_any": null, "type_of_any": 3}}}, "models": {".class": "SymbolTableNode", "cross_ref": "app.models", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pwd_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.security.pwd_context", "name": "pwd_context", "type": {".class": "AnyType", "missing_import_name": "app.security.CryptContext", "source_any": {".class": "AnyType", "missing_import_name": "app.security.CryptContext", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "requests": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.security.requests", "name": "requests", "type": {".class": "AnyType", "missing_import_name": "app.security.requests", "source_any": null, "type_of_any": 3}}}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "timezone": {".class": "SymbolTableNode", "cross_ref": "datetime.timezone", "kind": "Gdef"}, "verify_google_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.security.verify_google_token", "name": "verify_google_token", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["token"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_google_token", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "verify_password": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["plain_password", "hashed_password"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.security.verify_password", "name": "verify_password", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["plain_password", "hashed_password"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_password", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "D:\\Repos\\gjunedu-platinum-2025\\pybackend\\src\\app\\security.py"}