
  html,
  body {
    width: 100%;
    height: 100%;
    overflow: hidden;
    margin: 0;
    padding: 0;
    background-color: #111111;
    user-select: none;
    font-family: sans-serif;
  }
  @media only screen and (max-width: 1600px) {
    html {
      font-size: 14px;
    }
  }
  @media only screen and (max-width: 428px), screen and (max-height: 926px) {
    html {
      font-size: 14px;
    }
  }
  @media only screen and (max-width: 414px), screen and (max-height: 800px) {
    html {
      font-size: 14px;
    }
  }
  @media only screen and (max-width: 375px), screen and (max-height: 720px) {
    html {
      font-size: 13px;
    }
  }
  @media only screen and (max-width: 360px), screen and (max-height: 640px) {
    html {
      font-size: 12px;
    }
  }
  @media only screen and (max-width: 320px), screen and (max-height: 560px) {
    html {
      font-size: 11px;
    }
  }
  body {
    background-image: url(pc-bg.jpg);
    background-size: cover;
    background-position: center bottom;
  }
  .main {
    width: 100%;
    height: 100%;
  }
  .mainContainer {
    width: 100%;
    height: 100%;
  }
  .mainSlider {
    width: 100%;
    height: 100%;
  }
  ol {
    margin: 0;
    padding: 0;
    list-style: none;
  }
  img {
    -webkit-user-drag: none;
  }
  svg {
    pointer-events: none;
  }
  a:focus {
    outline: none;
  }
  canvas#webgl,
  canvas#lq-webgl {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  
  .loading {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1rem;
    position: absolute;
  }
  .loading.s_complete {
    visibility: hidden;
  }
  .loading .loadingBarGroup {
    position: absolute;
    height: 0;
    top: 50%;
    left: 0;
    width: 100%;
  }
  .loading .loadingBar {
    position: absolute;
    border-bottom: 1px solid #fff;
    width: 0%;
    transition: width 600ms;
  }
  .loading .loadingBar::before,
  .loading .loadingBar::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    height: 1px;
    transition: box-shadow 300ms 100ms;
  }
  .loading .loadingBar::before {
    width: 20px;
  }
  .loading .loadingBar::after {
    width: 1px;
  }
  .loading .loadingBar.left {
    left: 0;
  }
  .loading .loadingBar.left::before {
    box-shadow: 10px 0 15px 1px #fff;
    right: 0;
  }
  .loading .loadingBar.left::after {
    box-shadow: 0.5px 0 8px 2px #fff, 0.5px 0 2px 1px #fff;
    right: 0;
  }
  .loading .loadingBar.right {
    right: 0;
  }
  .loading .loadingBar.right::before {
    box-shadow: -10px 0 15px 1px #fff;
    left: 0;
  }
  .loading .loadingBar.right::after {
    box-shadow: -0.5px 0 8px 2px #fff, -0.5px 0 2px 1px #fff;
    left: 0;
  }
  .loading .loadingContent {
    margin-top: -4em;
  }
  .loading .loadingLogo {
    width: 11em;
    margin-bottom: 2.25em;
  }
  .loading .loadingLogo img {
    display: block;
    width: 100%;
  }
  .loading .loadingProgress {
    text-align: center;
    font-family: "Geometos";
    color: #fff;
    font-size: 1.25em;
    line-height: 1;
    letter-spacing: 0.1em;
  }
  .loading.s_unStart .loadingBar.left::before,
  .loading.s_complete .loadingBar.left::before,
  .loading.s_unStart .loadingBar.right::before,
  .loading.s_complete .loadingBar.right::before,
  .loading.s_unStart .loadingBar.left::after,
  .loading.s_complete .loadingBar.left::after,
  .loading.s_unStart .loadingBar.right::after,
  .loading.s_complete .loadingBar.right::after {
    box-shadow: 0px 0px 0px 0px #fff;
  }
  
  .layout {
    visibility: hidden;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  .layout.loaded {
    visibility: visible;
  }
  .layout .sections {
    width: 100%;
    height: 100%;
  }
  .layout .sections section {
    position: relative;
  }
  .layout .layoutHeader {
    opacity: 0;
    visibility: hidden;
  }
  .fadeIn.layout .layoutHeader {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s 1s, null;
  }
  .layout #nav {
    opacity: 0;
    visibility: hidden;
  }
  .fadeIn.layout #nav {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s 1s, null;
  }
  
  
  .mainHeader {
    height: 3rem;
    position: absolute;
    z-index: 30;
    color: #c9cbd0;
    font-size: 1rem;
    top: 2.75rem;
    right: 5.375rem;
    display: flex;
  }
  .mainHeader #userLogin,
  .mainHeader #userRegister {
    display: inline;
  }
  .mainHeader #userCenter,
  .mainHeader #userLogout {
    display: none;
  }
  .mainHeader[data-status="logged"] #userLogin,
  .mainHeader[data-status="logged"] #userRegister {
    display: none;
  }
  .mainHeader[data-status="logged"] #userCenter,
  .mainHeader[data-status="logged"] #userLogout {
    display: inline;
  }
  .mainHeader .headerUser {
    height: 100%;
    line-height: 100%;
    display: flex;
    align-items: center;
  }
  .mainHeader .headerUser a {
    text-decoration: none;
    color: #c9cbd0;
    font-family: "SansMedium";
    transition: color 200ms;
  }
  .mainHeader .headerUser a:hover {
    color: #fff;
  }
  .mainHeader .headerUser .sep {
    margin: 0 0.8rem;
  }
  .mainHeader .headerMedia {
    height: 100%;
    width: 8.5rem;
    margin-left: 1rem;
    position: relative;
  }
  .mainHeader .headerMediaSwitch {
    right: 0;
    padding: 0.5rem;
    margin: 0 0 0 auto;
    width: 2rem;
    height: 2rem;
    transition: background-color 0.3s;
  }
  .mainHeader .headerMediaSwitch svg {
    display: block;
    width: 100%;
    height: 100%;
  }
  .mainHeader .headerMediaSwitch svg circle {
    fill: #b4b9c2;
    transition: fill 0.3s;
  }
  .mainHeader .headerMediaSwitch svg line {
    stroke: #b4b9c2;
    transition: stroke 0.3s;
  }
  .mainHeader .headerMediaSwitch:hover {
    background-color: #333333;
  }
  .mainHeader .headerMediaSwitch:hover svg circle {
    fill: #22bbff;
  }
  .mainHeader .headerMediaSwitch:hover svg line {
    stroke: #22bbff;
  }
  .mainHeader .headerMediaContentWrapper {
    transition: height 0.3s ease-out;
    height: 0;
    bottom: 0;
    overflow: hidden;
    position: relative;
  }
  .mainHeader .headerMediaContentWrapper.open {
    height: 8rem;
  }
  .mainHeader .headerMediaContent {
    width: 100%;
    bottom: 0;
    position: absolute;
  }
  .mainHeader .headerMediaButton {
    display: inline-block;
    padding: 0.25rem;
    width: 2.25rem;
    height: 2.25rem;
  }
  .mainHeader .headerMediaButton svg {
    transition: fill 200ms;
  }
  .mainHeader .headerMediaButton#bilibili {
    height: 1.5rem;
    background-color: #309ec8;
    color: #fff;
    width: auto;
    white-space: nowrap;
    display: flex;
    align-items: center;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 300ms;
  }
  .mainHeader .headerMediaButton#bilibili svg {
    height: 100%;
    fill: #fff;
  }
  .mainHeader .headerMediaButton#bilibili span {
    font-family: "SansMedium";
    margin-left: 0.5rem;
    display: inline-block;
    font-style: italic;
  }
  .mainHeader .headerMediaButton#bilibili:hover {
    opacity: 1;
  }
  .mainHeader .headerMediaButton:hover svg {
    fill: #fff;
  }
  .mainHeader .headerMediaButtonSet {
    display: flex;
    justify-content: space-around;
  }
  .mainHeader .headerMediaButtonSet svg {
    fill: #9b9a9c;
  }
  .mainHeader .headerMediaButtonSet:first-of-type {
    margin-top: 0.5rem;
  }
  
  @media only screen and (max-width: 428px) {
    .mainHeader {
      height: 2rem;
      top: 2rem;
      right: 2rem;
      z-index: 10;
      position: fixed;
    }
    .mainHeader .headerMedia {
      height: 100%;
      width: 8.5rem;
      margin-left: 1rem;
      position: relative;
      display: none;
    }
  }
  
  .wechat-modal-header {
    text-align: left;
    font-family: "Geometos";
    font-weight: bold;
    font-size: 1.2em;
    padding: 1rem 2rem;
    box-sizing: border-box;
    display: flex;
    align-items: flex-end;
  }
  .wechat-modal-header img {
    border-radius: 3px;
    background: #000;
    width: 2.6em;
    height: 2.6em;
    margin-right: 0.5em;
  }
  .wechat-modal-body {
    text-align: center;
    padding: 0 2rem;
  }
  .wechat-modal-footer {
    text-align: center;
    font-family: "SansRegular";
    font-size: 0.8em;
    padding: 1em 0 1rem;
    color: #b0b0b0;
  }
  .wechat-modal-footer::before,
  .wechat-modal-footer::after {
    content: " - ";
  }
  
  .ak-modal {
    position: fixed;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    transition: opacity 0.3s, visibility 0.3s, transform 0.3s;
    opacity: 0;
    visibility: hidden;
    transform: scale(1.2);
    z-index: 999;
    font-family: sans-serif;
  }
  .ak-modal[active="true"] {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }
  .ak-modal .modal-mask {
    background-color: black;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
  }
  .ak-modal .modal-layer {
    background: #ffffff;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .ak-modal .modal-layer .close-btn {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(200%);
  }
  .ak-modal .modal-layer .close-btn:hover::before,
  .ak-modal .modal-layer .close-btn:hover::after {
    background-color: #22bbff;
  }
  .ak-modal .modal-layer .close-btn::before,
  .ak-modal .modal-layer .close-btn::after {
    content: "";
    display: block;
    width: 2.4em;
    height: 0.6em;
    transition: background-color 0.3s;
    background-color: #fff;
  }
  .ak-modal .modal-layer .close-btn::before {
    transform: translateY(50%) rotate(45deg);
  }
  .ak-modal .modal-layer .close-btn::after {
    transform: translateY(-50%) rotate(-45deg);
  }
  
  .cs-modal-header {
    text-align: left;
    font-family: "Geometos";
    font-weight: bold;
    font-size: 1.2em;
    box-sizing: border-box;
    display: flex;
    align-items: flex-end;
  }
  .cs-modal-header img {
    width: 2.6em;
    height: 2.6em;
    margin-right: 0.5em;
  }
  .cs-modal-body {
    text-align: left;
    padding: 2rem;
    font-family: "SansMedium";
  }
  .cs-modal-body .cs-method {
    margin-top: 1em;
  }
  .cs-modal-body .cs-method .cs-label {
    display: block;
    margin-bottom: 0.8em;
  }
  .cs-modal-body .cs-method .cs-link {
    font-size: 1.2em;
    display: block;
    padding: 0.5em 1em;
    border-radius: 0.5em;
    border: solid #22bbff 1px;
    background-color: #22bbff;
    color: white;
    text-decoration: none !important;
    transition: color 0.3s, background-color 0.3s;
  }
  .cs-modal-body .cs-method .cs-link:hover {
    background-color: transparent;
    color: #22bbff;
  }
  
  .ako-cursor-container {
    position: absolute;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s;
  }
  .ako-cursor-container .ako-cursor-inner,
  .ako-cursor-container .ako-cursor-outer,
  .ako-cursor-container .ako-cursor-effect {
    position: fixed;
    border-radius: 50%;
  }
  .ako-cursor-container .ako-cursor-outer {
    z-index: 999999;
    width: 36px;
    height: 36px;
    border: 1px solid #cccccc;
    transition: background-color 0.3s, width 0.3s, height 0.3s;
  }
  .ako-cursor-container .ako-cursor-effect {
    opacity: 0;
    z-index: 999998;
    width: 80px;
    height: 80px;
    border: 4px solid #cccccc;
  }
  
  #nav {
    z-index: 20;
    top: 0;
  }
  #nav .navBtn {
    display: block;
    position: absolute;
    top: 1.5rem;
    left: 1.5rem;
    padding: 0.5rem;
    font-size: 2rem;
    z-index: 10;
  }
  #nav .navBtnIcon {
    z-index: 10;
    width: 1em;
    height: 1em;
    display: block;
    position: relative;
  }
  #nav .navBtnIconBar {
    position: absolute;
    top: 50%;
    width: 1em;
    height: 0.1em;
    background-color: #d4d8dd;
    text-indent: 101%;
    white-space: nowrap;
    overflow: hidden;
    transition: transform ease-in-out 0.3s;
  }
  #nav .navBtnIconBar:nth-child(1) {
    left: 0;
    transform: translate(0, -350%);
  }
  #nav .navBtnIconBar:nth-child(2) {
    left: 0;
    transform: translate(0, -50%);
  }
  #nav .navBtnIconBar:nth-child(3) {
    right: 0;
    transform: translate(0, 250%);
  }
  #nav .navMenu {
    padding: 0;
    margin: 0;
    font-family: "SansRegular";
    font-size: 1rem;
  }
  #nav .navMenu a {
    text-decoration: none;
    color: #fff;
  }
  #nav .navMenuItem .navMenuItemName {
    font-family: "Geometos";
    white-space: nowrap;
  }
  #nav .navMenuItem span {
    display: block;
    position: relative;
    text-align: center;
  }
  #nav.expanded .navBtnIconBar:nth-child(1) {
    transform: translate(0, -50%) rotateZ(45deg) scaleX(0.5) translate(-50%);
  }
  #nav.expanded .navBtnIconBar:nth-child(3) {
    transform: translate(0, -50%) rotateZ(45deg) scaleX(0.5) translate(50%);
  }
  #nav.expanded .navBtnIconBar:nth-child(2) {
    transform: translate(0, -50%) rotateZ(-45deg);
  }
  
  @media only screen and (min-width: 429px) {
    #nav {
      right: 5.25rem;
      height: 100%;
      position: absolute;
    }
    #nav .navContent {
      position: relative;
      top: 50%;
      transform: translateY(-50%);
    }
    #nav .navMenu {
      width: 4rem;
    }
    #nav .navMenuItem.inMenu {
      display: none;
    }
    #nav .navMenuItem.active .navMenuItemTitle {
      color: #22bbff;
    }
    #nav .navMenuItem.active .navMenuItemTitle::after {
      content: "";
      display: block;
      width: 0px;
      height: 100%;
      border-left: 4px solid #22bbff;
      top: 0;
      right: 0;
      position: absolute;
    }
    #nav .navMenuItem:hover .navMenuItemTitle {
      color: #22bbff;
      margin-left: -1em;
    }
    #nav .navMenuItem a {
      padding: 2.8rem 0;
      display: block;
      pointer-events: none;
    }
    #nav .navMenuItem .navMenuItemName {
      display: none;
    }
    #nav .navMenuItem .navMenuItemTitle {
      width: 100%;
      text-align: center;
      transition: margin 0.3s, color 0.3s;
    }
    #nav .navBtn,
    #nav .navMenuDownload,
    #nav .navMenuMedia {
      display: none;
    }
    #nav .navPagination {
      position: absolute;
      bottom: 3.5rem;
      color: #7f8184;
      right: 0;
      font-family: "Bender";
      white-space: nowrap;
    }
    #nav .navPagination > span {
      display: inline-block;
      text-align: center;
      font-size: 1rem;
      letter-spacing: 0.25em;
      width: 2rem;
    }
    #nav .navPagination > span.navPaginationCurrent {
      text-align: right;
      width: auto;
    }
    #nav .navPagination > span.navPaginationTotal {
      text-align: right;
      width: 1.5rem;
    }
  }
  
  @media only screen and (max-width: 428px) {
    #nav {
      position: fixed;
      top: 0;
      left: 0;
    }
    #nav .navContent {
      position: fixed;
      top: 0;
      left: 0;
      width: calc(100% - 5rem);
      height: calc(100% - 6.5rem);
      transform: translateX(-100%);
      transition: transform 0.6s;
      background-color: rgba(0, 0, 0, 0.88);
      padding: 4.5rem 2.5rem 2rem;
      display: flex;
      flex-direction: column;
    }
    #nav .navMenu {
      width: 100%;
      display: flex;
      flex-direction: column;
      padding-right: 1rem;
      box-sizing: border-box;
      height: calc(100% - 12rem);
      position: relative;
    }
    #nav .navMenuItem {
      width: 100%;
      flex-shrink: 1;
      padding-bottom: 0.5rem;
    }
    #nav .navMenuItem:first-of-type {
      flex-grow: 0.5;
    }
    #nav .navMenuItem:not(:first-of-type) {
      flex-grow: 1;
    }
    #nav .navMenuItem a {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      padding-top: 0;
      padding-left: 2rem;
      padding-bottom: 6px;
      border-bottom: 1px solid rgba(57, 57, 57, 0.5);
      height: 100%;
      pointer-events: none;
    }
    #nav .navMenuItem .navMenuItemTitle {
      padding-left: 5px;
    }
    #nav .navMenuItem .navMenuItemTitle::after {
      content: "";
      display: block;
      width: 100%;
      height: 0;
      border-bottom: 2px solid #ffffff;
      bottom: -7px;
      right: 0;
      position: absolute;
    }
    #nav .navMenuItem.active .navMenuItemTitle::after {
      border-bottom-color: #22bbff;
    }
    #nav .navMenuDownload {
      height: 4.8rem;
      margin-top: 2rem;
    }
    #nav .navMenuMedia {
      height: 2.5rem;
      margin-top: 2rem;
      fill: #ffffff;
      display: flex;
      justify-content: space-between;
    }
    #nav .navMenuMedia .navMenuButton {
      width: 2.5rem;
    }
    #nav .navMenuMedia .navMenuButton:visited {
      color: unset;
      fill: unset;
    }
    #nav .navPagination {
      display: none;
    }
    #nav.expanded .navContent {
      transform: translateX(0%);
    }
  }
  
  .anime-text {
    position: relative;
  }
  .anime-text .anime-text-wrapper {
    display: block;
  }
  .anime-text .anime-text-wrapper span {
    display: inline-block;
  }
  .anime-text.zoom-out {
    height: 1em;
  }
  .anime-text.zoom-out .anime-text-wrapper {
    white-space: nowrap;
    position: absolute;
    left: 0;
    top: 0;
  }
  .anime-text.zoom-out .anime-text-wrapper span {
    position: relative;
    display: inline-block;
  }
  .anime-text.zoom-out .anime-text-wrapper.next span {
    opacity: 0;
  }
  .anime-text.fade-left {
    height: 1em;
  }
  .anime-text.fade-left .anime-text-wrapper {
    white-space: nowrap;
    left: 0;
    top: 0;
  }
  .anime-text.fade-left .anime-text-wrapper span {
    position: relative;
    display: inline-block;
  }
  .anime-text.fade-left .anime-text-wrapper.next {
    position: absolute;
    opacity: 0;
  }
  .anime-text.fade-down .anime-text-wrapper.next {
    opacity: 0;
  }
  .anime-text.fade-vertical .anime-text-wrapper.next {
    opacity: 0;
    display: none;
  }
  
  @media only screen and (min-width: 429px) {
    .downloadBtnWrapper {
      display: inline-block;
      flex-direction: column;
      padding: 0.5rem;
      width: 9.375rem;
      height: 9.375rem;
      border-radius: 0.25rem;
      background-color: rgba(34, 187, 255, 0);
      perspective: 500px;
    }
    .downloadBtnWrapper .downloadBtnContent {
      width: 100%;
      height: 100%;
      display: block;
      position: relative;
    }
    .downloadBtnWrapper .downloadBtn {
      background-image: url(https://web.hycdn.cn/arknights/official/static/rhodes.233a893d934d381666aa.png);
      width: 100%;
      height: 100%;
      display: block;
      background-size: 100% auto;
      background-repeat: no-repeat;
      display: flex;
      align-items: flex-end;
    }
    .downloadBtnWrapper .downloadBtnText {
      font-size: 0.7rem;
      flex-grow: 1;
      font-family: "SansMedium";
      color: #e3e3e3;
      transition: color 0.3s;
      display: flex;
    }
    .downloadBtnWrapper .downloadBtnText span {
      display: block;
    }
    .downloadBtnWrapper .downloadBtnText:first-of-type span:first-of-type {
      flex-grow: 2;
    }
    .downloadBtnWrapper .downloadBtnText:first-of-type span:last-of-type {
      flex-grow: 1;
    }
    .downloadBtnWrapper .downloadBtnText:last-of-type span {
      text-align: right;
    }
    .downloadBtnWrapper .downloadBtnText:last-of-type span:first-of-type {
      flex-grow: 1;
    }
    .downloadBtnWrapper .downloadBtnText:last-of-type span:last-of-type {
      flex-grow: 2;
    }
    .downloadBtnWrapper .downloadBtnDetail {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: none;
    }
    .downloadBtnWrapper .downloadBtnDetail .downloadBtnDetailQrCode {
      background-image: url(https://web.hycdn.cn/arknights/official/static/qr.c7ceabc360c69e5bb584.png);
      width: 100%;
      height: 100%;
      background-size: 100% auto;
      background-repeat: no-repeat;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      text-decoration: none;
    }
    .downloadBtnWrapper .downloadBtnDetail .downloadBtnDetailQrCode span {
      font-size: 0.7rem;
      flex-grow: 1;
      font-family: "SansMedium";
      color: #e3e3e3;
      width: 100%;
      text-align: justify;
      text-align-last: justify;
      display: block;
      height: 1rem;
    }
    .downloadBtnWrapper .downloadBtnDetail .downloadBtnDetailQrCode span:after {
      content: "";
      width: 100%;
      height: 0;
      display: inline-block;
      visibility: hidden;
    }
    .downloadBtnWrapper .downloadBtnDetail .downloadBtnDetailLinks {
      position: absolute;
      height: 100%;
      left: 12rem;
      top: 0;
    }
    .downloadBtnWrapper .downloadBtnDetail .downloadBtnDetailLink {
      width: 9.5rem;
      height: 2.75rem;
      border-radius: 6px;
      display: block;
      opacity: 0;
      transition: border-color 300ms;
    }
    .downloadBtnWrapper .downloadBtnDetail .downloadBtnDetailLink:not(:first-of-type) {
      margin-top: 0.5rem;
    }
    .downloadBtnWrapper .downloadBtnDetail .downloadBtnDetailLink svg {
      display: block;
      width: 100%;
      height: 100%;
    }
    .downloadBtnWrapper .downloadBtnDetail .downloadBtnDetailLink svg text {
      fill: #fff;
      font-family: "SansMedium";
    }
    .downloadBtnWrapper .downloadBtnDetail .downloadBtnDetailLink.ios {
      background-color: #000;
      border: 1px solid #333;
    }
    .downloadBtnWrapper .downloadBtnDetail .downloadBtnDetailLink.android {
      background-color: #8fc31f;
      border: 1px solid #8fc31f;
    }
    .downloadBtnWrapper .downloadBtnDetail .downloadBtnDetailLink.taptap {
      background-color: #009baa;
      border: 1px solid #009baa;
    }
    .downloadBtnWrapper .downloadBtnDetail .downloadBtnDetailLink:hover {
      border-color: #fff;
    }
  }
  @media only screen and (max-width: 428px) {
    .downloadBtnWrapper * {
      pointer-events: none;
    }
    .downloadBtnWrapper .downloadBtnContent {
      height: 100%;
      padding: 0 1rem;
      background-image: url(https://web.hycdn.cn/arknights/official/static/rhodes.233a893d934d381666aa.png);
      background-position: center;
      background-size: auto 100%;
      background-repeat: no-repeat;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .downloadBtnWrapper .downloadBtn {
      width: 100%;
      height: 4rem;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
    }
    .downloadBtnWrapper .downloadBtn::before,
    .downloadBtnWrapper .downloadBtn::after {
      content: "";
      display: block;
      position: absolute;
      height: calc(100% - 4px);
      width: 3px;
      top: -1px;
      border-top: 3px solid #fff;
      border-bottom: 3px solid #fff;
      z-index: 10;
    }
    .downloadBtnWrapper .downloadBtn::before {
      left: -1px;
    }
    .downloadBtnWrapper .downloadBtn::after {
      right: -1px;
    }
    .downloadBtnWrapper .downloadBtnText {
      height: 100%;
      width: 50%;
      color: #ffffff;
      display: flex;
      align-items: center;
      position: relative;
      font-family: "SansRegular";
    }
    .downloadBtnWrapper .downloadBtnText::before,
    .downloadBtnWrapper .downloadBtnText::after {
      content: "";
      display: block;
      position: absolute;
      height: 1px;
      background-image: linear-gradient(to right, #bcbcbc 0%, transparent 100%);
      background-size: 3px 1px;
    }
    .downloadBtnWrapper .downloadBtnText::before {
      top: 0;
      width: calc(100% - 1.5rem);
    }
    .downloadBtnWrapper .downloadBtnText::after {
      bottom: 0;
      width: calc(100% - 3.5rem);
    }
    .downloadBtnWrapper .downloadBtnText:first-of-type {
      padding-right: 1rem;
      justify-content: flex-end;
    }
    .downloadBtnWrapper .downloadBtnText:first-of-type::before,
    .downloadBtnWrapper .downloadBtnText:first-of-type::after {
      left: 0;
    }
    .downloadBtnWrapper .downloadBtnText:first-of-type span {
      margin-right: 2rem;
    }
    .downloadBtnWrapper .downloadBtnText:last-of-type {
      padding-left: 1rem;
      justify-content: flex-start;
    }
    .downloadBtnWrapper .downloadBtnText:last-of-type::before,
    .downloadBtnWrapper .downloadBtnText:last-of-type::after {
      right: 0;
    }
    .downloadBtnWrapper .downloadBtnText:last-of-type span {
      margin-left: 2rem;
    }
    .downloadBtnWrapper .downloadBtnDetail {
      display: none;
    }
  }
  .downloadBtnWrapper .downloadBtn {
    text-decoration: none;
  }
  
  @keyframes KF_ScrollTips_Anime {
    0% {
      border-left-color: #ababab;
      border-bottom-color: #ababab;
    }
    30% {
      border-left-color: #fff;
      border-bottom-color: #fff;
    }
    60% {
      border-left-color: #ababab;
      border-bottom-color: #ababab;
    }
  }
  .scrollTips {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    display: none;
    opacity: 0;
  }
  .scrollTips .scrollTipsArrow {
    border-style: solid;
    border-left-color: #ababab;
    border-bottom-color: #ababab;
    border-top: none;
    border-right: none;
    transform: translateX(-50%) rotate(-45deg);
    position: absolute;
    left: 50%;
  }
  .scrollTips.appear {
    display: block;
  }
  .scrollTips.appear .scrollTipsArrow:first-of-type {
    animation: KF_ScrollTips_Anime 2s infinite;
  }
  .scrollTips.appear .scrollTipsArrow:last-of-type {
    animation: KF_ScrollTips_Anime 2s 0.2s infinite;
  }
  
  @media only screen and (min-width: 429px) {
    .scrollTips {
      height: 3rem;
      bottom: 1rem;
    }
    .scrollTips .scrollTipsText {
      font-family: "Geometos";
      color: #fff;
      font-size: 1rem;
    }
    .scrollTips span.scrollTipsArrow {
      width: 0.35rem;
      height: 0.35rem;
      border-width: 2px;
    }
    .scrollTips span.scrollTipsArrow:first-of-type {
      bottom: 1rem;
    }
    .scrollTips span.scrollTipsArrow:last-of-type {
      bottom: 0.5rem;
    }
  }
  
  @media only screen and (max-width: 428px) {
    .scrollTips {
      height: 2rem;
      bottom: 0.5rem;
    }
    .scrollTips .scrollTipsText {
      display: none;
    }
    .scrollTips span.scrollTipsArrow {
      width: 1rem;
      height: 1rem;
      border-width: 1px;
    }
    .scrollTips span.scrollTipsArrow:first-of-type {
      bottom: 1.1rem;
    }
    .scrollTips span.scrollTipsArrow:last-of-type {
      bottom: 0.5rem;
    }
  }
  
  #section-homepage {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
  }
  #section-homepage::before {
    content: " ";
    display: block;
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    background-image: url(https://web.hycdn.cn/arknights/official/static/amiya.4cd2471ab732f5954f66.png);
    background-repeat: no-repeat;
    background-position: 0 center;
    background-size: auto 100%;
    opacity: 0;
    visibility: hidden;
  }
  .fadeIn#section-homepage::before {
    opacity: 0.1;
    visibility: visible;
    transition: opacity 1s 0.6s, null;
  }
  #section-homepage .homepageDisplay {
    position: relative;
    display: block;
  }
  #section-homepage .homepageTitleGrid {
    font-family: "Geometos";
    position: absolute;
    color: #fff;
    opacity: 0;
    visibility: hidden;
  }
  .fadeIn#section-homepage .homepageTitleGrid {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s 1s, null;
  }
  #section-homepage .homepageTitleGrid > span {
    display: block;
  }
  #section-homepage .homepageTitleGrid > span:not(:last-of-type) {
    margin-bottom: 4.74em;
  }
  #section-homepage .homepageTitleGrid > span > span {
    display: inline-block;
    width: 1em;
  }
  #section-homepage .homepageTitleGrid > span > span:not(:first-of-type) {
    margin-left: 4.74em;
  }
  #section-homepage .homepageTitle {
    overflow: hidden;
    display: inline-flex;
    width: 100%;
    height: 100%;
  }
  #section-homepage .homepageTitle svg {
    display: block;
    opacity: 0.2;
    width: 100%;
    transform: translateY(101%);
  }
  #section-homepage .homepageTitle svg path {
    fill: #e3e3e3;
  }
  #section-homepage .homepagePVWrapper {
    position: absolute;
    overflow: hidden;
    top: 100%;
    width: 100%;
  }
  #section-homepage .homepagePVContainer {
    transform: translateY(-105%);
    display: flex;
    justify-content: flex-start;
  }
  #section-homepage .homepagePV {
    display: flex;
    align-items: flex-start;
    height: 2.5rem;
  }
  #section-homepage .homepagePVList {
    display: inline-flex;
    margin: 0;
    padding: 0;
    list-style: none;
  }
  #section-homepage .homepagePVListItem {
    display: inline-block;
    font-family: "Helvetica";
    letter-spacing: -0.06em;
    position: relative;
    color: #66686b;
    transition: color 300ms;
  }
  #section-homepage .homepagePVListItem::after {
    content: "";
    display: block;
    width: 0%;
    border-bottom: 2px solid #06a3da;
    transition: width 300ms;
  }
  #section-homepage .homepagePVListItem.active {
    color: #e3e3e3;
  }
  #section-homepage .homepagePVListItem.active::after {
    width: 100%;
  }
  #section-homepage .homepagePVListItem.disable {
    color: #444444;
    cursor: not-allowed;
    pointer-events: none;
  }
  #section-homepage .homepagePVInfo {
    display: inline-flex;
    color: #e3e3e3;
    font-family: "Geometos";
    flex-direction: column;
    font-size: 1rem;
    transform-origin: top left;
  }
  #section-homepage .homepagePVInfoBgm {
    white-space: nowrap;
  }
  #section-homepage .homepagePVButton svg {
    transition: stroke;
  }
  @keyframes KF_PV_Button_Active {
    0% {
      stroke-width: 1;
    }
    40% {
      stroke-width: 5;
    }
    60% {
      stroke-width: 5;
    }
    100% {
      stroke-width: 1;
    }
  }
  #section-homepage .homepagePVButton.active {
    animation: KF_PV_Button_Active 300ms forwards;
  }
  #section-homepage .homepageLogo {
    position: absolute;
    opacity: 0;
    visibility: hidden;
  }
  .fadeIn#section-homepage .homepageLogo {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s 0s, null;
  }
  #section-homepage .homePuzzle {
    font-family: "SerifRegular";
    position: absolute;
    opacity: 0;
    display: none;
  }
  #section-homepage .mainDownload {
    position: absolute;
    opacity: 0;
    visibility: hidden;
  }
  .fadeIn#section-homepage .mainDownload {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s 0s, background-color 0.3s;
  }
  
  @media only screen and (min-width: 429px) {
    #section-homepage {
      padding-left: 5.875rem;
    }
    #section-homepage::before {
      left: 5.875rem;
    }
    #section-homepage .homepageDisplay {
      width: 40.75rem;
      height: 5.75rem;
    }
    #section-homepage .homepageTitleGrid {
      font-size: 0.65875rem;
      bottom: 18vh;
    }
    #section-homepage .homepagePVWrapper {
      margin-top: 2rem;
      margin-left: -3px;
    }
    #section-homepage .homepagePVContainer {
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-end;
    }
    #section-homepage .homepagePVListItem {
      font-size: 1.5rem;
      margin-right: 2.125rem;
    }
    #section-homepage .homepagePVInfo {
      transform: scale(0.625);
    }
    #section-homepage .homepagePVButton a {
      width: 3rem;
      height: 3rem;
      display: block;
      transition: transform 300ms;
    }
    #section-homepage .homepagePVButton a:hover svg use:first-of-type {
      transform: rotate(180deg);
      stroke-width: 2;
    }
    #section-homepage .homepagePVButton a:hover svg use:last-of-type {
      transform: scale(1.2);
      stroke-width: 2;
    }
    #section-homepage .homepagePVButton span {
      display: none;
    }
    #section-homepage .homepagePVButton svg {
      display: block;
      width: 100%;
      height: 100%;
    }
    #section-homepage .homepagePVButton svg use {
      transition: transform 300ms cubic-bezier(0.18, 0.89, 0.43, 1.22), stroke-width 300ms;
    }
    #section-homepage .homepageLogo {
      left: 4.5rem;
      top: 3.75rem;
      width: 11.125rem;
    }
    #section-homepage .homePuzzle {
      width: 24rem;
    }
    #section-homepage .homePuzzle.first {
      left: 40%;
      top: 50%;
    }
    #section-homepage .homePuzzle.second {
      left: 5%;
      top: 70%;
    }
    #section-homepage .homePuzzle.third {
      right: 3%;
      top: 20%;
    }
    #section-homepage .mainDownload {
      bottom: 2.5rem;
    }
  }
  
  @media only screen and (max-width: 428px) {
    #section-homepage .homepageLogo {
      position: absolute;
      left: 1.5rem;
      top: 6rem;
      width: 8.5rem;
    }
    #section-homepage .homepageDisplay {
      left: 2rem;
      width: 18rem;
      height: 2.54rem;
    }
    #section-homepage .homepageTitleGrid {
      bottom: 6.5rem;
      font-size: 0.5rem;
    }
    #section-homepage .homepagePVWrapper {
      margin-top: 1rem;
    }
    #section-homepage .homepagePVContainer {
      flex-direction: column;
    }
    #section-homepage .homepagePVList {
      font-size: 1.48rem;
    }
    #section-homepage .homepagePVListItem {
      margin-right: 1.333rem;
      line-height: 1.5rem;
    }
    #section-homepage .homepagePVInfo {
      font-size: 1rem;
      transform: scale(0.555);
      line-height: 1.5rem;
    }
    #section-homepage .homepagePVButton {
      font-size: 1rem;
      width: 5.16em;
      height: 2.44em;
      position: relative;
    }
    #section-homepage .homepagePVButton::before,
    #section-homepage .homepagePVButton::after {
      content: "";
      display: block;
      position: absolute;
      height: calc(100% - 6px);
      width: 3px;
      top: 0px;
      border-top: 3px solid #fff;
      border-bottom: 3px solid #fff;
      z-index: 10;
    }
    #section-homepage .homepagePVButton::before {
      left: 0px;
    }
    #section-homepage .homepagePVButton::after {
      right: 0px;
    }
    #section-homepage .homepagePVButton a {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
    }
    #section-homepage .homepagePVButton a::before,
    #section-homepage .homepagePVButton a::after {
      content: "";
      display: block;
      position: absolute;
      width: 100%;
      height: 1px;
      background-image: linear-gradient(to right, #bcbcbc 0%, transparent 50%);
      background-size: 3px 1px;
    }
    #section-homepage .homepagePVButton a::before {
      top: 1px;
    }
    #section-homepage .homepagePVButton a::after {
      bottom: 1px;
    }
    #section-homepage .homepagePVButton a span {
      font-family: "Helvetica";
      font-size: 1.2em;
      color: #fff;
      line-height: 1;
    }
    #section-homepage .homepagePVButton a svg {
      width: 1.5em;
      height: 1.5em;
    }
    #section-homepage .homePuzzle {
      width: 12rem;
    }
    #section-homepage .homePuzzle.first {
      left: 2%;
      top: 45%;
    }
    #section-homepage .homePuzzle.second {
      left: 50%;
      top: 70%;
    }
    #section-homepage .homePuzzle.third {
      right: -5%;
      top: 20%;
    }
    #section-homepage .mainDownload {
      left: 50%;
      transform: translateX(-50%);
      position: absolute;
      padding: 0;
      bottom: 3.8rem;
      width: 21rem;
      height: 4.8rem;
    }
  }
  
  .video-player-wrapper {
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 1000;
    display: none;
    top: 0;
    left: 0;
  }
  .video-player-wrapper.show {
    display: block;
  }
  .video-player-wrapper .video-player {
    width: 100%;
    height: 100%;
  }
  .video-player-wrapper .video-player-cover {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
  }
  .video-player-wrapper .video-player-content {
    position: absolute;
    top: 50%;
    left: 50%;
    width: auto;
    transform: translate(-50%, -50%);
  }
  .video-player-wrapper .video-player-video {
    width: 100%;
  }
  @media only screen and (max-width: 428px) {
    .video-player-wrapper .video-player-content {
      width: 90%;
    }
  }
  
  section.section {
    overflow: hidden;
  }
  section.section .sectionDisplay {
    display: flex;
    flex-direction: column;
    height: 100%;
    box-sizing: border-box;
  }
  
  @media only screen and (min-width: 429px) {
    section.section .sectionDisplay {
      padding: 3.5rem 0;
    }
    section.section .sectionHeader {
      display: none;
    }
  }
  
  @media only screen and (max-width: 428px) {
    section.section .sectionDisplay {
      padding-top: 5.8rem;
      padding-bottom: 3.8rem;
    }
    section.section .sectionHeader {
      display: none;
    }
  }
  
  
  @media only screen and (min-width: 429px) {
    #section-information .informationCategory {
      width: 54.25rem;
      margin-left: 5.875rem;
    }
    #section-information .informationBannerWrapper {
      margin-top: 2.625rem;
      margin-left: 5.875rem;
      width: 54.25rem;
    }
    #section-information .informationArticleContainer {
      margin-top: 1rem;
      width: 60.125rem;
      flex-grow: 1;
      flex-shrink: 1;
      display: flex;
      flex-direction: column;
    }
    #section-information .informationArticleContainer .articleItemLink {
      margin-left: 5.875rem;
    }
    #section-information .informationArticleContainer .articleFooter {
      margin: 2.75rem auto 0 5.875rem;
      width: 54.25rem;
    }
  }
  
  @media only screen and (max-width: 428px) {
    #section-information .informationBannerWrapper {
      order: 1;
      margin: 0 2rem;
    }
    #section-information .informationCategory {
      order: 2;
      margin: 0.8rem 1rem 1.833rem;
      padding: 0 1rem;
    }
    #section-information .informationArticleContainer {
      order: 3;
      margin: 0 1rem;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    #section-information .informationArticleContainer .articleItem {
      padding: 0 1rem;
    }
  }
  
  .banner {
    width: 100%;
    overflow: hidden;
    position: relative;
  }
  .banner::before {
    content: "";
    display: block;
    width: 100%;
    height: 0;
  }
  .banner .bannerList {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    white-space: nowrap;
    height: 100%;
  }
  .banner .bannerItem {
    display: block;
    flex-shrink: 0;
    flex-grow: 0;
    width: 100%;
    height: 100%;
  }
  .banner .bannerItemImage {
    display: block;
    width: 100%;
    height: 100%;
  }
  
  @media only screen and (min-width: 429px) {
    .banner {
      box-shadow: inset 0 -20px 15px -5px #000;
    }
    .banner::before {
      padding-top: 25.80645161%;
    }
    .banner .bannerPagination {
      position: absolute;
      bottom: 1.1875rem;
      right: 0.625rem;
      display: flex;
    }
    .banner .bannerPagination .bannerPaginationBulletin {
      display: block;
      width: 3.25rem;
      height: 0.25rem;
      background-color: #fff;
      transition: background-color 0.3s;
    }
    .banner .bannerPagination .bannerPaginationBulletin.active {
      background-color: #22bbff;
    }
    .banner .bannerPagination .bannerPaginationBulletin:not(:last-of-type) {
      margin-right: 0.625rem;
    }
  }
  
  @media only screen and (max-width: 428px) {
    .banner::before {
      padding-top: 58.69797225%;
    }
    .banner .bannerList {
      -webkit-mask-image: linear-gradient(to bottom, #fff 70%, transparent 98%);
      mask-image: linear-gradient(to bottom, #fff 70%, transparent 98%);
    }
    .banner .bannerPagination {
      position: absolute;
      bottom: 1.16rem;
      right: 0.83rem;
      display: flex;
    }
    .banner .bannerPagination .bannerPaginationBulletin {
      display: block;
      width: 2.14rem;
      height: 0.12rem;
      opacity: 0.5;
      background-color: #fff;
      transition: opacity 0.3s;
    }
    .banner .bannerPagination .bannerPaginationBulletin.active {
      opacity: 1;
    }
    .banner .bannerPagination .bannerPaginationBulletin:not(:last-of-type) {
      margin-right: 0.12rem;
    }
  }
  
  .articleContainer .articleList {
    display: none;
  }
  .articleContainer .articleList.active {
    display: flex;
  }
  .articleContainer .articleList .articleItem {
    display: none;
  }
  .articleContainer .articleList .articleItem.display {
    display: block;
  }
  .articleFooter .articlePaginationList {
    display: flex;
    height: 100%;
    align-items: flex-end;
    padding: 0;
    margin: 0;
    justify-content: center;
  }
  .articleFooter .articlePaginationList li {
    position: relative;
    font-family: "Bender";
    color: #838588;
    width: 1.875rem;
    height: 1.875rem;
    display: block;
    transition: background-color 0.15s, color 0.15s;
  }
  .articleFooter .articlePaginationList li::before,
  .articleFooter .articlePaginationList li::after {
    content: "";
    display: block;
    position: absolute;
    width: 4px;
    height: 4px;
    background-size: 100%;
  }
  .articleFooter .articlePaginationList li::before {
    top: 0;
    left: 0;
  }
  .articleFooter .articlePaginationList li::after {
    bottom: 0;
    right: 0;
  }
  .articleFooter .articlePaginationList li:not(.disabled):hover {
    background-color: #22bbff;
    color: #fff;
  }
  .articleFooter .articlePaginationList li.disabled {
    color: #4d4d4d;
  }
  .articleFooter .articlePaginationList li.active {
    color: #fff;
  }
  .articleFooter .articlePaginationList li.articlePaginationNavBtn {
    width: 3.875rem;
  }
  .articleFooter .articlePaginationList li a {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
  }
  .articleFooter .articleMore {
    display: flex;
    align-items: center;
    margin-left: 1rem;
  }
  .articleFooter .articleMoreButton {
    color: #ababab;
    white-space: nowrap;
    display: flex;
    font-family: "SansMedium";
    position: relative;
    height: 100%;
    text-decoration: none;
    align-items: center;
    transition: color 0.15s;
  }
  .articleFooter .articleMoreButton::before,
  .articleFooter .articleMoreButton::after {
    content: "";
    display: block;
    position: absolute;
    width: 100%;
    height: 20%;
    border: 1px solid #ababab;
    transition: border-color 0.15s, height 0.15s;
  }
  .articleFooter .articleMoreButton::before {
    top: 0;
    left: 0;
    border-bottom: none;
  }
  .articleFooter .articleMoreButton::after {
    bottom: 0;
    left: 0;
    border-top: none;
  }
  .articleFooter .articleMoreButton svg {
    width: 4rem;
    stroke: #ababab;
    transition: stroke 0.15s;
  }
  .articleFooter .articleMoreButton:hover {
    color: #22bbff;
  }
  .articleFooter .articleMoreButton:hover svg {
    stroke: #22bbff;
  }
  .articleFooter .articleMoreButton:hover::before,
  .articleFooter .articleMoreButton:hover::after {
    border-color: #22bbff;
    height: 50%;
  }
  
  @media only screen and (min-width: 429px) {
    .articleCategory {
      display: flex;
      justify-content: flex-end;
      height: 2.25rem;
      font-size: 1.25rem;
      font-family: "SansMedium";
    }
    .articleCategory .articleCategoryItem {
      color: #fff;
      display: inline-block;
      padding: 0 0.5rem;
      margin: 0 0.75rem;
      position: relative;
      transition: color 0.3s, background-color 0.3s;
    }
    .articleCategory .articleCategoryItem::before {
      content: "路";
      display: block;
      position: absolute;
      color: #fff;
      left: -0.75rem;
      transform: translateX(-50%);
    }
    .articleCategory .articleCategoryItem:last-of-type::after {
      content: "路";
      display: block;
      position: absolute;
      color: #fff;
      right: -0.75rem;
      top: 0;
      transform: translateX(50%);
    }
    .articleCategory .articleCategoryItem.active {
      color: #22bbff;
    }
    .articleCategory .articleCategoryItem:hover {
      color: #88daff;
    }
    .articleListWrapper {
      flex-grow: 1;
      display: block;
      position: relative;
    }
    .articleList {
      position: absolute;
      width: 100%;
      height: 100%;
      flex-direction: column;
    }
    .articleItem {
      flex-basis: 16.666%;
      border-image: linear-gradient(to right, #464646 0%, #464646 60%, rgba(70, 70, 70, 0) 100%) 30 stretch;
      border-top-width: 0;
      border-left-width: 0;
      border-right-width: 0;
      border-bottom: 1px solid;
    }
    .articleItem.last {
      border-bottom-width: 0;
    }
    .articleItemLink {
      color: unset;
      height: 100%;
      display: flex;
      align-items: center;
      text-decoration: none;
    }
    .articleItemLink::before {
      content: "READ MORE +";
      display: inline-block;
      font-weight: bold;
      font-size: 0.675rem;
      text-align: right;
      color: #000;
      font-family: "Bender";
      background-color: #22bbff;
      margin-right: 0;
      padding: 5px 0px;
      transition: width 0.5s, margin-right 0.3s, padding 0.3s;
      white-space: nowrap;
      width: 0;
      box-sizing: content-box;
      overflow: hidden;
    }
    .articleItemLink:hover::before {
      width: 11em;
      margin-right: 2em;
      padding: 5px 7px;
    }
    .articleItemDate {
      font-family: "Bender";
      color: #fff;
      font-size: 0.875rem;
      width: 6em;
      text-align: center;
      white-space: nowrap;
    }
    .articleItemCate {
      font-family: "SansMedium";
      color: #22bbff;
      font-size: 1rem;
      margin-left: 2.25rem;
    }
    .articleItemTitle {
      font-family: sans-serif;
      color: #fff;
      font-size: 1rem;
      font-weight: normal;
      margin: 0;
      margin-left: 0.75rem;
    }
    .articleFooter {
      height: 2rem;
      display: flex;
      justify-content: space-between;
    }
    .articlePaginationList li:not(:first-of-type) {
      margin-left: 1.5625rem;
    }
    .articleMoreButton {
      font-size: 1.1rem;
      padding: 0.4rem 0.8rem;
    }
  }
  
  @media only screen and (max-width: 428px) {
    .articleCategory {
      font-family: "SansBold";
      border-image: linear-gradient(to right, transparent 0%, #737373 10%, #737373 90%, transparent 100%) 30 stretch;
      border-top-width: 0;
      border-left-width: 0;
      border-right-width: 0;
      border-bottom: 1px solid;
    }
    .articleCategory .articleCategoryList {
      display: flex;
    }
    .articleCategory .articleCategoryItem {
      flex-grow: 1;
      height: 2rem;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      text-align: center;
      transition: background-color 300ms, color 300ms;
      clip-path: polygon(0 0.5rem, 0.5rem 0, 100% 0, 100% 100%, 0 100%);
    }
    .articleCategory .articleCategoryItem.active {
      background-color: #22bbff;
      background-image: linear-gradient(135deg, #174866 0%, #174866 12.5%, transparent 12.6%, transparent 100%);
      background-size: 4px 4px;
      background-position: 10% 10%;
      background-repeat: repeat repeat;
      color: #000;
    }
    .articleContainer .articleListWrapper {
      flex-shrink: 0;
      flex-grow: 1;
      position: relative;
      display: block;
    }
    .articleContainer .articleList {
      width: 100%;
      height: 100%;
      position: absolute;
      flex-direction: column;
      justify-content: flex-start;
    }
    .articleContainer .articleItem {
      flex-basis: 16.66666667%;
      border-image: linear-gradient(to right, transparent 0%, #737373 20%, #737373 80%, transparent 100%) 30 stretch;
      border-top-width: 0;
      border-left-width: 0;
      border-right-width: 0;
      border-bottom: 1px solid;
    }
    .articleContainer .articleItemLink {
      color: unset;
      height: 100%;
      display: flex;
      align-items: center;
      text-decoration: none;
      position: relative;
      overflow: hidden;
      min-height: 3rem;
    }
    .articleContainer .articleItemLink:hover::before {
      width: 11em;
      margin-right: 2em;
      padding: 5px 7px;
    }
    .articleContainer .articleItemDate {
      font-family: "Bender";
      color: #000;
      font-weight: bold;
      font-size: 0.6rem;
      width: 5em;
      padding: 0.1rem 0.5em;
      line-height: 1;
      background-color: #22bbff;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      white-space: nowrap;
      top: 0;
      left: 0;
    }
    .articleContainer .articleItemCate {
      font-family: "SansMedium";
      color: #22bbff;
      font-size: 0.833rem;
      white-space: nowrap;
      width: 3rem;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }
    .articleContainer .articleItemTitle {
      font-family: sans-serif;
      color: #fff;
      font-size: 0.833rem;
      font-weight: normal;
      margin: 0;
      white-space: nowrap;
    }
    .articleContainer .articleFooter {
      margin-top: 1.5rem;
      height: 2rem;
      display: flex;
      justify-content: space-between;
    }
    .articleContainer .articlePagination {
      flex-grow: 1;
    }
    .articleContainer .articlePaginationList li:not(:first-of-type) {
      margin-left: 1.5625rem;
    }
    .articleContainer .articleMoreButton {
      font-size: 0.833rem;
      padding: 0.2em 0.8em;
    }
  }
  
  #section-staff .staffCharDraw {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
  }
  #section-staff .staffItemIdx {
    font-family: "Bender";
  }
  #section-staff .staffDisplay {
    flex-grow: 1;
    display: flex;
    position: relative;
  }
  #section-staff .staffDetail.show .staffDetailContentWrapper::before {
    width: 100%;
    opacity: 1;
  }
  #section-staff .staffDetail.show .staffDetailContentWrapper::after {
    height: 100%;
    opacity: 1;
  }
  #section-staff .staffDetailImg {
    position: absolute;
    transform: translateX(-50%);
    left: 24%;
    bottom: 0;
    height: 60rem;
  }
  #section-staff .staffDetailImg img {
    display: block;
    height: 100%;
  }
  #section-staff .staffDetailContentWrapper {
    color: #fff;
    position: absolute;
  }
  #section-staff .staffDetailContentWrapper::before {
    content: "";
    display: block;
    position: absolute;
    background-image: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 20%, rgba(255, 255, 255, 0.5) 80%, rgba(255, 255, 255, 0) 100%);
    width: 0%;
    height: 1px;
    top: 47.4%;
    left: 0;
    opacity: 0.1;
    transition: width 0.3s 0.1s, opacity 0.3s 0.1s;
  }
  #section-staff .staffDetailContentWrapper::after {
    content: "";
    display: block;
    position: absolute;
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 20%, rgba(255, 255, 255, 0.5) 80%, rgba(255, 255, 255, 0) 100%);
    width: 1px;
    height: 0%;
    top: 0;
    left: 20%;
    opacity: 0.1;
    transition: height 0.3s, opacity 0.3s;
  }
  #section-staff .staffDetailContent {
    height: 100%;
    position: relative;
  }
  #section-staff .staffDetailInfo {
    width: 100%;
    position: absolute;
    box-sizing: border-box;
  }
  #section-staff .staffDetailInfoTop {
    margin-bottom: 1rem;
  }
  #section-staff .staffDetailInfoName {
    white-space: nowrap;
    line-height: 1;
  }
  #section-staff .staffDetailInfoCareer {
    fill: #fff;
    display: none;
  }
  #section-staff .staffDetailInfoBottom {
    display: flex;
    justify-content: space-between;
    height: 1.5rem;
    align-items: flex-end;
  }
  #section-staff .staffDetailInfoNameEn {
    font-family: "Geometos";
    line-height: 1;
  }
  #section-staff .staffDetailInfoCv {
    height: 100%;
    white-space: nowrap;
    display: flex;
    align-items: center;
  }
  #section-staff .staffDetailInfoCvButton {
    height: 100%;
    max-width: 2.74rem;
  }
  #section-staff .staffDetailInfoCvButton svg {
    height: 100%;
  }
  #section-staff .staffDetailInfoBack {
    z-index: -1;
    font-family: "Geometos";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
  #section-staff .staffDetailInfoBack svg {
    display: block;
  }
  #section-staff .staffDetailDesc {
    position: absolute;
    text-align: justify;
    white-space: pre-line;
  }
  
  @media only screen and (min-width: 429px) {
    #section-staff .staffDisplay {
      margin-left: 5.875rem;
      margin-right: 10rem;
      flex-grow: 1;
      display: flex;
      position: relative;
    }
    #section-staff .staffNavBtn {
      display: none;
    }
    #section-staff .staffDisplayLeft {
      margin-top: 5.625rem;
      min-width: 18rem;
      width: 30%;
      display: flex;
      flex-direction: column;
    }
    #section-staff .staffListContainer {
      flex-grow: 1;
      position: relative;
      overflow: hidden;
    }
    #section-staff .staffListWrapper {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
    }
    #section-staff .staffItem {
      color: #d5d6d9;
      fill: #d5d6d9;
      display: flex;
      align-items: center;
      transform-origin: 0 50%;
      transform: scale(1);
      transition: transform 0.3s, color 0.3s, fill 0.3s, margin 0.3s;
    }
    #section-staff .staffItem:hover:not(.active) {
      color: #fff;
      fill: #fff;
    }
    #section-staff .staffItem.active {
      color: #22bbff;
      fill: #22bbff;
      transform: scale(1.25);
    }
    #section-staff .staffItem span {
      pointer-events: none;
    }
    #section-staff .staffItemIdx {
      font-size: 1rem;
      transform: scale(0.7625);
    }
    #section-staff .staffItemCareer {
      margin-left: 1.75rem;
      width: 2rem;
      height: 2rem;
    }
    #section-staff .staffItemCareer svg {
      display: block;
    }
    #section-staff .staffItemName {
      margin-left: 1.2rem;
      font-size: 1.875rem;
      font-family: sans-serif;
    }
    #section-staff .staffFooter {
      margin-top: 5.625rem;
    }
    #section-staff .staffFooterCode {
      font-family: "Geometos";
      color: #7f8184;
    }
    #section-staff .staffDetail {
      height: 100%;
      flex-grow: 1;
    }
    #section-staff .staffDetailContentWrapper {
      bottom: 10%;
      right: 10%;
      width: 39.125rem;
      height: 23.75rem;
      padding-left: 7.7rem;
      padding-right: 3.6rem;
      box-sizing: border-box;
    }
    #section-staff .staffDetailInfo {
      bottom: 52.6%;
      padding-bottom: 1.75rem;
      padding-left: 1.5625rem;
    }
    #section-staff .staffDetailInfoBack {
      width: 27.8125rem;
      height: 2.875rem;
    }
    #section-staff .staffDetailInfoBack svg {
      width: 100%;
      height: 100%;
    }
    #section-staff .staffDetailInfoName {
      font-size: 4.375rem;
    }
    #section-staff .staffDetailInfoNameEn {
      font-size: 1.25rem;
    }
    #section-staff .staffDetailDesc {
      top: 47.4%;
      padding-top: 1.5625rem;
      padding-left: 1.5625rem;
      font-size: 0.875rem;
      line-height: 2.2em;
    }
  }
  
  @media only screen and (max-width: 428px) {
    #section-staff .sectionDisplay {
      padding-bottom: 0;
    }
    #section-staff .staffNavBtn {
      width: 3rem;
      height: 3rem;
      position: absolute;
      bottom: 50%;
      z-index: 1;
      padding: 0.5rem;
    }
    #section-staff .staffNavBtn.prev {
      left: 0;
    }
    #section-staff .staffNavBtn.next {
      right: 0;
    }
    #section-staff .staffDisplayLeft {
      padding: 0 3.7rem;
      width: 100%;
      box-sizing: border-box;
      position: absolute;
      bottom: 1.75rem;
      display: flex;
      height: 1.06rem;
      z-index: 1;
    }
    #section-staff .staffDisplayLeft::before {
      flex-grow: 0;
      content: "STAFF";
      display: flex;
      font-family: "Geometos";
      background-color: #22bbff;
      font-size: 0.72rem;
      line-height: 1rem;
      width: 2.96rem;
      height: 100%;
      justify-content: center;
      align-items: center;
    }
    #section-staff .staffListContainer {
      flex-grow: 1;
      overflow: hidden;
      margin: 0 0.5rem;
      position: relative;
      -webkit-mask-image: linear-gradient(to right, rgba(0, 0, 0, 0.3) 0%, #000000 40%, #000000 60%, rgba(0, 0, 0, 0.3) 100%);
    }
    #section-staff .staffListWrapper {
      height: 100%;
      position: absolute;
      left: 0;
      display: flex;
    }
    #section-staff .staffItem {
      height: 100%;
      font-size: 0.89rem;
      color: #b7b7b7;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    #section-staff .staffItem.active {
      color: #fff;
      font-weight: bold;
    }
    #section-staff .staffItemCareer {
      display: none;
    }
    #section-staff .staffItemName {
      display: none;
    }
    #section-staff .staffFooter {
      display: none;
    }
    #section-staff .staffDetail {
      height: 100%;
      width: 100%;
      position: absolute;
    }
    #section-staff .staffDetail.show .staffDetailContentWrapper::before {
      width: 115%;
    }
    #section-staff .staffDetailContentWrapper {
      padding-left: 2rem;
      width: 100%;
      box-sizing: border-box;
      height: 24rem;
      bottom: 0;
      padding-right: 2rem;
      background-image: url(https://web.hycdn.cn/arknights/official/static/staff_info_bg.aa5c9105b2bde239c7c6.png);
      background-size: 100% auto;
      background-position: 0 100%;
      background-repeat: no-repeat;
    }
    #section-staff .staffDetailContentWrapper::before {
      left: -15%;
    }
    #section-staff .staffDetailContentWrapper::after {
      left: 2rem;
      top: -1rem;
    }
    #section-staff .staffDetailInfo {
      bottom: 52.6%;
      padding-bottom: 1.75rem;
      padding-left: 1.75rem;
    }
    #section-staff .staffDetailInfoTop {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    #section-staff .staffDetailInfoName {
      font-size: 3.8275rem;
    }
    #section-staff .staffDetailInfoNameEn {
      font-size: 1.18rem;
    }
    #section-staff .staffDetailInfoCareer {
      fill: #fff;
      display: block;
      width: 3.5rem;
      height: 3.5rem;
    }
    #section-staff .staffDetailDesc {
      top: 47.4%;
      padding: 1.53rem 1.87rem 0;
      font-size: 0.722rem;
      line-height: 1.75em;
    }
  }
  
  .arrowBtn svg {
    fill: #666666;
    transition: fill 0.3s;
  }
  .arrowBtn svg circle {
    transition: transform 0.3s;
  }
  .arrowBtn svg use {
    transition: transform 0.3s;
  }
  .arrowBtn:hover.prev svg,
  .arrowBtn.clicked.prev svg {
    fill: #d6d6d6;
  }
  .arrowBtn:hover.prev svg .top,
  .arrowBtn.clicked.prev svg .top {
    transform: translate(8%, -8%);
  }
  .arrowBtn:hover.prev svg .dot,
  .arrowBtn.clicked.prev svg .dot {
    transform: scale(1);
  }
  .arrowBtn:hover.prev svg .bot,
  .arrowBtn.clicked.prev svg .bot {
    transform: translate(8%, 8%);
  }
  .arrowBtn:hover.next svg,
  .arrowBtn.clicked.next svg {
    fill: #d6d6d6;
  }
  .arrowBtn:hover.next svg .dot,
  .arrowBtn.clicked.next svg .dot {
    transform: scale(1);
  }
  .arrowBtn:hover.next svg .top,
  .arrowBtn.clicked.next svg .top {
    transform: translate(-8%, -8%);
  }
  .arrowBtn:hover.next svg .bot,
  .arrowBtn.clicked.next svg .bot {
    transform: translate(-8%, 8%);
  }
  
  #section-world .storyDetailView {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
  }
  #section-world .storyDetailNavBtn svg {
    height: 100%;
    display: block;
    margin: auto;
  }
  #section-world .storyDetailInfoWrapper {
    position: relative;
  }
  #section-world .storyDetailInfo {
    position: absolute;
    color: #fff;
  }
  #section-world .storyDetailInfoNameWrapper {
    position: relative;
  }
  #section-world .storyDetailInfoName {
    font-family: "SerifHeavy";
    fill: #fff;
    position: relative;
  }
  #section-world .storyDetailInfoNameEn {
    font-family: "Geometos";
    fill: #fff;
  }
  #section-world .wrapperTextContent {
    height: 100%;
  }
  #section-world .wrapperTextContent svg {
    height: 100%;
    display: block;
  }
  #section-world .storyDetailInfoDescWrapper {
    position: absolute;
  }
  #section-world .storyDetailInfoDesc {
    width: 100%;
    font-family: "SansRegular";
  }
  #section-world .svgDefs {
    position: absolute;
    visibility: hidden;
    display: none;
    top: 0;
    left: 0;
    width: 1em;
    height: 1em;
  }
  
  @media only screen and (min-width: 429px) {
    #section-world .storyListWrapper {
      display: flex;
      flex-grow: 1;
      align-items: center;
      z-index: 1;
      visibility: hidden;
    }
    #section-world .storyList {
      width: 50rem;
    }
    #section-world .storyItem {
      color: #9a9a9a;
      text-shadow: 5px 5px 10px rgba(0, 0, 0, 0.82);
      font-size: 1.875rem;
      height: 7.8125rem;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      transition: color 0.3s;
      position: relative;
      border: none;
    }
    #section-world .storyItem:not(:last-of-type) {
      border-image: linear-gradient(to right, #464646 0%, #464646 60%, rgba(70, 70, 70, 0) 100%) 30 stretch;
      border-left-width: 0;
      border-top-width: 0;
      border-right-width: 0;
      border-bottom: 1px solid;
    }
    #section-world .storyItem:hover {
      color: #fff;
    }
    #section-world .storyItem:hover .storyItemNameEn {
      margin-right: 2rem;
    }
    #section-world .storyItem:hover .storyItemNameBack {
      opacity: 1;
    }
    #section-world .storyItemName {
      font-family: "SerifHeavy";
      pointer-events: none;
    }
    #section-world .storyItemNameEn {
      margin-left: 0.5em;
      margin-right: 5rem;
      font-family: "Geometos";
      transition: margin-right 0.3s;
      pointer-events: none;
    }
    #section-world .storyItemNameBack {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      font-family: "Geometos";
      fill: none;
      font-size: 88px;
      stroke: rgba(255, 255, 255, 0.1);
      opacity: 0;
      transition: opacity 0.3s;
      pointer-events: none;
    }
    #section-world .storyDrawBoard {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
    }
    #section-world .storyDetailNavBtn {
      padding: 0.625rem;
      width: 4.375rem;
      height: 4.375rem;
      visibility: hidden;
    }
    #section-world .storyDetailNavBtn.prev {
      order: 1;
      margin-right: 4.75rem;
    }
    #section-world .storyDetailNavBtn.next {
      order: 3;
      margin-left: 4.75rem;
    }
    #section-world .storyDetailClose {
      width: 5rem;
      height: 5rem;
      position: absolute;
      right: -5rem;
      padding: 0.5rem;
      bottom: 75%;
      transform: translate(-50%, 50%);
    }
    #section-world .storyDetailCloseBtn {
      visibility: hidden;
      padding: 0.5rem;
      width: 4rem;
      height: 4rem;
      fill: #666666;
      transition: fill 0.3s;
    }
    #section-world .storyDetailCloseBtn:hover {
      fill: #f0f0f0;
    }
    #section-world .storyDetailCloseBtn svg {
      width: 100%;
      height: 100%;
    }
    #section-world .storyDetailInfoWrapper {
      height: 100%;
      width: 57%;
      order: 2;
      max-width: 68.375rem;
    }
    #section-world .storyDetailInfo {
      width: 0;
      right: 5.25rem;
      bottom: 50%;
      position: absolute;
      transition: width 0.6s;
    }
    #section-world .storyDetailInfo.show {
      width: 30.5rem;
    }
    #section-world .storyDetailInfo::after {
      content: "";
      display: block;
      width: 100%;
      height: 1px;
      background-image: linear-gradient(to left, #909090 0%, #909090 25%, transparent 50%);
      background-size: 4px 1px;
      position: absolute;
      bottom: 0;
    }
    #section-world .storyDetailInfoName {
      margin-bottom: 0.75rem;
      height: 4rem;
    }
    #section-world .storyDetailInfoNameEn {
      margin-bottom: 0.625rem;
      height: 2rem;
    }
    #section-world .storyDetailInfoDescWrapper {
      margin-top: 0.875rem;
      position: absolute;
    }
    #section-world .storyDetailInfoDesc {
      font-size: 1rem;
      line-height: 2;
    }
    #section-world .storyDetailSeparator {
      pointer-events: none;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    #section-world .storyDetailSeparator::before,
    #section-world .storyDetailSeparator::after {
      display: block;
      content: "";
      position: absolute;
    }
    #section-world .storyDetailSeparator.horizontal::before,
    #section-world .storyDetailSeparator.vertical::before,
    #section-world .storyDetailSeparator.horizontal::after,
    #section-world .storyDetailSeparator.vertical::after {
      background-size: 7px 2px;
      height: 2px;
      width: 0;
      top: 50%;
      margin-top: -1px;
    }
    #section-world .storyDetailSeparator.horizontal::before,
    #section-world .storyDetailSeparator.vertical::before {
      background-image: linear-gradient(to left, #909090 0%, #909090 14%, transparent 28%);
      right: 5rem;
      background-position: 100% 0;
    }
    #section-world .storyDetailSeparator.horizontal::after,
    #section-world .storyDetailSeparator.vertical::after {
      background-image: linear-gradient(to right, #909090 0%, #909090 14%, transparent 28%);
      left: 5rem;
      background-position: 0 0;
    }
    @keyframes KF_separator_horizontal_show {
      from {
        width: 0;
      }
      to {
        width: 100vw;
      }
    }
    @keyframes KF_separator_vertical_show_1 {
      from {
        transform: rotate(0);
      }
      to {
        transform: rotate(90deg);
      }
    }
    @keyframes KF_separator_vertical_show_2 {
      from {
        width: 100vw;
      }
      to {
        width: 100vh;
      }
    }
    @keyframes KF_separator_horizontal_hide {
      from {
        width: 100vw;
      }
      to {
        width: 0;
      }
    }
    @keyframes KF_separator_vertical_hide {
      from {
        width: 100vh;
      }
      to {
        width: 0;
      }
    }
    #section-world .storyDetailView {
      display: flex;
      justify-content: center;
      align-items: center;
      visibility: hidden;
    }
    #section-world .storyDetailView.show .storyDetailSeparator.horizontal::before,
    #section-world .storyDetailView.show .storyDetailSeparator.vertical::before,
    #section-world .storyDetailView.show .storyDetailSeparator.horizontal::after,
    #section-world .storyDetailView.show .storyDetailSeparator.vertical::after {
      animation: KF_separator_horizontal_show 0.8s forwards;
    }
    #section-world .storyDetailView.show .storyDetailSeparator.vertical {
      animation: KF_separator_vertical_show_1 0.6s 0.6s cubic-bezier(0.22, 0.61, 0.33, 1) forwards;
    }
    #section-world .storyDetailView.show .storyDetailSeparator.vertical::before,
    #section-world .storyDetailView.show .storyDetailSeparator.vertical::after {
      animation: KF_separator_vertical_show_2 0.4s 0.6s forwards;
    }
    #section-world .storyDetailView.hide .storyDetailSeparator.horizontal::before,
    #section-world .storyDetailView.hide .storyDetailSeparator.horizontal::after {
      animation: KF_separator_horizontal_hide 0.6s forwards;
    }
    #section-world .storyDetailView.hide .storyDetailSeparator.vertical {
      transform: rotate(90deg);
    }
    #section-world .storyDetailView.hide .storyDetailSeparator.vertical::before,
    #section-world .storyDetailView.hide .storyDetailSeparator.vertical::after {
      animation: KF_separator_vertical_hide 0.6s forwards;
    }
  }
  
  @media only screen and (max-width: 428px) {
    #section-world .storyListWrapper {
      display: none;
    }
    #section-world .storyDetailClose {
      display: none;
    }
    #section-world .storyDetailNavBtn {
      width: 3rem;
      height: 3rem;
      position: absolute;
      bottom: 50%;
      z-index: 1;
      padding: 0.5rem;
    }
    #section-world .storyDetailNavBtn.prev {
      left: 0;
      opacity: 0;
    }
    #section-world .storyDetailNavBtn.next {
      right: 0;
      opacity: 0;
    }
    #section-world .storyDetailSeparator {
      display: none;
    }
    #section-world .storyDetailInfoWrapper {
      box-sizing: border-box;
      display: flex;
      position: absolute;
      top: 5rem;
      left: 2.5rem;
      right: 2.5rem;
      bottom: 3rem;
    }
    #section-world .storyDetailInfo {
      bottom: 12rem;
      width: 0;
      transition: width 0.6s;
    }
    #section-world .storyDetailInfo.show {
      width: 100%;
    }
    #section-world .storyDetailInfo::after {
      content: "";
      display: block;
      width: 100%;
      height: 1px;
      background-color: #808080;
      position: absolute;
      bottom: 0;
    }
    #section-world .storyDetailInfoName {
      margin-bottom: 0.5rem;
      height: 3rem;
    }
    #section-world .storyDetailInfoNameEn {
      margin-bottom: 0.75rem;
      height: 1.5rem;
    }
    #section-world .storyDetailInfoDescWrapper {
      margin-top: 0.5rem;
      font-size: 0.83rem;
      line-height: 2;
      text-align: justify;
    }
  }
  
  .wrapperText {
    overflow: hidden;
  }
  .wrapperText:not(.wrapperTextDirectionDown) .wrapperTextContent {
    transform: translate3d(0, 100%, 0);
    transition: transform 0.8s cubic-bezier(0.21, 0.45, 0.07, 1);
  }
  .wrapperText.wrapperTextDirectionDown .wrapperTextContent {
    transform: translate3d(0, -100%, 0);
    transition: transform 0.8s cubic-bezier(0.21, 0.45, 0.07, 1);
  }
  .wrapperText.show .wrapperTextContent {
    transform: translate3d(0, 0, 0);
  }
  @keyframes KF_Wrapper_Text_Pre_Go_Next {
    0% {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
    100% {
      opacity: 0;
      transform: translate3d(-20%, 0, 0);
    }
  }
  @keyframes KF_Wrapper_Text_Go_Next {
    0% {
      opacity: 0;
      transform: translate3d(20%, 0, 0);
    }
    100% {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
  }
  @keyframes KF_Wrapper_Text_Pre_Go_Prev {
    0% {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
    100% {
      opacity: 0;
      transform: translate3d(20%, 0, 0);
    }
  }
  @keyframes KF_Wrapper_Text_Go_Prev {
    0% {
      opacity: 0;
      transform: translate3d(-20%, 0, 0);
    }
    100% {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
  }
  .wrapperText.preGoNext {
    animation: KF_Wrapper_Text_Pre_Go_Next 0.4s forwards;
  }
  .wrapperText.goNext {
    animation: KF_Wrapper_Text_Go_Next 0.4s forwards;
  }
  .wrapperText.preGoPrev {
    animation: KF_Wrapper_Text_Pre_Go_Prev 0.4s forwards;
  }
  .wrapperText.goPrev {
    animation: KF_Wrapper_Text_Go_Prev 0.4s forwards;
  }
  
  #section-media {
    color: #e3e3e3;
    transform: translateZ(1px);
  }
  #section-media .media-nav-btn {
    position: absolute;
    z-index: 1;
  }
  #section-media .media-main-view {
    position: relative;
  }
  #section-media .media-main-view .media-main-pic {
    position: relative;
  }
  #section-media .media-main-view .media-main-pic #media-layer-btn {
    pointer-events: none;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
  }
  #section-media .media-main-view .media-main-pic[data-type="1"] .media-play-btn {
    color: #ffffff;
    pointer-events: auto;
  }
  #section-media .media-main-view .media-main-pic .media-play-btn {
    color: rgba(255, 255, 255, 0);
    pointer-events: none;
    transition: color 0.3s;
    position: absolute;
  }
  #section-media .media-main-view .media-main-pic .media-img {
    background-color: rgba(0, 0, 0, 0.5);
    position: absolute;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
  }
  #section-media .media-main-view .media-user-interactive .media-current-info .media-info-serial {
    font-family: "Bender";
  }
  #section-media .media-main-view .media-user-interactive .media-detail-type {
    display: none;
  }
  
  @media only screen and (min-width: 429px) {
    #section-media {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 100%;
      height: 100%;
    }
    #section-media .media-nav-btn {
      position: relative;
      width: 5.63rem;
      flex: none;
    }
    #section-media .media-nav-btn.prev {
      margin-left: 8.13rem;
    }
    #section-media .media-nav-btn.next {
      margin-right: 8.13rem;
    }
    #section-media .media-container-box {
      flex: auto;
      min-width: 0;
    }
    #section-media .media-main-view .media-play-btn {
      width: 5.31rem;
      right: 0;
      bottom: 1.88rem;
    }
    #section-media .media-main-view .media-main-pic {
      width: 51.88rem;
      height: 29.13rem;
      margin-left: 5.25rem;
    }
    #section-media .media-user-interactive {
      position: relative;
      float: left;
      margin-top: -15rem;
      pointer-events: none;
      z-index: 1;
    }
    #section-media .media-user-interactive .media-current-info {
      height: 16.25rem;
      width: 57.13rem;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
    }
    #section-media .media-user-interactive .media-current-info .media-info-wrapper {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    #section-media .media-user-interactive .media-current-info .media-info-wrapper .media-info-serial {
      padding-left: 0.94rem;
      letter-spacing: 0.31rem;
      font-size: 5.63rem;
    }
    #section-media .media-user-interactive .media-current-info .media-info-wrapper .media-info-title {
      font-size: 3.13rem;
      padding: 0.94rem;
      background-color: rgba(255, 255, 255, 0.08);
      -webkit-backdrop-filter: blur(12px);
      backdrop-filter: blur(12px);
    }
    #section-media .media-user-interactive .media-current-info .media-info-wrapper .media-info-detail {
      padding-left: 0.94rem;
    }
    #section-media .media-user-interactive .media-current-info .media-nav-wrapper {
      pointer-events: auto;
    }
    #section-media .media-user-interactive .media-current-info .media-nav-wrapper .media-nav-item {
      display: inline-block;
    }
    #section-media .media-user-interactive .media-current-info .media-nav-wrapper .media-nav-item::after {
      content: "";
      display: inline-block;
      margin-left: 0.5rem;
      width: 0.38rem;
      height: 1rem;
      transition: background-color 0.3s, transform 0.3s;
      transform: skewX(-30deg) scale(1);
      background-color: gray;
    }
    #section-media .media-user-interactive .media-current-info .media-nav-wrapper .media-nav-item:hover::after,
    #section-media .media-user-interactive .media-current-info .media-nav-wrapper .media-nav-item[active="true"]::after {
      background-color: white;
    }
    #section-media .media-user-interactive .media-current-info .media-nav-wrapper .media-nav-item[active="true"]::after {
      transform: skewX(-30deg) scale(1.2);
    }
    #section-media .media-list-wrapper {
      margin-top: 5.63rem;
      height: 7.13rem;
    }
    #section-media .media-list-wrapper .media-list-item {
      display: inline-block;
      width: 12.75rem;
      height: 7.13rem;
      padding: 0 1.44rem;
      position: absolute;
      opacity: 0;
      transition: opacity 0.5s, transform 0.5s;
    }
    #section-media .media-list-wrapper .media-list-item .media-list-item-img {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;
      background-size: cover;
      background-position: center;
      opacity: 0.5;
      transform: scale(1);
      transition: opacity 0.3s, transform 0.3s;
    }
    #section-media .media-list-wrapper .media-list-item .media-list-item-img::after {
      content: attr(data-title);
      display: inline-block;
      background: linear-gradient(0, #000000, transparent) center center / auto auto no-repeat;
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      padding: 0.31rem;
      padding-top: 0.63rem;
      transform: translateY(100%);
      transition: transform 0.3s;
    }
    #section-media .media-list-wrapper .media-list-item:hover .media-list-item-img {
      opacity: 1;
      transform: scale(1.1);
    }
    #section-media .media-list-wrapper .media-list-item:hover .media-list-item-img::after {
      transform: translateY(0);
    }
    #section-media .media-list-wrapper .media-list-item:hover .media-list-item-desc,
    #section-media .media-list-wrapper .media-list-item:hover::after {
      transform: translateY(0);
    }
  }
  
  @media only screen and (max-width: 428px) {
    #section-media {
      padding-top: 5.38rem;
      box-sizing: border-box;
      height: 100%;
      position: relative;
      display: flex;
      flex-direction: column;
    }
    #section-media .media-nav-btn {
      width: 4.23rem;
      top: 10.77rem;
    }
    #section-media .media-nav-btn.prev {
      left: -0.77rem;
    }
    #section-media .media-nav-btn.next {
      right: -0.77rem;
    }
    #section-media #media-container-box {
      flex: auto;
      height: 100%;
      position: relative;
      text-align: center;
    }
    #section-media #media-container-box .media-main-view {
      height: 100%;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    #section-media #media-container-box .media-main-view .media-current {
      z-index: 1;
      flex: none;
      overflow: hidden;
    }
    #section-media #media-container-box .media-main-view .media-play-btn {
      width: 3.85rem;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
    #section-media #media-container-box .media-main-view .media-main-pic {
      width: 22.15rem;
      height: 12.46rem;
    }
    #section-media #media-container-box .media-main-view .media-user-interactive {
      text-align: left;
      margin: 0.38rem 0 0;
    }
    #section-media #media-container-box .media-main-view .media-user-interactive .media-current-info .media-info-wrapper {
      display: flex;
      align-items: center;
      font-size: 0.62rem;
      overflow: hidden;
    }
    #section-media #media-container-box .media-main-view .media-user-interactive .media-current-info .media-info-wrapper .media-info-serial {
      display: none;
    }
    #section-media #media-container-box .media-main-view .media-user-interactive .media-current-info .media-info-wrapper .media-info-title {
      font-size: 1.38rem;
      max-width: 11.15rem;
      overflow: hidden;
      white-space: nowrap;
      margin-right: 0.69rem;
    }
    #section-media #media-container-box .media-main-view .media-user-interactive .media-current-info .media-nav-wrapper {
      display: none;
    }
    #section-media #media-container-box .media-main-view .media-list-wrapper {
      box-sizing: border-box;
      height: 100%;
      flex: auto;
      overflow: hidden;
      position: relative;
      width: 100%;
      display: flex;
      justify-content: center;
    }
    #section-media #media-container-box .media-main-view .media-list-wrapper .media-list-item {
      width: 22.15rem;
      height: 12.46rem;
      position: relative;
      border-width: 1.92rem;
      border-style: solid;
      border-image: url(https://web.hycdn.cn/arknights/official/static/media-border.76f20f11670e935b7062.png) 50 repeat;
      position: absolute;
      top: 0;
      opacity: 0;
      transition: opacity 0.5s, transform 0.5s, top 0.5s;
    }
    #section-media #media-container-box .media-main-view .media-list-wrapper .media-list-item::before {
      content: attr(data-serial);
      display: inline-block;
      position: absolute;
      z-index: 1;
      font-size: 0.92rem;
      left: -0.46rem;
      top: -0.46rem;
    }
    #section-media #media-container-box .media-main-view .media-list-wrapper .media-list-item::after {
      content: "";
      display: inline-block;
      position: absolute;
      z-index: 1;
      width: 1.04rem;
      height: 1.04rem;
      right: -0.23rem;
      bottom: -0.23rem;
      background: url(https://web.hycdn.cn/arknights/official/static/expand-arrow.0e5cf6857d3189597130.png) center / contain no-repeat;
    }
    #section-media #media-container-box .media-main-view .media-list-wrapper .media-list-item .media-list-item-img {
      width: 100%;
      height: 100%;
      clip-path: polygon(7% 0, 100% 0, 100% 88%, 93% 100%, 0 100%, 0 12%);
      background-size: cover;
      background-position: center;
    }
    #section-media #media-container-box .media-main-view .media-list-wrapper .media-list-item .media-list-item-desc {
      display: none;
    }
  }
  