# Synctix 票務平台前端開發文檔

## 📋 目錄
- [專案概述](#專案概述)
- [技術架構](#技術架構)
- [頁面功能詳解](#頁面功能詳解)
- [API 整合規範](#api-整合規範)
- [組件架構](#組件架構)
- [狀態管理](#狀態管理)
- [路由配置](#路由配置)
- [開發規範](#開發規範)

## 🎯 專案概述

Synctix 是一個現代化的演唱會票務平台前端應用，採用 React 18 + Vite 架構，提供完整的票務購買流程和管理功能。

### 核心特色
- 響應式設計，支援桌面、平板、手機
- 深色主題風格，具備毛玻璃效果
- 完整的用戶認證系統
- 實時的 API 數據整合
- 管理員後台功能

## 🏗️ 技術架構

### 前端技術棧
```json
{
  "框架": "React 18.2.0",
  "建構工具": "Vite 4.4.5",
  "UI 庫": "Material-UI (MUI) 5.14.5",
  "路由": "React Router DOM 6.15.0",
  "HTTP 客戶端": "Axios 1.5.0",
  "狀態管理": "React Context API",
  "樣式": "CSS-in-JS (MUI styled)"
}
```

### 專案結構
```
frontend/
├── src/
│   ├── components/          # 共用組件
│   │   ├── ArknightsBackground.jsx    # 背景效果
│   │   ├── HeroBanner.jsx            # 主頁輪播
│   │   ├── Navbar.jsx                # 導航列
│   │   ├── Footer.jsx                # 頁腳
│   │   ├── CategoryFilter.jsx        # 分類篩選
│   │   ├── ThemeManager.jsx          # 主題管理
│   │   └── ConcertManagementDialog.jsx # 活動管理對話框
│   ├── pages/               # 頁面組件
│   │   ├── ExplorePage.jsx           # 主頁/活動探索
│   │   ├── EventDetailPage.jsx       # 活動詳情
│   │   ├── PurchasePage.jsx          # 購票頁面
│   │   ├── CheckoutPage.jsx          # 結帳頁面
│   │   ├── PaymentPage.jsx           # 付款頁面
│   │   ├── ProfilePage.jsx           # 個人頁面
│   │   ├── LoginPage.jsx             # 登入頁面
│   │   ├── RegisterPage.jsx          # 註冊頁面
│   │   └── AdminDashboard.jsx        # 管理員後台
│   ├── contexts/            # React Context
│   │   ├── AuthContext.jsx           # 認證狀態管理
│   │   └── ThemeContext.jsx          # 主題狀態管理
│   ├── utils/               # 工具函數
│   └── assets/              # 靜態資源
├── public/                  # 公共資源
└── package.json            # 依賴配置
```

## 📱 頁面功能詳解

### 1. 主頁 (ExplorePage) - 路由: `/`
**功能描述**: 活動探索和瀏覽頁面，包含海報輪播和活動列表

**核心功能**:
- 海報輪播展示精選活動
- 活動列表分頁顯示 (每頁 12 個)
- 分類篩選功能
- 搜尋功能
- 響應式卡片佈局

**API 調用**:
```javascript
// 獲取活動列表
GET /api/concerts?page={page}&limit={limit}&category={category}&search={search}

// 獲取海報輪播
GET /api/hero-banners

// 獲取分類列表
GET /api/categories
```

**關鍵組件**:
- `HeroBanner`: 輪播組件
- `CategoryFilter`: 分類篩選
- `ConcertCard`: 活動卡片

### 2. 活動詳情頁 (EventDetailPage) - 路由: `/event/:id`
**功能描述**: 顯示單一活動的完整資訊

**核心功能**:
- 活動基本資訊展示
- 票種和價格列表
- 購票按鈕導向購票頁面
- 活動描述和注意事項

**API 調用**:
```javascript
// 獲取活動詳情
GET /api/concerts/{id}
```

**數據結構**:
```javascript
{
  id: number,
  name: string,
  artist: string,
  date: string,
  time: string,
  location: string,
  poster_url: string,
  description: string,
  categories: string[],
  ticketTypes: TicketType[],
  status: string
}
```

### 3. 購票頁面 (PurchasePage) - 路由: `/purchase/:id`
**功能描述**: 票種選擇和數量設定

**核心功能**:
- 票種選擇 (VIP、一般座位、學生票等)
- 數量選擇 (1-4張限制)
- 價格計算
- 導向結帳頁面

**狀態傳遞**:
```javascript
// 傳遞到 CheckoutPage 的數據
{
  event: EventData,
  ticketType: TicketTypeData,
  quantity: number,
  totalAmount: number
}
```

### 4. 結帳頁面 (CheckoutPage) - 路由: `/checkout`
**功能描述**: 個人資料填寫和訂單確認

**核心功能**:
- 訂單摘要顯示
- 個人資料表單 (會員自動填入)
- 表單驗證
- 創建訂單

**API 調用**:
```javascript
// 創建訂單
POST /api/orders
{
  concert_id: number,
  ticket_type_id: number,
  quantity: number,
  customer_info: {
    name: string,
    email: string,
    phone: string
  },
  user_id?: string  // 登入用戶才有
}
```

### 5. 付款頁面 (PaymentPage) - 路由: `/payment`
**功能描述**: 付款方式選擇和付款處理

**核心功能**:
- 付款方式選擇 (信用卡、LINE Pay、Apple Pay)
- 付款處理
- 購票成功頁面
- QR Code 顯示

**API 調用**:
```javascript
// 處理付款
POST /api/orders/{orderId}/payment
{
  payment_method: string
}
```

### 6. 個人頁面 (ProfilePage) - 路由: `/profile`
**功能描述**: 會員個人資料和購票記錄

**核心功能**:
- 個人資料顯示
- 購票記錄列表
- 票券狀態管理
- 登出功能

**API 調用**:
```javascript
// 獲取用戶資料
GET /api/users/{userId}

// 獲取用戶訂單
GET /api/users/{userId}/orders
```

### 7. 認證頁面
**登入頁面 (LoginPage)** - 路由: `/login`
**註冊頁面 (RegisterPage)** - 路由: `/register`

**API 調用**:
```javascript
// 用戶登入
POST /api/auth/login
{
  email: string,
  password: string
}

// 用戶註冊
POST /api/auth/register
{
  name: string,
  email: string,
  phone: string,
  password: string
}
```

### 8. 管理員後台 (AdminDashboard) - 路由: `/admin`
**功能描述**: 管理員專用的後台管理介面

**核心功能**:
- 儀表板統計數據
- 活動管理 (新增、編輯、刪除)
- 訂單管理
- 用戶管理

**API 調用**:
```javascript
// 儀表板數據
GET /api/admin/dashboard

// 活動管理
GET /api/admin/concerts/manage
POST /api/admin/concerts
PUT /api/admin/concerts/{id}
DELETE /api/admin/concerts/{id}

// 訂單管理
GET /api/admin/orders

// 用戶管理
GET /api/admin/users
```

## 🔌 API 整合規範

### 基礎配置
```javascript
// API 基礎 URL
const API_BASE_URL = 'http://localhost:8000'

// 常用 Headers
const headers = {
  'Content-Type': 'application/json',
  // 如需認證: 'Authorization': `Bearer ${token}`
}
```

### 錯誤處理標準
```javascript
try {
  const response = await fetch(url, options);
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.detail || '請求失敗');
  }
  return await response.json();
} catch (error) {
  console.error('API 錯誤:', error);
  // 顯示用戶友好的錯誤訊息
}
```

### 圖片 URL 處理
**重要**: 後端 API 返回的圖片 URL 已經是完整路徑，前端直接使用即可
```javascript
// 正確使用方式
<img src={concert.poster_url} alt={concert.name} />

// 錯誤方式 (會導致重複域名)
<img src={`http://localhost:8000${concert.poster_url}`} alt={concert.name} />
```

## 🧩 組件架構

### 共用組件說明

#### HeroBanner
**用途**: 主頁海報輪播
**Props**: 無 (內部調用 API)
**特色**: 自動輪播、響應式設計、毛玻璃效果

#### Navbar
**用途**: 全站導航列
**功能**: 
- 品牌 Logo 顯示
- 登入/登出狀態切換
- 管理員入口 (管理員用戶可見)
- 響應式選單

#### CategoryFilter
**用途**: 活動分類篩選
**Props**: 
```javascript
{
  selectedCategory: string,
  onCategoryChange: (category: string) => void
}
```

#### ConcertManagementDialog
**用途**: 管理員活動管理對話框
**Props**:
```javascript
{
  open: boolean,
  onClose: () => void,
  concert: ConcertData | null,
  isEditMode: boolean,
  onSave: (concert: ConcertData) => void
}
```

## 🔄 狀態管理

### AuthContext
**用途**: 全域用戶認證狀態管理

**提供的方法**:
```javascript
{
  user: UserData | null,
  login: (email: string, password: string) => Promise<void>,
  register: (userData: RegisterData) => Promise<void>,
  logout: () => void,
  isAuthenticated: () => boolean
}
```

**使用方式**:
```javascript
import { useAuth } from '../contexts/AuthContext';

function MyComponent() {
  const { user, login, logout, isAuthenticated } = useAuth();
  
  if (isAuthenticated()) {
    return <div>歡迎, {user.name}</div>;
  }
  
  return <LoginForm onLogin={login} />;
}
```

### ThemeContext
**用途**: 主題狀態管理 (深色/淺色模式切換)

## 🛣️ 路由配置

### 路由表
```javascript
const routes = [
  { path: '/', component: 'ExplorePage', public: true },
  { path: '/login', component: 'LoginPage', public: true },
  { path: '/register', component: 'RegisterPage', public: true },
  { path: '/event/:id', component: 'EventDetailPage', public: true },
  { path: '/purchase/:id', component: 'PurchasePage', public: true },
  { path: '/checkout', component: 'CheckoutPage', public: true },
  { path: '/payment', component: 'PaymentPage', public: true },
  { path: '/profile', component: 'ProfilePage', protected: true },
  { path: '/admin', component: 'AdminDashboard', admin: true }
];
```

### 路由保護
- **公開路由**: 所有用戶可訪問
- **保護路由**: 需要登入才能訪問
- **管理員路由**: 需要管理員權限

## 📋 開發規範

### 代碼風格
- 使用 ES6+ 語法
- 函數組件 + Hooks
- 統一的錯誤處理
- 響應式設計優先

### 命名規範
- 組件: PascalCase (例: `EventDetailPage`)
- 函數: camelCase (例: `handleSubmit`)
- 常數: UPPER_SNAKE_CASE (例: `API_BASE_URL`)

### 性能優化
- 圖片使用 WebP 格式
- 分頁載入大量數據
- 適當使用 React.memo
- 避免不必要的重新渲染

### 測試建議
- 單元測試: 關鍵業務邏輯
- 整合測試: API 調用流程
- E2E 測試: 完整購票流程

## 🚀 部署說明

### 開發環境
```bash
npm run dev  # 啟動開發服務器 (http://localhost:5173)
```

### 生產建構
```bash
npm run build  # 建構生產版本
npm run preview  # 預覽生產版本
```

### 環境變數
```env
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_TITLE=Synctix 票務平台
```

## 📞 技術支援

如有任何前端相關問題，請參考此文檔或聯繫前端開發團隊。

## 📊 數據結構規範

### 演唱會數據 (Concert)
```typescript
interface Concert {
  id: number;
  name: string;
  artist: string;
  date: string;           // YYYY-MM-DD
  time: string;           // HH:MM
  location: string;
  address: string;
  poster_url: string;     // 完整 URL
  description: string;
  categories: string[];
  ticketSaleStart: string; // YYYY-MM-DD HH:MM
  ticketTypes: TicketType[];
  importantNotes: string[];
  purchaseInstructions: string[];
  legalNotices: string[];
  status: 'upcoming' | 'on_sale' | 'sold_out' | 'ended';
  featured: boolean;
}
```

### 票種數據 (TicketType)
```typescript
interface TicketType {
  id: number;
  name: string;
  price: number;          // 價格 (整數，單位: 元)
  available: number;      // 可售數量
  total: number;          // 總數量
  description: string;
}
```

### 用戶數據 (User)
```typescript
interface User {
  id: string;
  email: string;
  name: string;
  phone: string;
  is_admin: boolean;
  created_at?: string;
}
```

### 訂單數據 (Order)
```typescript
interface Order {
  id: string;
  user_id: string;
  concert_id: number;
  concert_name: string;
  customer_info: {
    name: string;
    email: string;
    phone: string;
  };
  items: OrderItem[];
  total_amount: number;
  status: 'pending' | 'paid' | 'cancelled';
  payment_method: string;
  created_at: string;
  paid_at: string | null;
  qr_code: string | null;
}
```

### 海報輪播數據 (HeroBanner)
```typescript
interface HeroBanner {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  image: string;          // 完整 URL
  date: string;
  location: string;
  concertId: number;
}
```

## 🔧 API 端點完整列表

### 公開 API
| 方法 | 端點 | 描述 | 參數 |
|------|------|------|------|
| GET | `/api/concerts` | 獲取演唱會列表 | `page`, `limit`, `category`, `search` |
| GET | `/api/concerts/{id}` | 獲取演唱會詳情 | `id` |
| GET | `/api/categories` | 獲取分類列表 | 無 |
| GET | `/api/featured` | 獲取精選演唱會 | 無 |
| GET | `/api/hero-banners` | 獲取輪播海報 | 無 |
| GET | `/api/search` | 搜尋演唱會 | `q`, `category` |

### 認證 API
| 方法 | 端點 | 描述 | 請求體 |
|------|------|------|--------|
| POST | `/api/auth/login` | 用戶登入 | `{email, password}` |
| POST | `/api/auth/register` | 用戶註冊 | `{name, email, phone, password}` |

### 用戶 API
| 方法 | 端點 | 描述 | 參數 |
|------|------|------|------|
| GET | `/api/users/{id}` | 獲取用戶資料 | `id` |
| GET | `/api/users/{id}/orders` | 獲取用戶訂單 | `id` |

### 訂單 API
| 方法 | 端點 | 描述 | 請求體 |
|------|------|------|--------|
| POST | `/api/orders` | 創建訂單 | `CreateOrderRequest` |
| GET | `/api/orders/{id}` | 獲取訂單詳情 | 無 |
| POST | `/api/orders/{id}/payment` | 處理付款 | `{payment_method}` |

### 管理員 API
| 方法 | 端點 | 描述 | 參數/請求體 |
|------|------|------|------------|
| GET | `/api/admin/dashboard` | 儀表板數據 | 無 |
| GET | `/api/admin/concerts/manage` | 管理活動列表 | `status`, `limit`, `offset` |
| POST | `/api/admin/concerts` | 創建活動 | `ConcertCreateRequest` |
| PUT | `/api/admin/concerts/{id}` | 更新活動 | `ConcertUpdateRequest` |
| DELETE | `/api/admin/concerts/{id}` | 刪除活動 | 無 |
| GET | `/api/admin/orders` | 管理訂單列表 | `status`, `limit`, `offset` |
| GET | `/api/admin/users` | 管理用戶列表 | `limit`, `offset` |

## 🎨 UI/UX 設計規範

### 色彩系統
```css
:root {
  --primary-color: #00c0ff;      /* 主要藍色 */
  --secondary-color: #64d8ff;    /* 次要藍色 */
  --background-dark: #1a1a1a;    /* 深色背景 */
  --background-light: #2a2a2a;   /* 淺色背景 */
  --text-primary: #ffffff;       /* 主要文字 */
  --text-secondary: #aaaaaa;     /* 次要文字 */
  --error-color: #f44336;        /* 錯誤色 */
  --success-color: #4caf50;      /* 成功色 */
  --warning-color: #ff9800;      /* 警告色 */
}
```

### 響應式斷點
```css
/* 手機 */
@media (max-width: 600px) { }

/* 平板 */
@media (min-width: 601px) and (max-width: 960px) { }

/* 桌面 */
@media (min-width: 961px) { }
```

### 組件樣式規範
- **卡片**: 圓角 8px，陰影效果，毛玻璃背景
- **按鈕**: 主要按鈕使用品牌色，次要按鈕使用邊框樣式
- **表單**: 統一的輸入框樣式，錯誤狀態顯示
- **導航**: 固定頂部，半透明背景

## 🔒 安全性考量

### 前端安全措施
1. **輸入驗證**: 所有用戶輸入都進行前端驗證
2. **XSS 防護**: 使用 React 的內建 XSS 防護
3. **敏感資料**: 不在前端存儲敏感資料
4. **HTTPS**: 生產環境強制使用 HTTPS

### 認證流程
1. 用戶登入後，後端返回用戶資料 (不含密碼)
2. 前端將用戶資料存儲在 Context 中
3. 頁面刷新時需要重新登入 (無持久化 token)
4. 登出時清除所有用戶狀態

## 📱 移動端適配

### 響應式設計要點
- **觸控友好**: 按鈕最小 44px 觸控區域
- **滑動操作**: 支援左右滑動切換輪播
- **縮放適配**: 圖片和文字自動縮放
- **導航優化**: 移動端使用漢堡選單

### 性能優化
- **圖片懶載入**: 使用 Intersection Observer
- **虛擬滾動**: 大列表使用虛擬滾動
- **代碼分割**: 路由級別的代碼分割
- **快取策略**: 適當的 HTTP 快取設定

## 🧪 測試策略

### 測試覆蓋範圍
1. **單元測試**: 工具函數、Hook、純組件
2. **整合測試**: API 調用、表單提交
3. **E2E 測試**: 完整購票流程
4. **視覺測試**: 組件快照測試

### 測試用例範例
```javascript
// 購票流程測試
describe('購票流程', () => {
  test('用戶可以完成完整購票流程', async () => {
    // 1. 瀏覽活動
    // 2. 選擇票種
    // 3. 填寫資料
    // 4. 完成付款
    // 5. 獲得票券
  });
});
```

## 🚀 部署和維護

### CI/CD 流程
1. **代碼提交**: Git push 觸發自動化流程
2. **代碼檢查**: ESLint、Prettier 檢查
3. **測試執行**: 自動執行所有測試
4. **建構部署**: 自動建構並部署到測試環境
5. **生產部署**: 手動確認後部署到生產環境

### 監控和日誌
- **錯誤監控**: 使用 Sentry 等工具監控前端錯誤
- **性能監控**: 監控頁面載入時間和用戶體驗指標
- **用戶行為**: 追蹤關鍵用戶行為和轉換率

**最後更新**: 2025-07-22
**文檔版本**: 1.0.0
