import React, { useEffect, useState } from 'react';
import { Box, keyframes } from '@mui/material';
import { useTheme } from '../contexts/ThemeContext';

// 🎨 明日方舟特效組件 - 添加科技風格的視覺效果
const ArknightsEffects = () => {
  const { isDarkMode } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // 科技掃描線動畫
  const scanLine = keyframes`
    0% {
      transform: translateY(-100vh);
      opacity: 0;
    }
    10% {
      opacity: 1;
    }
    90% {
      opacity: 1;
    }
    100% {
      transform: translateY(100vh);
      opacity: 0;
    }
  `;

  // 數據流動畫
  const dataFlow = keyframes`
    0% {
      transform: translateX(-100px);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateX(calc(100vw + 100px));
      opacity: 0;
    }
  `;

  // 脈衝動畫
  const pulse = keyframes`
    0%, 100% {
      transform: scale(1);
      opacity: 0.3;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.8;
    }
  `;

  if (!mounted) return null;

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: -1,
        pointerEvents: 'none',
        overflow: 'hidden',
      }}
    >
      {/* 科技掃描線 */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '2px',
          background: isDarkMode 
            ? 'linear-gradient(90deg, transparent, #00c0ff, transparent)'
            : 'linear-gradient(90deg, transparent, #1976d2, transparent)',
          animation: `${scanLine} 8s ease-in-out infinite`,
          animationDelay: '2s',
        }}
      />

      {/* 數據流線條 */}
      {[...Array(3)].map((_, index) => (
        <Box
          key={`dataflow-${index}`}
          sx={{
            position: 'absolute',
            top: `${20 + index * 30}%`,
            left: 0,
            width: '100px',
            height: '1px',
            background: isDarkMode 
              ? 'linear-gradient(90deg, transparent, #00c0ff, #64d8ff, transparent)'
              : 'linear-gradient(90deg, transparent, #1976d2, #42a5f5, transparent)',
            animation: `${dataFlow} ${6 + index * 2}s linear infinite`,
            animationDelay: `${index * 2}s`,
          }}
        />
      ))}



      {/* 中央脈衝點 */}
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '4px',
          height: '4px',
          borderRadius: '50%',
          background: isDarkMode ? '#00c0ff' : '#1976d2',
          animation: `${pulse} 3s ease-in-out infinite`,
        }}
      />

      {/* 隨機閃爍點 */}
      {[...Array(8)].map((_, index) => (
        <Box
          key={`sparkle-${index}`}
          sx={{
            position: 'absolute',
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
            width: '2px',
            height: '2px',
            borderRadius: '50%',
            background: isDarkMode ? '#64d8ff' : '#42a5f5',
            animation: `${pulse} ${2 + Math.random() * 3}s ease-in-out infinite`,
            animationDelay: `${Math.random() * 2}s`,
          }}
        />
      ))}

      {/* 網格線條 */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundImage: isDarkMode 
            ? `linear-gradient(rgba(0, 192, 255, 0.03) 1px, transparent 1px),
               linear-gradient(90deg, rgba(0, 192, 255, 0.03) 1px, transparent 1px)`
            : `linear-gradient(rgba(25, 118, 210, 0.02) 1px, transparent 1px),
               linear-gradient(90deg, rgba(25, 118, 210, 0.02) 1px, transparent 1px)`,
          backgroundSize: '50px 50px',
        }}
      />

      {/* 動態光暈 */}
      <Box
        sx={{
          position: 'absolute',
          top: '10%',
          right: '15%',
          width: '300px',
          height: '300px',
          borderRadius: '50%',
          background: isDarkMode 
            ? 'radial-gradient(circle, rgba(0, 192, 255, 0.1) 0%, transparent 70%)'
            : 'radial-gradient(circle, rgba(25, 118, 210, 0.05) 0%, transparent 70%)',
          animation: `${pulse} 6s ease-in-out infinite`,
        }}
      />

      <Box
        sx={{
          position: 'absolute',
          bottom: '15%',
          left: '10%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: isDarkMode 
            ? 'radial-gradient(circle, rgba(255, 107, 53, 0.08) 0%, transparent 70%)'
            : 'radial-gradient(circle, rgba(220, 0, 78, 0.04) 0%, transparent 70%)',
          animation: `${pulse} 8s ease-in-out infinite`,
          animationDelay: '2s',
        }}
      />
    </Box>
  );
};

export default ArknightsEffects;
