#!/usr/bin/env python3
"""
生成剩餘192張圖片的假資料並加入後端
實現動態分類管理
"""

import json
import random
import requests
import os
from datetime import datetime, timedelta
from pathlib import Path
import hashlib
from PIL import Image
import io

# 讀取現有的演唱會數據
def load_existing_data():
    """載入現有的演唱會數據"""
    concerts_file = Path("data/concerts.json")
    if concerts_file.exists():
        with open(concerts_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return []

# 從 img_source.md 讀取剩餘的圖片 URL (從第101行開始)
def load_remaining_image_urls():
    """從 img_source.md 載入剩餘的圖片 URL"""
    urls = []
    with open("img_source.md", 'r', encoding='utf-8') as f:
        lines = f.readlines()
        # 跳過前100行和標題行，從第101行開始
        for line in lines[101:]:  # 第102行開始 (0-based index)
            line = line.strip()
            if line and line.startswith('http'):
                urls.append(line)
    return urls

# 動態分類管理
def get_dynamic_categories():
    """根據現有數據動態生成分類"""
    existing_concerts = load_existing_data()
    existing_categories = set()
    
    # 收集現有分類
    for concert in existing_concerts:
        existing_categories.update(concert.get("categories", []))
    
    # 基礎分類池
    base_categories = [
        "流行音樂", "搖滾音樂", "電子音樂", "古典音樂", "爵士音樂",
        "鄉村音樂", "嘻哈音樂", "民謠音樂", "金屬音樂", "藍調音樂",
        "雷鬼音樂", "龐克音樂", "獨立音樂", "世界音樂", "新世紀音樂",
        "實驗音樂", "環境音樂", "後搖滾", "迷幻音樂", "前衛音樂",
        "合成器流行", "夢幻流行", "氛圍音樂", "極簡音樂", "噪音音樂"
    ]
    
    # 合併現有分類和基礎分類
    all_categories = list(existing_categories) + [cat for cat in base_categories if cat not in existing_categories]
    
    return all_categories

# 生成假資料
def generate_concert_data(image_url, concert_id, categories_pool):
    """生成單個演唱會的假資料"""
    
    # 藝人名稱池
    artists = [
        "星際旅行者", "夢境編織者", "時空漫遊者", "光影詩人", "數位幻想家",
        "電波傳說", "虛擬現實", "賽博朋克", "未來回聲", "量子共鳴",
        "霓虹夜曲", "像素藝術家", "合成器大師", "節拍製造者", "頻率探索者",
        "聲波雕塑家", "音頻建築師", "電子詩人", "數據流浪者", "算法音樂家",
        "機械夢想", "人工智慧樂團", "神經網路", "深度學習", "量子計算",
        "區塊鏈音樂", "元宇宙樂隊", "虛擬偶像", "全息投影", "增強現實"
    ]
    
    # 地點池
    venues = [
        {"name": "台北流行音樂中心", "address": "台北市南港區市民大道八段99號"},
        {"name": "高雄巨蛋", "address": "高雄市左營區博愛二路757號"},
        {"name": "台中國家歌劇院", "address": "台中市西屯區惠來路二段101號"},
        {"name": "桃園展演中心", "address": "桃園市桃園區中正路1188號"},
        {"name": "新竹縣政府文化局", "address": "新竹縣竹北市縣政九路146號"},
        {"name": "台南文化中心", "address": "台南市東區中華東路三段332號"},
        {"name": "嘉義市文化局", "address": "嘉義市東區忠孝路275號"},
        {"name": "宜蘭演藝廳", "address": "宜蘭市中山路二段482號"},
        {"name": "花蓮文化創意產業園區", "address": "花蓮市中華路144號"},
        {"name": "台東藝文中心", "address": "台東市南京路25號"}
    ]
    
    # 票種名稱池
    ticket_types_pool = [
        "VIP搖滾區", "內場A區", "內場B區", "內場C區", "外場A區", "外場B區",
        "學生票", "早鳥票", "團體票", "身心障礙票", "敬老票", "站票",
        "包廂票", "貴賓席", "前排座位", "中排座位", "後排座位"
    ]
    
    # 隨機選擇
    artist = random.choice(artists)
    venue = random.choice(venues)
    selected_categories = random.sample(categories_pool, random.randint(1, 3))
    
    # 生成日期 (未來3-12個月)
    start_date = datetime.now() + timedelta(days=random.randint(90, 365))
    date_str = start_date.strftime("%Y-%m-%d")
    time_str = f"{random.randint(18, 21)}:{random.choice(['00', '30'])}"
    
    # 生成售票開始時間 (演出前1-2個月)
    ticket_start = start_date - timedelta(days=random.randint(30, 60))
    ticket_start_str = ticket_start.strftime("%Y-%m-%d %H:%M")
    
    # 生成票種
    num_ticket_types = random.randint(3, 6)
    ticket_types = []
    for i in range(num_ticket_types):
        ticket_type = {
            "id": i + 1,
            "name": random.choice(ticket_types_pool),
            "price": random.randint(500, 5000),
            "available": random.randint(0, 300),
            "total": random.randint(100, 500),
            "description": f"{random.choice(ticket_types_pool)}座位"
        }
        ticket_types.append(ticket_type)
    
    # 生成演唱會名稱
    concert_name = f"{artist} {random.choice(['2024', '2025'])} {random.choice(['世界巡迴', '經典重現', '全新專輯', '十週年紀念', '夏日音樂節', '跨年演唱會'])}演唱會"
    
    # 生成描述
    description = f"這是一場精彩的{selected_categories[0]}演出，{artist}將為您帶來最震撼的音樂體驗。不容錯過的音樂盛宴，讓我們一起沉浸在美妙的音樂世界中！"
    
    # 生成重要事項
    important_notes = [
        "請提前30分鐘入場",
        "禁止攜帶外食及飲料",
        "演出期間請勿錄影錄音",
        "遲到觀眾請於適當時機入場",
        "場內禁止吸菸"
    ]
    
    # 生成購票須知
    purchase_instructions = [
        "每人限購4張",
        "購票後恕不退換",
        "請攜帶身分證件入場",
        "學生票需出示學生證",
        "票券遺失恕不補發"
    ]
    
    # 生成法律聲明
    legal_notices = [
        "主辦單位保留修改活動內容之權利",
        "如遇不可抗力因素，主辦單位有權取消或延期",
        "票券轉售無效",
        "入場即同意肖像權使用",
        "禁止商業攝影"
    ]
    
    return {
        "id": concert_id,
        "name": concert_name,
        "artist": artist,
        "date": date_str,
        "time": time_str,
        "location": venue["name"],
        "address": venue["address"],
        "poster_url": image_url,  # 這裡會在下載圖片後更新
        "description": description,
        "categories": selected_categories,
        "ticketSaleStart": ticket_start_str,
        "ticketTypes": ticket_types,
        "importantNotes": random.sample(important_notes, random.randint(3, 5)),
        "purchaseInstructions": random.sample(purchase_instructions, random.randint(3, 5)),
        "legalNotices": random.sample(legal_notices, random.randint(3, 5)),
        "status": random.choice(["upcoming", "on_sale", "sold_out"]),
        "featured": random.choice([True, False])
    }

def download_and_convert_image(url, concert_id):
    """下載圖片並轉換為 WebP 格式"""
    try:
        # 創建圖片目錄
        images_dir = Path("images/concerts")
        images_dir.mkdir(parents=True, exist_ok=True)
        
        # 下載圖片
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # 生成文件名
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        filename = f"concert_{concert_id:03d}_{url_hash}.webp"
        filepath = images_dir / filename
        
        # 轉換為 WebP
        image = Image.open(io.BytesIO(response.content))
        image = image.convert('RGB')
        image.save(filepath, 'WEBP', quality=85, optimize=True)
        
        print(f"✓ 已下載並轉換: {filename}")
        return f"/images/concerts/{filename}"
        
    except Exception as e:
        print(f"✗ 下載失敗 {url}: {e}")
        return url  # 如果下載失敗，返回原始 URL

def main():
    """主函數"""
    print("開始生成剩餘192張圖片的假資料...")
    
    # 載入現有數據
    existing_concerts = load_existing_data()
    existing_ids = {concert["id"] for concert in existing_concerts}
    
    # 載入剩餘的圖片 URL
    remaining_urls = load_remaining_image_urls()
    print(f"找到 {len(remaining_urls)} 張剩餘圖片")
    
    # 獲取動態分類
    categories_pool = get_dynamic_categories()
    print(f"可用分類: {categories_pool}")
    
    # 生成新的演唱會數據
    new_concerts = []
    start_id = max(existing_ids) + 1 if existing_ids else 1
    
    for i, url in enumerate(remaining_urls):
        concert_id = start_id + i
        print(f"處理第 {i+1}/{len(remaining_urls)} 張圖片...")
        
        # 下載並轉換圖片
        local_image_url = download_and_convert_image(url, concert_id)
        
        # 生成演唱會數據
        concert_data = generate_concert_data(local_image_url, concert_id, categories_pool)
        new_concerts.append(concert_data)
    
    # 合併數據
    all_concerts = existing_concerts + new_concerts
    
    # 保存到文件
    with open("data/concerts.json", 'w', encoding='utf-8') as f:
        json.dump(all_concerts, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 成功生成 {len(new_concerts)} 個新演唱會數據")
    print(f"總計演唱會數量: {len(all_concerts)}")
    print(f"數據已保存到 data/concerts.json")
    
    # 顯示分類統計
    all_categories = set()
    for concert in all_concerts:
        all_categories.update(concert.get("categories", []))
    
    print(f"\n📊 分類統計:")
    print(f"總分類數量: {len(all_categories)}")
    print(f"所有分類: {sorted(list(all_categories))}")

if __name__ == "__main__":
    main()
