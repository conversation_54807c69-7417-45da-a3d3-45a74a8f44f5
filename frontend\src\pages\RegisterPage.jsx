import React, { useState } from 'react';
import { useN<PERSON><PERSON>, Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Grid,
  Divider
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Person,
  Email,
  Lock,
  Cake,
  Wc,
  LocationOn,
  Security
} from '@mui/icons-material';

function RegisterPage() {
  const navigate = useNavigate();
  const { register } = useAuth();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    agreeTerms: false
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const handleInputChange = (e) => {
    const { name, value, checked, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // 清除對應的錯誤訊息
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = '請輸入姓名';
    }

    if (!formData.email.trim()) {
      newErrors.email = '請輸入Email';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email格式不正確';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = '請輸入手機號碼';
    } else if (!/^09\d{8}$/.test(formData.phone.replace(/[-\s]/g, ''))) {
      newErrors.phone = '請輸入有效的手機號碼格式（09xxxxxxxx）';
    }

    if (!formData.password) {
      newErrors.password = '請輸入密碼';
    } else if (formData.password.length < 6) {
      newErrors.password = '密碼至少需要6個字元';
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '請確認密碼';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = '密碼確認不一致';
    }

    if (!formData.agreeTerms) {
      newErrors.agreeTerms = '請同意服務條款';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const result = await register({
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        password: formData.password
      });

      if (result.success) {
        alert('註冊成功！歡迎加入 Synctix');
        navigate('/');
      } else {
        setErrors({ submit: result.error || '註冊失敗' });
      }
    } catch (error) {
      setErrors({ submit: '註冊失敗，請稍後再試' });
    } finally {
      setLoading(false);
    }
  };

  const containerStyle = {
    minHeight: '100vh',
    paddingY: '2rem',
    background: 'linear-gradient(135deg, #1a237e 0%, #121212 50%, #1a237e 100%)',
    position: 'relative',
    overflow: 'hidden'
  };

  const paperStyle = {
    padding: '3rem',
    backgroundColor: 'rgba(36, 36, 36, 0.95)',
    backdropFilter: 'blur(10px)',
    border: '1px solid #00c0ff',
    borderRadius: '0',
    clipPath: 'polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 20px 100%, 0 calc(100% - 20px))',
    color: 'white',
    maxWidth: '600px',
    width: '100%',
    margin: '0 auto',
    position: 'relative',
    boxShadow: '0 0 30px rgba(0, 192, 255, 0.3)'
  };

  const textFieldStyle = {
    '& .MuiOutlinedInput-root': {
      color: 'white',
      '& fieldset': {
        borderColor: '#444',
        borderWidth: '1px'
      },
      '&:hover fieldset': {
        borderColor: '#00c0ff'
      },
      '&.Mui-focused fieldset': {
        borderColor: '#00c0ff',
        borderWidth: '2px'
      }
    },
    '& .MuiInputLabel-root': {
      color: '#aaa',
      '&.Mui-focused': {
        color: '#00c0ff'
      }
    },
    '& .MuiInputAdornment-root .MuiSvgIcon-root': {
      color: '#00c0ff'
    }
  };

  const selectStyle = {
    '& .MuiOutlinedInput-root': {
      color: 'white',
      '& fieldset': {
        borderColor: '#444'
      },
      '&:hover fieldset': {
        borderColor: '#00c0ff'
      },
      '&.Mui-focused fieldset': {
        borderColor: '#00c0ff'
      }
    },
    '& .MuiInputLabel-root': {
      color: '#aaa',
      '&.Mui-focused': {
        color: '#00c0ff'
      }
    },
    '& .MuiSelect-icon': {
      color: '#00c0ff'
    }
  };

  return (
    <Box sx={containerStyle}>
      {/* 背景裝飾 */}
      <Box sx={{
        position: 'absolute',
        top: 0, left: 0, width: '100%', height: '100%',
        backgroundImage: 'url(https://www.transparenttextures.com/patterns/hexellence.png)',
        opacity: 0.1,
        zIndex: 0
      }}/>

      <Container maxWidth="md">
        <Paper elevation={0} sx={paperStyle}>
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography variant="h4" component="h1" sx={{
              fontWeight: 'bold',
              color: '#00c0ff',
              textShadow: '0 0 10px #00c0ff',
              mb: 1
            }}>
              建立新帳號
            </Typography>
            <Typography variant="body2" sx={{ color: '#aaa' }}>
              加入 Synctix，開始您的活動探索之旅
            </Typography>
          </Box>

          {errors.submit && (
            <Alert severity="error" sx={{
              mb: 3,
              backgroundColor: 'rgba(211, 47, 47, 0.1)',
              color: '#ff6b6b',
              border: '1px solid rgba(211, 47, 47, 0.3)'
            }}>
              {errors.submit}
            </Alert>
          )}

          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              {/* 姓名 */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="姓名"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  error={!!errors.name}
                  helperText={errors.name}
                  sx={textFieldStyle}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Person />
                      </InputAdornment>
                    )
                  }}
                />
              </Grid>

              {/* Email */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  error={!!errors.email}
                  helperText={errors.email}
                  sx={textFieldStyle}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Email />
                      </InputAdornment>
                    )
                  }}
                />
              </Grid>

              {/* 手機號碼 */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="手機號碼"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  required
                  error={!!errors.phone}
                  helperText={errors.phone || '格式：09xxxxxxxx'}
                  sx={textFieldStyle}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LocationOn />
                      </InputAdornment>
                    )
                  }}
                />
              </Grid>

              {/* 密碼 */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="密碼"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  error={!!errors.password}
                  helperText={errors.password}
                  sx={textFieldStyle}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                          sx={{ color: '#00c0ff' }}
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                />
              </Grid>

              {/* 密碼確認 */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="密碼確認"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  required
                  error={!!errors.confirmPassword}
                  helperText={errors.confirmPassword}
                  sx={textFieldStyle}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          edge="end"
                          sx={{ color: '#00c0ff' }}
                        >
                          {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                />
              </Grid>
            </Grid>

            {/* 服務條款同意 */}
            <Grid container spacing={3} sx={{ mt: 2 }}>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      name="agreeTerms"
                      checked={formData.agreeTerms}
                      onChange={handleInputChange}
                      sx={{
                        color: '#00c0ff',
                        '&.Mui-checked': {
                          color: '#00c0ff',
                        },
                      }}
                    />
                  }
                  label={
                    <Typography variant="body2" sx={{ color: errors.agreeTerms ? '#ff6b6b' : '#aaa' }}>
                      我已閱讀並同意{' '}
                      <Typography component="span" sx={{ color: '#00c0ff', textDecoration: 'underline', cursor: 'pointer' }}>
                        服務條款
                      </Typography>
                      {' '}與{' '}
                      <Typography component="span" sx={{ color: '#00c0ff', textDecoration: 'underline', cursor: 'pointer' }}>
                        隱私權政策
                      </Typography>
                    </Typography>
                  }
                />
                {errors.agreeTerms && (
                  <Typography variant="caption" sx={{ color: '#ff6b6b', display: 'block', ml: 4 }}>
                    {errors.agreeTerms}
                  </Typography>
                )}
              </Grid>
            </Grid>

            {/* 註冊按鈕 */}
            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={loading}
              sx={{
                mt: 4,
                mb: 3,
                backgroundColor: '#00c0ff',
                color: 'black',
                fontWeight: 'bold',
                padding: '12px 0',
                clipPath: 'polygon(10% 0, 100% 0, 90% 100%, 0% 100%)',
                '&:hover': {
                  backgroundColor: '#64d8ff',
                  boxShadow: '0 0 20px rgba(0, 192, 255, 0.5)'
                },
                '&:disabled': {
                  backgroundColor: '#555',
                  color: '#888'
                }
              }}
            >
              {loading ? '註冊中...' : '建立帳號'}
            </Button>

            <Divider sx={{ backgroundColor: '#444', mb: 3 }} />

            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" sx={{ color: '#aaa', mb: 2 }}>
                已經有帳號了嗎？
              </Typography>
              <Button
                component={Link}
                to="/login"
                variant="outlined"
                sx={{
                  color: '#00c0ff',
                  borderColor: '#00c0ff',
                  clipPath: 'polygon(5% 0, 100% 0, 95% 100%, 0% 100%)',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 192, 255, 0.1)',
                    borderColor: '#64d8ff'
                  }
                }}
              >
                立即登入
              </Button>
            </Box>
          </form>
        </Paper>
      </Container>
    </Box>
  );
}

export default RegisterPage;
