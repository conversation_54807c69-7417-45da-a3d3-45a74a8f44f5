#!/usr/bin/env python3
"""
生成演唱會假資料腳本
下載圖片並轉換為 WebP 格式，生成豐富的演唱會資料
"""

import asyncio
import aiohttp
import aiofiles
import os
import json
import random
from datetime import datetime, timedelta
from PIL import Image
import hashlib

# 確保目錄存在
os.makedirs("images/concerts", exist_ok=True)
os.makedirs("data", exist_ok=True)

# 讀取圖片 URL
def read_image_urls():
    """讀取 img_source.md 中的所有 URL"""
    urls = []
    with open("img_source.md", "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if line.startswith("http"):
                urls.append(line)
    return urls

# 演唱會類別
CATEGORIES = [
    "流行音樂", "搖滾樂", "電子音樂", "古典音樂", "爵士樂", 
    "民謠", "嘻哈", "R&B", "獨立音樂", "世界音樂",
    "金屬樂", "龐克", "藍調", "鄉村音樂", "雷鬼",
    "新世紀音樂", "實驗音樂", "融合爵士", "後搖滾", "氛圍音樂"
]

# 藝人名稱池
ARTISTS = [
    "星光樂團", "夢幻女聲", "電音王子", "搖滾之神", "民謠詩人",
    "爵士女王", "嘻哈新星", "古典大師", "獨立樂隊", "世界音樂家",
    "金屬戰士", "龐克少女", "藍調老爹", "鄉村歌手", "雷鬼之王",
    "新世紀音樂家", "實驗音樂人", "融合爵士團", "後搖樂隊", "氛圍音樂師",
    "流行天后", "搖滾傳奇", "電子音樂製作人", "古典交響樂團", "爵士三重奏",
    "民謠組合", "嘻哈團體", "R&B歌手", "獨立創作人", "世界音樂合奏團"
]

# 場地名稱
VENUES = [
    "台北小巨蛋", "高雄巨蛋", "台中洲際棒球場", "桃園國際棒球場",
    "台北國際會議中心", "高雄展覽館", "台中國際展覽館", "新竹縣體育館",
    "台南文化中心", "嘉義縣表演藝術中心", "花蓮縣文化局演藝廳", "台東縣文化處演藝廳",
    "宜蘭縣文化中心", "基隆文化中心", "新北市藝文中心", "桃園展演中心",
    "苗栗縣文化觀光局", "彰化縣員林演藝廳", "南投縣文化局", "雲林縣文化處",
    "屏東縣文化處", "澎湖縣文化局", "金門縣文化局", "連江縣文化處"
]

# 地址對應
ADDRESSES = {
    "台北小巨蛋": "台北市松山區南京東路四段2號",
    "高雄巨蛋": "高雄市左營區博愛二路757號",
    "台中洲際棒球場": "台中市北屯區崇德路三段835號",
    "桃園國際棒球場": "桃園市中壢區領航北路一段1號",
    "台北國際會議中心": "台北市信義區信義路五段1號",
    "高雄展覽館": "高雄市前鎮區成功二路39號",
    "台中國際展覽館": "台中市烏日區中山路三段1號",
    "新竹縣體育館": "新竹縣竹北市縣政九路146號",
    "台南文化中心": "台南市東區中華東路三段332號",
    "嘉義縣表演藝術中心": "嘉義縣民雄鄉建國路二段265號",
    "花蓮縣文化局演藝廳": "花蓮縣花蓮市文復路6號",
    "台東縣文化處演藝廳": "台東縣台東市南京路25號",
    "宜蘭縣文化中心": "宜蘭縣宜蘭市復興路二段101號",
    "基隆文化中心": "基隆市中正區信一路181號",
    "新北市藝文中心": "新北市板橋區莊敬路62號",
    "桃園展演中心": "桃園市桃園區中正路1188號",
    "苗栗縣文化觀光局": "苗栗縣苗栗市自治路50號",
    "彰化縣員林演藝廳": "彰化縣員林市員林大道二段99號",
    "南投縣文化局": "南投縣南投市建國路135號",
    "雲林縣文化處": "雲林縣斗六市大學路三段310號",
    "屏東縣文化處": "屏東縣屏東市大連路69號",
    "澎湖縣文化局": "澎湖縣馬公市中華路230號",
    "金門縣文化局": "金門縣金城鎮環島北路一段66號",
    "連江縣文化處": "連江縣南竿鄉介壽村76號"
}

async def download_and_convert_image(session, url, filename):
    """下載圖片並轉換為 WebP 格式"""
    try:
        async with session.get(url) as response:
            if response.status == 200:
                content = await response.read()
                
                # 保存原始圖片
                temp_path = f"images/concerts/temp_{filename}"
                async with aiofiles.open(temp_path, 'wb') as f:
                    await f.write(content)
                
                # 轉換為 WebP
                webp_path = f"images/concerts/{filename}.webp"
                with Image.open(temp_path) as img:
                    # 調整大小以優化性能
                    img.thumbnail((800, 600), Image.Resampling.LANCZOS)
                    img.save(webp_path, 'WEBP', quality=85, optimize=True)
                
                # 刪除臨時文件
                os.remove(temp_path)
                
                print(f"✅ 已下載並轉換: {filename}.webp")
                return webp_path
            else:
                print(f"❌ 下載失敗: {url} (狀態碼: {response.status})")
                return None
    except Exception as e:
        print(f"❌ 處理圖片時發生錯誤: {url} - {e}")
        return None

def generate_concert_name(artist, category):
    """生成演唱會名稱"""
    concert_types = [
        "世界巡迴演唱會", "台灣巡演", "專輯發表會", "音樂節", "慈善演唱會",
        "跨年演唱會", "夏日音樂祭", "搖滾音樂節", "爵士音樂節", "電音派對",
        "不插電演唱會", "經典重現演唱會", "告別演唱會", "復出演唱會", "十週年演唱會"
    ]
    
    year = random.choice([2024, 2025])
    concert_type = random.choice(concert_types)
    
    return f"{artist} {year} {concert_type}"

def generate_ticket_types():
    """生成票種資訊"""
    ticket_types = [
        {"name": "VIP搖滾區", "base_price": 3500, "description": "最靠近舞台，站立區域"},
        {"name": "內場A區", "base_price": 2800, "description": "內場前排座位"},
        {"name": "內場B區", "base_price": 2200, "description": "內場中排座位"},
        {"name": "內場C區", "base_price": 1800, "description": "內場後排座位"},
        {"name": "外場A區", "base_price": 1500, "description": "外場前排座位"},
        {"name": "外場B區", "base_price": 1200, "description": "外場中排座位"},
        {"name": "外場C區", "base_price": 800, "description": "外場後排座位"},
        {"name": "學生票", "base_price": 600, "description": "限學生購買，需出示學生證"}
    ]
    
    # 隨機選擇 3-6 種票型
    selected_types = random.sample(ticket_types, random.randint(3, 6))
    
    result = []
    for i, ticket_type in enumerate(selected_types):
        total = random.randint(100, 500)
        available = random.randint(0, total)
        
        result.append({
            "id": i + 1,
            "name": ticket_type["name"],
            "price": ticket_type["base_price"] + random.randint(-200, 500),
            "available": available,
            "total": total,
            "description": ticket_type["description"]
        })
    
    return result

async def main():
    """主函數"""
    print("🎵 開始生成演唱會資料...")
    
    # 讀取圖片 URL
    urls = read_image_urls()
    print(f"📸 找到 {len(urls)} 張圖片")
    
    # 下載並轉換圖片
    concerts = []
    
    async with aiohttp.ClientSession() as session:
        for i, url in enumerate(urls[:100]):  # 限制前100張圖片以避免過多資料
            # 生成文件名
            url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
            filename = f"concert_{i+1:03d}_{url_hash}"
            
            # 下載並轉換圖片
            image_path = await download_and_convert_image(session, url, filename)
            
            if image_path:
                # 生成演唱會資料
                artist = random.choice(ARTISTS)
                category = random.choice(CATEGORIES)
                venue = random.choice(VENUES)
                
                # 生成日期（未來3個月內）
                start_date = datetime.now()
                end_date = start_date + timedelta(days=90)
                random_date = start_date + timedelta(
                    seconds=random.randint(0, int((end_date - start_date).total_seconds()))
                )
                
                # 生成售票開始時間（演出前1-4週）
                ticket_sale_start = random_date - timedelta(days=random.randint(7, 28))
                
                concert = {
                    "id": i + 1,
                    "name": generate_concert_name(artist, category),
                    "artist": artist,
                    "date": random_date.strftime("%Y-%m-%d"),
                    "time": f"{random.randint(18, 21):02d}:{random.choice(['00', '30'])}",
                    "location": venue,
                    "address": ADDRESSES.get(venue, "地址待確認"),
                    "poster_url": f"/images/concerts/{filename}.webp",
                    "description": f"這是一場精彩的{category}演出，{artist}將為您帶來最震撼的音樂體驗。不容錯過的音樂盛宴，讓我們一起沉浸在美妙的音樂世界中！",
                    "categories": [category] + random.sample([c for c in CATEGORIES if c != category], random.randint(0, 2)),
                    "ticketSaleStart": ticket_sale_start.strftime("%Y-%m-%d %H:%M"),
                    "ticketTypes": generate_ticket_types(),
                    "importantNotes": [
                        "請提前30分鐘入場",
                        "禁止攜帶外食及飲料",
                        "演出期間請勿錄影錄音",
                        "遲到觀眾請於適當時機入場"
                    ],
                    "purchaseInstructions": [
                        "每人限購4張",
                        "購票後不得退換",
                        "請攜帶身分證件入場",
                        "兒童需購票入場"
                    ],
                    "legalNotices": [
                        "主辦單位保留節目異動權利",
                        "如遇不可抗力因素，主辦單位將另行公告",
                        "票券遺失恕不補發"
                    ],
                    "status": random.choice(["upcoming", "on_sale", "on_sale", "on_sale"]),  # 大部分設為 on_sale
                    "featured": random.random() < 0.2  # 20% 機率為精選
                }
                
                concerts.append(concert)
                print(f"🎤 已生成演唱會資料: {concert['name']}")
    
    # 保存演唱會資料
    with open("data/concerts.json", "w", encoding="utf-8") as f:
        json.dump(concerts, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 已生成 {len(concerts)} 場演唱會資料")
    print("📁 資料已保存至 data/concerts.json")
    print("🖼️ 圖片已保存至 images/concerts/ 目錄")

if __name__ == "__main__":
    asyncio.run(main())
