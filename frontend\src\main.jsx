import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { CustomThemeProvider } from './contexts/ThemeContext';
import './index.css';
import App from './App.jsx';

// 在開發模式下載入響應式除錯工具
if (process.env.NODE_ENV === 'development') {
  import('./utils/responsiveDebug.js');
}

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <CustomThemeProvider>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </CustomThemeProvider>
  </StrictMode>,
);