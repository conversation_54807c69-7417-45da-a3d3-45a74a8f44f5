{"data_mtime": 1753691084, "dep_lines": [1, 3, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["typing", "pydantic", "builtins", "_frozen_importlib", "abc", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "9e92cf3db66f171b546cb19d8a2562f67364b4a8", "id": "app.schemas", "ignore_all": false, "interface_hash": "6bbbbb1b6be034a0787b8779a15f9021ef4c22d6", "mtime": 1753691082, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\Repos\\gjunedu-platinum-2025\\pybackend\\src\\app\\schemas.py", "plugin_data": null, "size": 3049, "suppressed": [], "version_id": "1.15.0"}