# Synctix 票務平台系統總覽

## 🎯 專案概述

Synctix 是一個現代化的票務平台，提供完整的演唱會票券購買、管理和後台管理功能。系統採用前後端分離架構，具備響應式設計和深色主題風格。

## 🏗️ 系統架構

### 前端 (React + Vite)
- **框架**: React 18 + Vite
- **UI 庫**: Material-UI (MUI)
- **路由**: React Router
- **狀態管理**: React Context API
- **樣式**: 深色主題 + 毛玻璃效果

### 後端 (FastAPI + Python)
- **框架**: FastAPI
- **資料庫**: 簡易 JSON 檔案系統
- **認證**: 密碼雜湊 (SHA-256)
- **API 文檔**: 自動生成 Swagger UI

## 📁 專案結構

```
ticket-booking-system/
├── frontend/
│   ├── src/
│   │   ├── components/          # 共用組件
│   │   ├── pages/              # 頁面組件
│   │   ├── contexts/           # React Context
│   │   └── App.jsx             # 主應用程式
│   ├── backend/
│   │   ├── src/
│   │   │   ├── main.py         # FastAPI 主程式
│   │   │   ├── database.py     # 簡易資料庫系統
│   │   │   └── init_test_data.py # 測試數據初始化
│   │   └── data/               # JSON 資料檔案
│   └── public/                 # 靜態資源
└── README.md
```

## 🚀 功能特色

### 🎫 用戶功能
- **活動瀏覽**: 分頁顯示、搜尋、分類篩選
- **活動詳情**: 完整活動資訊、票種選擇
- **購票流程**: 
  - 選擇票種和數量
  - 填寫個人資料（會員自動填入）
  - 選擇付款方式
  - 查看購票成功頁面和 QR Code
- **會員系統**: 註冊、登入、個人資料管理
- **訂單管理**: 查看購票記錄、票券狀態

### 🛠️ 管理員功能
- **儀表板**: 統計數據、最近訂單、熱門活動
- **活動管理**: 
  - 新增活動
  - 編輯活動資訊
  - 刪除活動
  - 管理票種和庫存
- **訂單管理**: 查看所有訂單、訂單詳情
- **用戶管理**: 查看註冊用戶列表

### 🎨 設計特色
- **響應式設計**: 適配桌面、平板、手機
- **深色主題**: 統一的視覺風格
- **毛玻璃效果**: 現代化 UI 設計
- **動畫效果**: 平滑的過渡動畫
- **分頁系統**: 高效的數據展示

## 🔧 技術實現

### 前端核心技術
- **AuthContext**: 全域用戶狀態管理
- **路由保護**: 基於用戶角色的頁面訪問控制
- **表單驗證**: 完整的輸入驗證和錯誤處理
- **API 整合**: 統一的後端 API 調用
- **圖片處理**: 自動修復圖片 URL 路徑

### 後端核心技術
- **SimpleJSONDB**: 自製簡易資料庫系統
- **CRUD 操作**: 完整的增刪改查功能
- **數據持久化**: JSON 檔案存儲
- **API 設計**: RESTful API 架構
- **錯誤處理**: 統一的錯誤回應格式

## 📊 資料模型

### 用戶 (User)
```json
{
  "id": "string",
  "email": "string",
  "name": "string", 
  "phone": "string",
  "password": "hashed_string",
  "is_admin": "boolean",
  "created_at": "datetime"
}
```

### 演唱會 (Concert)
```json
{
  "id": "integer",
  "name": "string",
  "artist": "string",
  "date": "string",
  "time": "string",
  "location": "string",
  "address": "string",
  "poster_url": "string",
  "description": "string",
  "categories": ["string"],
  "ticketTypes": [TicketType],
  "status": "string",
  "featured": "boolean"
}
```

### 訂單 (Order)
```json
{
  "id": "string",
  "user_id": "string",
  "concert_id": "integer",
  "concert_name": "string",
  "customer_info": CustomerInfo,
  "items": [OrderItem],
  "total_amount": "integer",
  "status": "string",
  "payment_method": "string",
  "created_at": "datetime",
  "paid_at": "datetime",
  "qr_code": "string"
}
```

## 🔐 測試帳號

### 一般用戶
- **帳號**: <EMAIL>
- **密碼**: 123456
- **說明**: 有購票記錄的測試用戶

### 管理員
- **帳號**: <EMAIL>  
- **密碼**: admin123
- **說明**: 具備完整後台管理權限

## 🚀 啟動方式

### 1. 啟動後端
```bash
cd frontend/backend
python src/main.py
```
後端將在 http://localhost:8000 啟動

### 2. 啟動前端
```bash
cd frontend
npm run dev
```
前端將在 http://localhost:5173 啟動

### 3. 初始化測試數據（可選）
```bash
cd frontend/backend
python src/init_test_data.py
```

## 📋 API 端點

### 公開 API
- `GET /api/concerts` - 獲取演唱會列表
- `GET /api/concerts/{id}` - 獲取演唱會詳情
- `GET /api/categories` - 獲取活動分類
- `POST /api/auth/register` - 用戶註冊
- `POST /api/auth/login` - 用戶登入

### 用戶 API
- `GET /api/users/{id}` - 獲取用戶資料
- `POST /api/orders` - 創建訂單
- `GET /api/orders/{id}` - 獲取訂單詳情
- `POST /api/orders/{id}/payment` - 處理付款

### 管理員 API
- `GET /api/admin/dashboard` - 儀表板數據
- `GET /api/admin/concerts/manage` - 管理活動列表
- `POST /api/admin/concerts` - 創建活動
- `PUT /api/admin/concerts/{id}` - 更新活動
- `DELETE /api/admin/concerts/{id}` - 刪除活動

## 🔄 數據流程

### 購票流程
1. 用戶瀏覽活動 → ExplorePage
2. 查看活動詳情 → EventDetailPage  
3. 選擇票種數量 → PurchasePage
4. 填寫個人資料 → CheckoutPage
5. 選擇付款方式 → PaymentPage
6. 完成購票 → 成功頁面

### 管理流程
1. 管理員登入 → AdminDashboard
2. 查看統計數據 → 儀表板
3. 管理活動 → 活動管理標籤
4. 查看訂單 → 訂單管理標籤
5. 管理用戶 → 用戶管理標籤

## 🎯 未來擴展

系統設計考慮了未來的擴展性：

1. **資料庫升級**: 可輕鬆替換為 PostgreSQL、MySQL 等
2. **認證系統**: 可整合 JWT、OAuth 等
3. **支付系統**: 可接入真實的支付閘道
4. **檔案上傳**: 可添加圖片上傳功能
5. **通知系統**: 可添加郵件、簡訊通知
6. **多語言**: 可擴展國際化支援

## 📞 技術支援

如需技術支援或有任何問題，請聯繫開發團隊。系統已完成所有核心功能的開發和測試，可直接用於演示和進一步開發。
