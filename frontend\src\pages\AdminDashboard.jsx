import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Paper,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Tabs,
  Tab,
  Alert,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Dashboard,
  ConfirmationNumber,
  People,
  AttachMoney,
  Event,
  TrendingUp,
  ExitToApp,
  Visibility
} from '@mui/icons-material';
import ConcertManagementDialog from '../components/ConcertManagementDialog';

function AdminDashboard() {
  const navigate = useNavigate();
  const [currentTab, setCurrentTab] = useState(0);
  const [dashboardData, setDashboardData] = useState(null);
  const [orders, setOrders] = useState([]);
  const [users, setUsers] = useState([]);
  const [concerts, setConcerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showOrderDialog, setShowOrderDialog] = useState(false);
  const [showConcertDialog, setShowConcertDialog] = useState(false);
  const [selectedConcert, setSelectedConcert] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [adminCredentials, setAdminCredentials] = useState({
    email: '<EMAIL>',
    password: 'admin123'
  });
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // 檢查是否已登入管理員
    const adminUser = localStorage.getItem('adminUser');
    if (adminUser) {
      setIsAuthenticated(true);
      fetchDashboardData();
    }
  }, []);

  const handleAdminLogin = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(adminCredentials)
      });

      if (response.ok) {
        const result = await response.json();
        if (result.user.is_admin) {
          localStorage.setItem('adminUser', JSON.stringify(result.user));
          setIsAuthenticated(true);
          fetchDashboardData();
        } else {
          alert('您沒有管理員權限');
        }
      } else {
        alert('登入失敗，請檢查帳號密碼');
      }
    } catch (error) {
      console.error('登入錯誤:', error);
      alert('登入失敗，請稍後再試');
    }
  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // 獲取儀表板數據
      const dashboardResponse = await fetch('http://localhost:8000/api/admin/dashboard');
      const dashboardResult = await dashboardResponse.json();
      setDashboardData(dashboardResult);

      // 獲取訂單數據
      const ordersResponse = await fetch('http://localhost:8000/api/admin/orders');
      const ordersResult = await ordersResponse.json();
      setOrders(ordersResult.orders);

      // 獲取用戶數據
      const usersResponse = await fetch('http://localhost:8000/api/admin/users');
      const usersResult = await usersResponse.json();
      setUsers(usersResult.users);

      // 獲取活動數據
      const concertsResponse = await fetch('http://localhost:8000/api/admin/concerts/manage');
      const concertsResult = await concertsResponse.json();
      setConcerts(concertsResult.concerts);

    } catch (error) {
      console.error('獲取數據錯誤:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('adminUser');
    setIsAuthenticated(false);
    navigate('/');
  };

  const handleViewOrder = (order) => {
    setSelectedOrder(order);
    setShowOrderDialog(true);
  };

  const handleViewConcert = (concert) => {
    setSelectedConcert(concert);
    setIsEditMode(false);
    setShowConcertDialog(true);
  };

  const handleEditConcert = (concert) => {
    setSelectedConcert(concert);
    setIsEditMode(true);
    setShowConcertDialog(true);
  };

  const handleDeleteConcert = async (concertId) => {
    if (window.confirm('確定要刪除此活動嗎？此操作無法復原。')) {
      try {
        const response = await fetch(`http://localhost:8000/api/admin/concerts/${concertId}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          alert('活動刪除成功');
          fetchDashboardData(); // 重新載入數據
        } else {
          const error = await response.json();
          alert(`刪除失敗：${error.detail}`);
        }
      } catch (error) {
        console.error('刪除活動錯誤:', error);
        alert('刪除失敗，請稍後再試');
      }
    }
  };

  const handleSaveConcert = (savedConcert) => {
    // 重新載入數據
    fetchDashboardData();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return '#4caf50';
      case 'pending':
        return '#ff9800';
      case 'cancelled':
        return '#f44336';
      default:
        return '#666';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'paid':
        return '已付款';
      case 'pending':
        return '待付款';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  };

  if (!isAuthenticated) {
    return (
      <Box sx={{
        minHeight: '100vh',
        backgroundColor: '#121212',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Container maxWidth="sm">
          <Paper sx={{
            backgroundColor: 'rgba(36, 36, 36, 0.95)',
            backdropFilter: 'blur(10px)',
            border: '1px solid #444',
            borderRadius: '0',
            padding: '2rem'
          }}>
            <Typography variant="h4" sx={{ color: '#00c0ff', textAlign: 'center', mb: 3 }}>
              管理員登入
            </Typography>
            
            <TextField
              fullWidth
              label="管理員郵箱"
              value={adminCredentials.email}
              onChange={(e) => setAdminCredentials(prev => ({ ...prev, email: e.target.value }))}
              sx={{
                mb: 2,
                '& .MuiOutlinedInput-root': {
                  color: 'white',
                  '& fieldset': { borderColor: '#444' },
                  '&:hover fieldset': { borderColor: '#00c0ff' },
                  '&.Mui-focused fieldset': { borderColor: '#00c0ff' }
                },
                '& .MuiInputLabel-root': {
                  color: '#aaa',
                  '&.Mui-focused': { color: '#00c0ff' }
                }
              }}
            />
            
            <TextField
              fullWidth
              label="密碼"
              type="password"
              value={adminCredentials.password}
              onChange={(e) => setAdminCredentials(prev => ({ ...prev, password: e.target.value }))}
              sx={{
                mb: 3,
                '& .MuiOutlinedInput-root': {
                  color: 'white',
                  '& fieldset': { borderColor: '#444' },
                  '&:hover fieldset': { borderColor: '#00c0ff' },
                  '&.Mui-focused fieldset': { borderColor: '#00c0ff' }
                },
                '& .MuiInputLabel-root': {
                  color: '#aaa',
                  '&.Mui-focused': { color: '#00c0ff' }
                }
              }}
            />
            
            <Button
              fullWidth
              variant="contained"
              onClick={handleAdminLogin}
              sx={{
                backgroundColor: '#00c0ff',
                color: 'black',
                fontWeight: 'bold',
                '&:hover': {
                  backgroundColor: '#64d8ff'
                }
              }}
            >
              登入
            </Button>
            
            <Alert severity="info" sx={{ mt: 2, backgroundColor: 'rgba(33, 150, 243, 0.1)', color: '#64b5f6' }}>
              預設帳號：<EMAIL><br />
              預設密碼：admin123
            </Alert>
          </Paper>
        </Container>
      </Box>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Typography>載入中...</Typography>
      </Box>
    );
  }

  const containerStyle = {
    minHeight: '100vh',
    backgroundColor: '#121212',
    color: 'white',
    paddingY: '2rem'
  };

  const paperStyle = {
    backgroundColor: 'rgba(36, 36, 36, 0.95)',
    backdropFilter: 'blur(10px)',
    border: '1px solid #444',
    borderRadius: '0',
    clipPath: 'polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 15px 100%, 0 calc(100% - 15px))',
    padding: '2rem',
    marginBottom: '2rem'
  };

  return (
    <Box sx={containerStyle}>
      <Container maxWidth="xl">
        {/* 頁面標題 */}
        <Paper elevation={0} sx={paperStyle}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h4" sx={{ color: '#00c0ff', fontWeight: 'bold' }}>
              <Dashboard sx={{ mr: 1, verticalAlign: 'middle' }} />
              管理員儀表板
            </Typography>
            <Button
              variant="outlined"
              onClick={handleLogout}
              sx={{
                color: '#ff6b6b',
                borderColor: '#ff6b6b',
                '&:hover': {
                  backgroundColor: 'rgba(255, 107, 107, 0.1)'
                }
              }}
            >
              <ExitToApp sx={{ mr: 1 }} />
              登出
            </Button>
          </Box>
        </Paper>

        {/* 統計卡片 */}
        {dashboardData && (
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{
                backgroundColor: 'rgba(0, 192, 255, 0.1)',
                border: '1px solid #00c0ff',
                borderRadius: '0'
              }}>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Event sx={{ fontSize: 40, color: '#00c0ff', mb: 1 }} />
                  <Typography variant="h4" sx={{ color: '#00c0ff', fontWeight: 'bold' }}>
                    {dashboardData.stats.total_concerts}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#aaa' }}>
                    總演唱會數
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{
                backgroundColor: 'rgba(76, 175, 80, 0.1)',
                border: '1px solid #4caf50',
                borderRadius: '0'
              }}>
                <CardContent sx={{ textAlign: 'center' }}>
                  <ConfirmationNumber sx={{ fontSize: 40, color: '#4caf50', mb: 1 }} />
                  <Typography variant="h4" sx={{ color: '#4caf50', fontWeight: 'bold' }}>
                    {dashboardData.stats.total_orders}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#aaa' }}>
                    總訂單數
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{
                backgroundColor: 'rgba(255, 193, 7, 0.1)',
                border: '1px solid #ffc107',
                borderRadius: '0'
              }}>
                <CardContent sx={{ textAlign: 'center' }}>
                  <People sx={{ fontSize: 40, color: '#ffc107', mb: 1 }} />
                  <Typography variant="h4" sx={{ color: '#ffc107', fontWeight: 'bold' }}>
                    {dashboardData.stats.total_users}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#aaa' }}>
                    註冊用戶數
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{
                backgroundColor: 'rgba(156, 39, 176, 0.1)',
                border: '1px solid #9c27b0',
                borderRadius: '0'
              }}>
                <CardContent sx={{ textAlign: 'center' }}>
                  <AttachMoney sx={{ fontSize: 40, color: '#9c27b0', mb: 1 }} />
                  <Typography variant="h4" sx={{ color: '#9c27b0', fontWeight: 'bold' }}>
                    {dashboardData.stats.total_revenue.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#aaa' }}>
                    總收入 (NT$)
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* 標籤頁 */}
        <Paper elevation={0} sx={paperStyle}>
          <Tabs
            value={currentTab}
            onChange={(e, newValue) => setCurrentTab(newValue)}
            sx={{
              '& .MuiTab-root': {
                color: '#aaa',
                '&.Mui-selected': {
                  color: '#00c0ff'
                }
              },
              '& .MuiTabs-indicator': {
                backgroundColor: '#00c0ff'
              }
            }}
          >
            <Tab label="最近訂單" />
            <Tab label="用戶管理" />
            <Tab label="活動管理" />
            <Tab label="熱門演唱會" />
          </Tabs>

          <Box sx={{ mt: 3 }}>
            {/* 最近訂單 */}
            {currentTab === 0 && (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                        訂單編號
                      </TableCell>
                      <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                        演唱會
                      </TableCell>
                      <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                        購票人
                      </TableCell>
                      <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                        金額
                      </TableCell>
                      <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                        狀態
                      </TableCell>
                      <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                        操作
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {orders.slice(0, 10).map((order) => (
                      <TableRow key={order.id}>
                        <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                          {order.id.slice(0, 8)}...
                        </TableCell>
                        <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                          {order.concert_name}
                        </TableCell>
                        <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                          {order.customer_info.name}
                        </TableCell>
                        <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                          NT$ {order.total_amount.toLocaleString()}
                        </TableCell>
                        <TableCell sx={{ borderColor: '#444' }}>
                          <Chip
                            label={getStatusText(order.status)}
                            size="small"
                            sx={{
                              backgroundColor: getStatusColor(order.status),
                              color: 'white',
                              fontWeight: 'bold'
                            }}
                          />
                        </TableCell>
                        <TableCell sx={{ borderColor: '#444' }}>
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => handleViewOrder(order)}
                            sx={{
                              color: '#00c0ff',
                              borderColor: '#00c0ff',
                              '&:hover': {
                                backgroundColor: 'rgba(0, 192, 255, 0.1)'
                              }
                            }}
                          >
                            <Visibility sx={{ fontSize: '1rem' }} />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* 用戶管理 */}
            {currentTab === 1 && (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                        用戶編號
                      </TableCell>
                      <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                        姓名
                      </TableCell>
                      <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                        郵箱
                      </TableCell>
                      <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                        手機
                      </TableCell>
                      <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                        註冊時間
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                          {user.id.slice(0, 8)}...
                        </TableCell>
                        <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                          {user.name}
                        </TableCell>
                        <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                          {user.email}
                        </TableCell>
                        <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                          {user.phone}
                        </TableCell>
                        <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                          {new Date(user.created_at).toLocaleDateString('zh-TW')}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* 活動管理 */}
            {currentTab === 2 && (
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6" sx={{ color: '#00c0ff' }}>
                    活動管理
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => {
                      setSelectedConcert(null);
                      setIsEditMode(false);
                      setShowConcertDialog(true);
                    }}
                    sx={{
                      backgroundColor: '#00c0ff',
                      color: 'black',
                      '&:hover': {
                        backgroundColor: '#64d8ff'
                      }
                    }}
                  >
                    新增活動
                  </Button>
                </Box>

                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                          活動名稱
                        </TableCell>
                        <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                          演出者
                        </TableCell>
                        <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                          日期時間
                        </TableCell>
                        <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                          地點
                        </TableCell>
                        <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                          狀態
                        </TableCell>
                        <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                          操作
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {concerts.map((concert) => (
                        <TableRow key={concert.id}>
                          <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                              {concert.name}
                            </Typography>
                          </TableCell>
                          <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                            {concert.artist}
                          </TableCell>
                          <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                            <Typography variant="body2">
                              {concert.date}
                            </Typography>
                            <Typography variant="caption" sx={{ color: '#aaa' }}>
                              {concert.time}
                            </Typography>
                          </TableCell>
                          <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                            {concert.location}
                          </TableCell>
                          <TableCell sx={{ borderColor: '#444' }}>
                            <Chip
                              label={concert.status === 'on_sale' ? '售票中' :
                                    concert.status === 'upcoming' ? '即將開售' :
                                    concert.status === 'sold_out' ? '售完' : '已結束'}
                              size="small"
                              sx={{
                                backgroundColor: concert.status === 'on_sale' ? '#4caf50' :
                                               concert.status === 'upcoming' ? '#ff9800' :
                                               concert.status === 'sold_out' ? '#f44336' : '#666',
                                color: 'white',
                                fontWeight: 'bold'
                              }}
                            />
                          </TableCell>
                          <TableCell sx={{ borderColor: '#444' }}>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Button
                                size="small"
                                variant="outlined"
                                onClick={() => handleViewConcert(concert)}
                                sx={{
                                  color: '#00c0ff',
                                  borderColor: '#00c0ff',
                                  minWidth: 'auto',
                                  '&:hover': {
                                    backgroundColor: 'rgba(0, 192, 255, 0.1)'
                                  }
                                }}
                              >
                                <Visibility sx={{ fontSize: '1rem' }} />
                              </Button>
                              <Button
                                size="small"
                                variant="outlined"
                                onClick={() => handleEditConcert(concert)}
                                sx={{
                                  color: '#ffc107',
                                  borderColor: '#ffc107',
                                  minWidth: 'auto',
                                  '&:hover': {
                                    backgroundColor: 'rgba(255, 193, 7, 0.1)'
                                  }
                                }}
                              >
                                編輯
                              </Button>
                              <Button
                                size="small"
                                variant="outlined"
                                onClick={() => handleDeleteConcert(concert.id)}
                                sx={{
                                  color: '#f44336',
                                  borderColor: '#f44336',
                                  minWidth: 'auto',
                                  '&:hover': {
                                    backgroundColor: 'rgba(244, 67, 54, 0.1)'
                                  }
                                }}
                              >
                                刪除
                              </Button>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}

            {/* 熱門演唱會 */}
            {currentTab === 3 && dashboardData && (
              <Grid container spacing={2}>
                {dashboardData.popular_concerts.map((item, index) => (
                  <Grid item xs={12} sm={6} md={4} key={item.concert.id}>
                    <Card sx={{
                      backgroundColor: 'rgba(60, 60, 60, 0.5)',
                      border: '1px solid #444',
                      borderRadius: '0'
                    }}>
                      <CardContent>
                        <Typography variant="h6" sx={{ color: 'white', mb: 1 }}>
                          #{index + 1} {item.concert.name}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#aaa', mb: 1 }}>
                          演出者：{item.concert.artist}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#00c0ff' }}>
                          訂單數：{item.order_count}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </Paper>

        {/* 訂單詳情對話框 */}
        <Dialog
          open={showOrderDialog}
          onClose={() => setShowOrderDialog(false)}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              backgroundColor: 'rgba(36, 36, 36, 0.95)',
              color: 'white',
              border: '1px solid #444'
            }
          }}
        >
          <DialogTitle sx={{ color: '#00c0ff' }}>
            訂單詳情
          </DialogTitle>
          <DialogContent>
            {selectedOrder && (
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" sx={{ color: '#aaa' }}>訂單編號</Typography>
                  <Typography variant="body1" sx={{ color: 'white', mb: 2 }}>
                    {selectedOrder.id}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" sx={{ color: '#aaa' }}>演唱會</Typography>
                  <Typography variant="body1" sx={{ color: 'white', mb: 2 }}>
                    {selectedOrder.concert_name}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" sx={{ color: '#aaa' }}>購票人</Typography>
                  <Typography variant="body1" sx={{ color: 'white', mb: 2 }}>
                    {selectedOrder.customer_info.name}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" sx={{ color: '#aaa' }}>聯絡郵箱</Typography>
                  <Typography variant="body1" sx={{ color: 'white', mb: 2 }}>
                    {selectedOrder.customer_info.email}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" sx={{ color: '#aaa' }}>手機號碼</Typography>
                  <Typography variant="body1" sx={{ color: 'white', mb: 2 }}>
                    {selectedOrder.customer_info.phone}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" sx={{ color: '#aaa' }}>總金額</Typography>
                  <Typography variant="body1" sx={{ color: 'white', mb: 2 }}>
                    NT$ {selectedOrder.total_amount.toLocaleString()}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2" sx={{ color: '#aaa' }}>票券資訊</Typography>
                  <Typography variant="body1" sx={{ color: 'white' }}>
                    {selectedOrder.items[0].ticket_type_name} × {selectedOrder.items[0].quantity} 張
                  </Typography>
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setShowOrderDialog(false)}
              sx={{ color: '#00c0ff' }}
            >
              關閉
            </Button>
          </DialogActions>
        </Dialog>

        {/* 活動管理對話框 */}
        <ConcertManagementDialog
          open={showConcertDialog}
          onClose={() => setShowConcertDialog(false)}
          concert={selectedConcert}
          isEditMode={isEditMode}
          onSave={handleSaveConcert}
        />
      </Container>
    </Box>
  );
}

export default AdminDashboard;
