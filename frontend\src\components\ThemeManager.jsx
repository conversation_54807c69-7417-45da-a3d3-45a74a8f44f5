import { useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';

const ThemeManager = () => {
  const { isDarkMode } = useTheme();

  useEffect(() => {
    // 更新 body 的類名以應用對應的背景樣式
    document.body.className = isDarkMode ? 'dark-theme' : 'light-theme';
    
    // 清理函數
    return () => {
      document.body.className = '';
    };
  }, [isDarkMode]);

  return null; // 這個組件不渲染任何內容
};

export default ThemeManager;
