/*! For license information please see 9085.93df3ddfd17e1e45d82c.js.LICENSE.txt */
(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[9085],{91033:r=>{function e(r,e,t){switch(t.length){case 0:return r.call(e);case 1:return r.call(e,t[0]);case 2:return r.call(e,t[0],t[1]);case 3:return r.call(e,t[0],t[1],t[2])}return r.apply(e,t)}r.exports=e},83729:r=>{function e(r,e){var t=-1,n=r==null?0:r.length;while(++t<n){if(e(r[t],t,r)===false){break}}return r}r.exports=e},16547:(r,e,t)=>{var n=t(98598),o=t(75288);var a=Object.prototype;var u=a.hasOwnProperty;function c(r,e,t){var a=r[e];if(!(u.call(r,e)&&o(a,t))||t===undefined&&!(e in r)){n(r,e,t)}}r.exports=c},74733:(r,e,t)=>{var n=t(21791),o=t(95950);function a(r,e){return r&&n(e,o(e),r)}r.exports=a},43838:(r,e,t)=>{var n=t(21791),o=t(37241);function a(r,e){return r&&n(e,o(e),r)}r.exports=a},98598:(r,e,t)=>{var n=t(93243);function o(r,e,t){if(e=="__proto__"&&n){n(r,e,{configurable:true,enumerable:true,value:t,writable:true})}else{r[e]=t}}r.exports=o},9999:(r,e,t)=>{var n=t(37217),o=t(83729),a=t(16547),u=t(74733),c=t(43838),i=t(93290),f=t(23007),s=t(92271),l=t(48948),v=t(50002),p=t(83349),b=t(5861),y=t(76189),j=t(77199),x=t(35529),d=t(56449),h=t(3656),w=t(87730),g=t(23805),O=t(38440),_=t(95950),A=t(37241);var S=1,m=2,P=4;var k="[object Arguments]",E="[object Array]",I="[object Boolean]",U="[object Date]",F="[object Error]",C="[object Function]",D="[object GeneratorFunction]",M="[object Map]",R="[object Number]",B="[object Object]",L="[object RegExp]",N="[object Set]",T="[object String]",$="[object Symbol]",V="[object WeakMap]";var W="[object ArrayBuffer]",z="[object DataView]",G="[object Float32Array]",Y="[object Float64Array]",q="[object Int8Array]",H="[object Int16Array]",J="[object Int32Array]",K="[object Uint8Array]",Q="[object Uint8ClampedArray]",X="[object Uint16Array]",Z="[object Uint32Array]";var rr={};rr[k]=rr[E]=rr[W]=rr[z]=rr[I]=rr[U]=rr[G]=rr[Y]=rr[q]=rr[H]=rr[J]=rr[M]=rr[R]=rr[B]=rr[L]=rr[N]=rr[T]=rr[$]=rr[K]=rr[Q]=rr[X]=rr[Z]=true;rr[F]=rr[C]=rr[V]=false;function er(r,e,t,E,I,U){var F,M=e&S,R=e&m,L=e&P;if(t){F=I?t(r,E,I,U):t(r)}if(F!==undefined){return F}if(!g(r)){return r}var N=d(r);if(N){F=y(r);if(!M){return f(r,F)}}else{var T=b(r),$=T==C||T==D;if(h(r)){return i(r,M)}if(T==B||T==k||$&&!I){F=R||$?{}:x(r);if(!M){return R?l(r,c(F,r)):s(r,u(F,r))}}else{if(!rr[T]){return I?r:{}}F=j(r,T,M)}}U||(U=new n);var V=U.get(r);if(V){return V}U.set(r,F);if(O(r)){r.forEach((function(n){F.add(er(n,e,t,n,r,U))}))}else if(w(r)){r.forEach((function(n,o){F.set(o,er(n,e,t,o,r,U))}))}var W=L?R?p:v:R?A:_;var z=N?undefined:W(r);o(z||r,(function(n,o){if(z){o=n;n=r[o]}a(F,o,er(n,e,t,o,r,U))}));return F}r.exports=er},39344:(r,e,t)=>{var n=t(23805);var o=Object.create;var a=function(){function r(){}return function(e){if(!n(e)){return{}}if(o){return o(e)}r.prototype=e;var t=new r;r.prototype=undefined;return t}}();r.exports=a},83120:(r,e,t)=>{var n=t(14528),o=t(45891);function a(r,e,t,u,c){var i=-1,f=r.length;t||(t=o);c||(c=[]);while(++i<f){var s=r[i];if(e>0&&t(s)){if(e>1){a(s,e-1,t,u,c)}else{n(c,s)}}else if(!u){c[c.length]=s}}return c}r.exports=a},20426:r=>{var e=Object.prototype;var t=e.hasOwnProperty;function n(r,e){return r!=null&&t.call(r,e)}r.exports=n},28077:r=>{function e(r,e){return r!=null&&e in Object(r)}r.exports=e},29172:(r,e,t)=>{var n=t(5861),o=t(40346);var a="[object Map]";function u(r){return o(r)&&n(r)==a}r.exports=u},16038:(r,e,t)=>{var n=t(5861),o=t(40346);var a="[object Set]";function u(r){return o(r)&&n(r)==a}r.exports=u},72903:(r,e,t)=>{var n=t(23805),o=t(55527),a=t(90181);var u=Object.prototype;var c=u.hasOwnProperty;function i(r){if(!n(r)){return a(r)}var e=o(r),t=[];for(var u in r){if(!(u=="constructor"&&(e||!c.call(r,u)))){t.push(u)}}return t}r.exports=i},73170:(r,e,t)=>{var n=t(16547),o=t(31769),a=t(30361),u=t(23805),c=t(77797);function i(r,e,t,i){if(!u(r)){return r}e=o(e,r);var f=-1,s=e.length,l=s-1,v=r;while(v!=null&&++f<s){var p=c(e[f]),b=t;if(p==="__proto__"||p==="constructor"||p==="prototype"){return r}if(f!=l){var y=v[p];b=i?i(y,p,v):undefined;if(b===undefined){b=u(y)?y:a(e[f+1])?[]:{}}}n(v,p,b);v=v[p]}return r}r.exports=i},19570:(r,e,t)=>{var n=t(37334),o=t(93243),a=t(83488);var u=!o?a:function(r,e){return o(r,"toString",{configurable:true,enumerable:false,value:n(e),writable:true})};r.exports=u},25160:r=>{function e(r,e,t){var n=-1,o=r.length;if(e<0){e=-e>o?0:o+e}t=t>o?o:t;if(t<0){t+=o}o=e>t?0:t-e>>>0;e>>>=0;var a=Array(o);while(++n<o){a[n]=r[n+e]}return a}r.exports=e},19931:(r,e,t)=>{var n=t(31769),o=t(68090),a=t(68969),u=t(77797);function c(r,e){e=n(e,r);r=a(r,e);return r==null||delete r[u(o(e))]}r.exports=c},49653:(r,e,t)=>{var n=t(37828);function o(r){var e=new r.constructor(r.byteLength);new n(e).set(new n(r));return e}r.exports=o},93290:(r,e,t)=>{r=t.nmd(r);var n=t(9325);var o=true&&e&&!e.nodeType&&e;var a=o&&"object"=="object"&&r&&!r.nodeType&&r;var u=a&&a.exports===o;var c=u?n.Buffer:undefined,i=c?c.allocUnsafe:undefined;function f(r,e){if(e){return r.slice()}var t=r.length,n=i?i(t):new r.constructor(t);r.copy(n);return n}r.exports=f},76169:(r,e,t)=>{var n=t(49653);function o(r,e){var t=e?n(r.buffer):r.buffer;return new r.constructor(t,r.byteOffset,r.byteLength)}r.exports=o},73201:r=>{var e=/\w*$/;function t(r){var t=new r.constructor(r.source,e.exec(r));t.lastIndex=r.lastIndex;return t}r.exports=t},93736:(r,e,t)=>{var n=t(51873);var o=n?n.prototype:undefined,a=o?o.valueOf:undefined;function u(r){return a?Object(a.call(r)):{}}r.exports=u},71961:(r,e,t)=>{var n=t(49653);function o(r,e){var t=e?n(r.buffer):r.buffer;return new r.constructor(t,r.byteOffset,r.length)}r.exports=o},23007:r=>{function e(r,e){var t=-1,n=r.length;e||(e=Array(n));while(++t<n){e[t]=r[t]}return e}r.exports=e},21791:(r,e,t)=>{var n=t(16547),o=t(98598);function a(r,e,t,a){var u=!t;t||(t={});var c=-1,i=e.length;while(++c<i){var f=e[c];var s=a?a(t[f],r[f],f,t,r):undefined;if(s===undefined){s=r[f]}if(u){o(t,f,s)}else{n(t,f,s)}}return t}r.exports=a},92271:(r,e,t)=>{var n=t(21791),o=t(4664);function a(r,e){return n(r,o(r),e)}r.exports=a},48948:(r,e,t)=>{var n=t(21791),o=t(86375);function a(r,e){return n(r,o(r),e)}r.exports=a},53138:(r,e,t)=>{var n=t(11331);function o(r){return n(r)?undefined:r}r.exports=o},93243:(r,e,t)=>{var n=t(56110);var o=function(){try{var r=n(Object,"defineProperty");r({},"",{});return r}catch(e){}}();r.exports=o},38816:(r,e,t)=>{var n=t(35970),o=t(56757),a=t(32865);function u(r){return a(o(r,undefined,n),r+"")}r.exports=u},83349:(r,e,t)=>{var n=t(82199),o=t(86375),a=t(37241);function u(r){return n(r,a,o)}r.exports=u},28879:(r,e,t)=>{var n=t(74335);var o=n(Object.getPrototypeOf,Object);r.exports=o},86375:(r,e,t)=>{var n=t(14528),o=t(28879),a=t(4664),u=t(63345);var c=Object.getOwnPropertySymbols;var i=!c?u:function(r){var e=[];while(r){n(e,a(r));r=o(r)}return e};r.exports=i},49326:(r,e,t)=>{var n=t(31769),o=t(72428),a=t(56449),u=t(30361),c=t(30294),i=t(77797);function f(r,e,t){e=n(e,r);var f=-1,s=e.length,l=false;while(++f<s){var v=i(e[f]);if(!(l=r!=null&&t(r,v))){break}r=r[v]}if(l||++f!=s){return l}s=r==null?0:r.length;return!!s&&c(s)&&u(v,s)&&(a(r)||o(r))}r.exports=f},76189:r=>{var e=Object.prototype;var t=e.hasOwnProperty;function n(r){var e=r.length,n=new r.constructor(e);if(e&&typeof r[0]=="string"&&t.call(r,"index")){n.index=r.index;n.input=r.input}return n}r.exports=n},77199:(r,e,t)=>{var n=t(49653),o=t(76169),a=t(73201),u=t(93736),c=t(71961);var i="[object Boolean]",f="[object Date]",s="[object Map]",l="[object Number]",v="[object RegExp]",p="[object Set]",b="[object String]",y="[object Symbol]";var j="[object ArrayBuffer]",x="[object DataView]",d="[object Float32Array]",h="[object Float64Array]",w="[object Int8Array]",g="[object Int16Array]",O="[object Int32Array]",_="[object Uint8Array]",A="[object Uint8ClampedArray]",S="[object Uint16Array]",m="[object Uint32Array]";function P(r,e,t){var P=r.constructor;switch(e){case j:return n(r);case i:case f:return new P(+r);case x:return o(r,t);case d:case h:case w:case g:case O:case _:case A:case S:case m:return c(r,t);case s:return new P;case l:case b:return new P(r);case v:return a(r);case p:return new P;case y:return u(r)}}r.exports=P},35529:(r,e,t)=>{var n=t(39344),o=t(28879),a=t(55527);function u(r){return typeof r.constructor=="function"&&!a(r)?n(o(r)):{}}r.exports=u},45891:(r,e,t)=>{var n=t(51873),o=t(72428),a=t(56449);var u=n?n.isConcatSpreadable:undefined;function c(r){return a(r)||o(r)||!!(u&&r&&r[u])}r.exports=c},90181:r=>{function e(r){var e=[];if(r!=null){for(var t in Object(r)){e.push(t)}}return e}r.exports=e},56757:(r,e,t)=>{var n=t(91033);var o=Math.max;function a(r,e,t){e=o(e===undefined?r.length-1:e,0);return function(){var a=arguments,u=-1,c=o(a.length-e,0),i=Array(c);while(++u<c){i[u]=a[e+u]}u=-1;var f=Array(e+1);while(++u<e){f[u]=a[u]}f[e]=t(i);return n(r,this,f)}}r.exports=a},68969:(r,e,t)=>{var n=t(47422),o=t(25160);function a(r,e){return e.length<2?r:n(r,o(e,0,-1))}r.exports=a},32865:(r,e,t)=>{var n=t(19570),o=t(51811);var a=o(n);r.exports=a},51811:r=>{var e=800,t=16;var n=Date.now;function o(r){var o=0,a=0;return function(){var u=n(),c=t-(u-a);a=u;if(c>0){if(++o>=e){return arguments[0]}}else{o=0}return r.apply(undefined,arguments)}}r.exports=o},88055:(r,e,t)=>{var n=t(9999);var o=1,a=4;function u(r){return n(r,o|a)}r.exports=u},37334:r=>{function e(r){return function(){return r}}r.exports=e},35970:(r,e,t)=>{var n=t(83120);function o(r){var e=r==null?0:r.length;return e?n(r,1):[]}r.exports=o},61448:(r,e,t)=>{var n=t(20426),o=t(49326);function a(r,e){return r!=null&&o(r,e,n)}r.exports=a},80631:(r,e,t)=>{var n=t(28077),o=t(49326);function a(r,e){return r!=null&&o(r,e,n)}r.exports=a},83488:r=>{function e(r){return r}r.exports=e},62193:(r,e,t)=>{var n=t(88984),o=t(5861),a=t(72428),u=t(56449),c=t(64894),i=t(3656),f=t(55527),s=t(37167);var l="[object Map]",v="[object Set]";var p=Object.prototype;var b=p.hasOwnProperty;function y(r){if(r==null){return true}if(c(r)&&(u(r)||typeof r=="string"||typeof r.splice=="function"||i(r)||s(r)||a(r))){return!r.length}var e=o(r);if(e==l||e==v){return!r.size}if(f(r)){return!n(r).length}for(var t in r){if(b.call(r,t)){return false}}return true}r.exports=y},87730:(r,e,t)=>{var n=t(29172),o=t(27301),a=t(86009);var u=a&&a.isMap;var c=u?o(u):n;r.exports=c},11331:(r,e,t)=>{var n=t(72552),o=t(28879),a=t(40346);var u="[object Object]";var c=Function.prototype,i=Object.prototype;var f=c.toString;var s=i.hasOwnProperty;var l=f.call(Object);function v(r){if(!a(r)||n(r)!=u){return false}var e=o(r);if(e===null){return true}var t=s.call(e,"constructor")&&e.constructor;return typeof t=="function"&&t instanceof t&&f.call(t)==l}r.exports=v},38440:(r,e,t)=>{var n=t(16038),o=t(27301),a=t(86009);var u=a&&a.isSet;var c=u?o(u):n;r.exports=c},37241:(r,e,t)=>{var n=t(70695),o=t(72903),a=t(64894);function u(r){return a(r)?n(r,true):o(r)}r.exports=u},68090:r=>{function e(r){var e=r==null?0:r.length;return e?r[e-1]:undefined}r.exports=e},90179:(r,e,t)=>{var n=t(34932),o=t(9999),a=t(19931),u=t(31769),c=t(21791),i=t(53138),f=t(38816),s=t(83349);var l=1,v=2,p=4;var b=f((function(r,e){var t={};if(r==null){return t}var f=false;e=n(e,(function(e){e=u(e,r);f||(f=e.length>1);return e}));c(r,s(r),t);if(f){t=o(t,l|v|p,i)}var b=e.length;while(b--){a(t,e[b])}return t}));r.exports=b},63560:(r,e,t)=>{var n=t(73170);function o(r,e,t){return r==null?r:n(r,e,t)}r.exports=o},42072:(r,e,t)=>{var n=t(34932),o=t(23007),a=t(56449),u=t(44394),c=t(39421),i=t(77797),f=t(13222);function s(r){if(a(r)){return n(r,i)}return u(r)?[r]:o(c(f(r)))}r.exports=s},21020:(r,e,t)=>{"use strict";var n=t(44914),o=Symbol.for("react.element"),a=Symbol.for("react.fragment"),u=Object.prototype.hasOwnProperty,c=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};function f(r,e,t){var n,a={},f=null,s=null;void 0!==t&&(f=""+t);void 0!==e.key&&(f=""+e.key);void 0!==e.ref&&(s=e.ref);for(n in e)u.call(e,n)&&!i.hasOwnProperty(n)&&(a[n]=e[n]);if(r&&r.defaultProps)for(n in e=r.defaultProps,e)void 0===a[n]&&(a[n]=e[n]);return{$$typeof:o,type:r,key:f,ref:s,props:a,_owner:c.current}}e.Fragment=a;e.jsx=f;e.jsxs=f},74848:(r,e,t)=>{"use strict";if(true){r.exports=t(21020)}else{}}}]);