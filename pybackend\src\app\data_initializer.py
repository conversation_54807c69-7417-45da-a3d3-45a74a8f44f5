"""
資料初始化模組
用於初始化演唱會、用戶、訂單等測試數據
"""

import hashlib
import json
import os
from datetime import datetime, timedelta
from .json_database import get_json_database

def initialize_synctix_data(base_dir: str):
    """初始化 Synctix 票務系統數據"""
    db = get_json_database()
    
    print("開始初始化 Synctix 票務系統數據...")
    
    # 載入演唱會資料
    if db.count("concerts") == 0:
        print("正在初始化演唱會資料...")
        concerts_file = os.path.join(base_dir, "data", "concerts.json")
        if os.path.exists(concerts_file):
            with open(concerts_file, "r", encoding="utf-8") as f:
                concerts = json.load(f)
            for concert in concerts:
                db.insert("concerts", concert)
            print(f"載入了 {len(concerts)} 個演唱會資料")

    # 載入海報資料
    if db.count("hero_banners") == 0:
        print("正在初始化海報資料...")
        banners_file = os.path.join(base_dir, "data", "hero_banners.json")
        if os.path.exists(banners_file):
            with open(banners_file, "r", encoding="utf-8") as f:
                banners = json.load(f)
            for banner in banners:
                db.insert("hero_banners", banner)
            print(f"載入了 {len(banners)} 個海報資料")

    # 初始化管理員用戶
    if db.count("users", {"email": "<EMAIL>"}) == 0:
        admin_user = {
            "id": "admin-001",
            "email": "<EMAIL>",
            "password": hashlib.sha256("admin123".encode()).hexdigest(),
            "name": "系統管理員",
            "phone": "0900-000-000",
            "is_admin": True
        }
        db.insert("users", admin_user)
        print("創建管理員帳號")

    # 添加測試用戶
    test_users = [
        {
            "id": "user-001",
            "email": "<EMAIL>",
            "password": hashlib.sha256("123456".encode()).hexdigest(),
            "name": "張小明",
            "phone": "0912-345-678",
            "is_admin": False
        },
        {
            "id": "user-002", 
            "email": "<EMAIL>",
            "password": hashlib.sha256("123456".encode()).hexdigest(),
            "name": "李小華",
            "phone": "0923-456-789",
            "is_admin": False
        },
        {
            "id": "user-003",
            "email": "<EMAIL>", 
            "password": hashlib.sha256("123456".encode()).hexdigest(),
            "name": "王小美",
            "phone": "0934-567-890",
            "is_admin": False
        }
    ]
    
    for user in test_users:
        # 檢查用戶是否已存在
        existing = db.get_by_field("users", "email", user["email"])
        if not existing:
            db.insert("users", user)
            print(f"添加測試用戶: {user['name']} ({user['email']})")

    # 添加測試訂單
    test_orders = [
        {
            "id": "order-001",
            "user_id": "user-001",
            "concert_id": 1,
            "concert_name": "電子音樂製作人 2024 經典重現演唱會",
            "customer_info": {
                "name": "張小明",
                "email": "<EMAIL>",
                "phone": "0912-345-678"
            },
            "items": [{
                "ticket_type_id": 2,
                "ticket_type_name": "VIP搖滾區",
                "price": 3807,
                "quantity": 2
            }],
            "total_amount": 7614,
            "status": "paid",
            "payment_method": "credit_card",
            "paid_at": (datetime.now() - timedelta(days=5)).isoformat(),
            "qr_code": "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=SYNCTIX-order-001"
        },
        {
            "id": "order-002",
            "user_id": "user-001",
            "concert_id": 2,
            "concert_name": "流行音樂節",
            "customer_info": {
                "name": "張小明",
                "email": "<EMAIL>",
                "phone": "0912-345-678"
            },
            "items": [{
                "ticket_type_id": 1,
                "ticket_type_name": "一般票",
                "price": 1500,
                "quantity": 1
            }],
            "total_amount": 1500,
            "status": "paid",
            "payment_method": "bank_transfer",
            "paid_at": (datetime.now() - timedelta(days=2)).isoformat(),
            "qr_code": "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=SYNCTIX-order-002"
        },
        {
            "id": "order-003",
            "user_id": "user-002",
            "concert_id": 3,
            "concert_name": "搖滾音樂節",
            "customer_info": {
                "name": "李小華",
                "email": "<EMAIL>", 
                "phone": "0923-456-789"
            },
            "items": [{
                "ticket_type_id": 3,
                "ticket_type_name": "學生票",
                "price": 1200,
                "quantity": 1
            }],
            "total_amount": 1200,
            "status": "pending",
            "payment_method": "",
            "paid_at": None,
            "qr_code": None
        }
    ]
    
    for order in test_orders:
        # 檢查訂單是否已存在
        existing = db.get_by_id("orders", order["id"])
        if not existing:
            db.insert("orders", order)
            print(f"添加測試訂單: {order['id']} - {order['concert_name']}")

    # 統計資訊
    total_users = db.count("users")
    total_orders = db.count("orders")
    total_concerts = db.count("concerts")
    total_banners = db.count("hero_banners")
    
    print(f"\nSynctix 數據初始化完成！")
    print(f"用戶數量: {total_users}")
    print(f"訂單數量: {total_orders}")
    print(f"演唱會數量: {total_concerts}")
    print(f"海報數量: {total_banners}")
    print(f"\n測試帳號:")
    print(f"一般用戶: <EMAIL> / 123456")
    print(f"一般用戶: <EMAIL> / 123456") 
    print(f"一般用戶: <EMAIL> / 123456")
    print(f"管理員: <EMAIL> / admin123")

def get_all_categories() -> list:
    """獲取所有音樂類別"""
    db = get_json_database()
    concerts = db.get_all("concerts")
    categories = set()
    
    for concert in concerts:
        for category in concert.get("categories", []):
            categories.add(category)
    
    return sorted(list(categories))
