<!DOCTYPE HTML>
<html>

<head>

    <meta charset="utf-8">

    <title>{% block title %}Jupyter Server{% endblock %}</title>
    {% block favicon %}<link id="favicon" rel="shortcut icon" type="image/x-icon" href="{{ static_url("favicon.ico") }}">
    {% endblock %}
    <link rel="stylesheet" href="{{static_url("style/bootstrap.min.css") }}" />
    <link rel="stylesheet" href="{{static_url("style/bootstrap-theme.min.css") }}" />
    <link rel="stylesheet" href="{{static_url("style/index.css") }}" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    {% block stylesheet %}
    {% endblock stylesheet %}

    {% block meta %}
    {% endblock meta %}

</head>

<body class="{% block bodyclasses %}{% endblock %}" {% block params %} {% if logged_in and token %}
  data-jupyter-api-token="{{token | urlencode}}" {% endif %} {% endblock params %} dir="ltr">

  <noscript>
    <div id='noscript'>
      {% trans %}Jupyter Server requires JavaScript.{% endtrans %}<br>
      {% trans %}Please enable it to proceed. {% endtrans %}
    </div>
  </noscript>

  <div id="header" role="navigation" aria-label="{% trans %}Top Menu{% endtrans %}">
    <div id="header-container" class="container">
      <div id="jupyter_server" class="nav navbar-brand"><a href="{{default_url}}" title='{% trans %}dashboard{% endtrans %}'>
          {% block logo %}<img src='{{static_url("logo/logo.png") }}' alt='Jupyter Server' />{% endblock %}
        </a></div>

      {% block headercontainer %}
      {% endblock headercontainer %}

      {% block header_buttons %}
      {% endblock header_buttons %}

    </div>
    <div class="header-bar"></div>

    {% block header %}
    {% endblock header %}
  </div>

  <div id="site">
    {% block site %}
    {% endblock site %}
  </div>

  {% block after_site %}
  {% endblock after_site %}

  {% block script %}
  {% endblock script %}

  <script type='text/javascript'>
    function _remove_token_from_url() {
      if (window.location.search.length <= 1) {
        return;
      }
      var search_parameters = window.location.search.slice(1).split('&');
      for (var i = 0; i < search_parameters.length; i++) {
        if (search_parameters[i].split('=')[0] === 'token') {
          // remote token from search parameters
          search_parameters.splice(i, 1);
          var new_search = '';
          if (search_parameters.length) {
            new_search = '?' + search_parameters.join('&');
          }
          var new_url = window.location.origin +
            window.location.pathname +
            new_search +
            window.location.hash;
          window.history.replaceState({}, "", new_url);
          return;
        }
      }
    }
    _remove_token_from_url();
  </script>
</body>

</html>
