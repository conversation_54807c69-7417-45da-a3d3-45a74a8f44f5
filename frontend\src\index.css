/* Import Google Font */
@import url('https://fonts.googleapis.com/css2?family=Exo+2:wght@300;400;500;600;700&display=swap');

:root {
  font-family: 'Exo 2', Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #0a0a0a;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* � 全局樣式重置 - 移除所有邊框和輪廓 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 移除大部分元素的邊框，但保留表單元素 */
*:not(input):not(textarea):not(select):not(.MuiInput-underline):not(.MuiInput-underline::before):not(.MuiInput-underline::after),
*:not(input):not(textarea):not(select):not(.MuiInput-underline):not(.MuiInput-underline::before):not(.MuiInput-underline::after)::before,
*:not(input):not(textarea):not(select):not(.MuiInput-underline):not(.MuiInput-underline::before):not(.MuiInput-underline::after)::after {
  border: none !important;
  outline: none !important;
}

/* 移除 body 和 html 的邊框 */
html,
body {
  border: none !important;
  outline: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* �🎨 明日方舟風格動畫關鍵幀 */
@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(100px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes geometryRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

/* 🎨 明日方舟風格深色模式背景 - 更深色調 */
body.dark-theme {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;

  /* 基礎背景層 - 更深的色調 */
  background-color: #050505;
  background-image:
    linear-gradient(135deg, rgba(5, 5, 5, 0.95) 0%, rgba(15, 15, 25, 0.9) 25%, rgba(10, 15, 30, 0.85) 50%, rgba(8, 20, 40, 0.8) 75%, rgba(12, 25, 60, 0.75) 100%),
    url('/background/backgroung-black.webp');
  background-attachment: fixed, fixed;
  background-size: 100% 100%, cover;
  background-position: center, center;
  background-repeat: no-repeat, no-repeat;
  color: rgba(255, 255, 255, 0.87);
}

/* 🎨 深色模式動態背景層 */
body.dark-theme::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;

  background:
    /* 科技光暈效果 */
    radial-gradient(ellipse at 20% 30%, rgba(0, 192, 255, 0.15) 0%, transparent 60%),
    radial-gradient(ellipse at 80% 70%, rgba(255, 107, 53, 0.12) 0%, transparent 60%),
    radial-gradient(ellipse at 50% 0%, rgba(138, 43, 226, 0.08) 0%, transparent 70%);

  background-size: 1200px 1200px, 800px 800px, 600px 600px;
  background-position: center, center, center;
  background-repeat: no-repeat, no-repeat, no-repeat;
}

/* 🎨 明日方舟風格淺色模式背景 - 更淺色調 */
body.light-theme {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;

  /* 基礎背景層 - 更淺的色調 */
  background-color: #fafbfc;
  background-image:
    linear-gradient(135deg, rgba(250, 251, 252, 0.95) 0%, rgba(245, 248, 252, 0.9) 25%, rgba(240, 245, 250, 0.85) 50%, rgba(235, 240, 248, 0.8) 75%, rgba(230, 235, 245, 0.75) 100%),
    url('/background/backgroung-white.webp');
  background-attachment: fixed, fixed;
  background-size: 100% 100%, cover;
  background-position: center, center;
  background-repeat: no-repeat, no-repeat;
  color: rgba(0, 0, 0, 0.87);
}

/* 🎨 淺色模式動態背景層 */
body.light-theme::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;

  background:
    /* 科技光暈效果 */
    radial-gradient(ellipse at 20% 30%, rgba(25, 118, 210, 0.08) 0%, transparent 60%),
    radial-gradient(ellipse at 80% 70%, rgba(220, 0, 78, 0.06) 0%, transparent 60%),
    radial-gradient(ellipse at 50% 0%, rgba(156, 39, 176, 0.05) 0%, transparent 70%);

  background-size: 1200px 1200px, 800px 800px, 600px 600px;
  background-position: center, center, center;
  background-repeat: no-repeat, no-repeat, no-repeat;
}

/* 🎨 預設為明日方舟風格深色模式 */
body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;

  /* 基礎背景層 */
  background:
    linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #1e3a8a 100%),
    url('/background/backgroung-black.webp');
  background-attachment: fixed, fixed;
  background-size: 100% 100%, cover;
  background-position: center, center;
  background-repeat: no-repeat, no-repeat;
  transition: background 0.5s ease;
  color: rgba(255, 255, 255, 0.87);
}

/* 🎨 預設動態背景層 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;

  background:
    /* 科技光暈效果 */
    radial-gradient(ellipse at 20% 30%, rgba(0, 192, 255, 0.15) 0%, transparent 60%),
    radial-gradient(ellipse at 80% 70%, rgba(255, 107, 53, 0.12) 0%, transparent 60%),
    radial-gradient(ellipse at 50% 0%, rgba(138, 43, 226, 0.08) 0%, transparent 70%);

  background-size: 1200px 1200px, 800px 800px, 600px 600px;
  background-position: center, center, center;
  background-repeat: no-repeat, no-repeat, no-repeat;
}

#root {
  min-height: 100vh;
  max-width: 100%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

/* 🎨 明日方舟風格容器透明度設定 */
.MuiContainer-root,
.MuiBox-root:not(.hero-banner) {
  background: transparent;
}

/* 🎨 增強卡片效果 */
.MuiCard-root {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* 🎨 科技風格按鈕效果 */
.MuiButton-contained {
  position: relative;
  overflow: hidden;
}

.MuiButton-contained::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.MuiButton-contained:hover::before {
  left: 100%;
}

/* 🎨 移動設備優化 */
@media (max-width: 768px) {
  /* 減少動畫效果以提升性能 */
  body.dark-theme::before,
  body.light-theme::before,
  body::before {
    background-size: 800px 800px, 600px 600px, 400px 400px;
  }

  /* 簡化背景效果 */
  .MuiButton-contained::before {
    display: none;
  }
}

@media (prefers-reduced-motion: reduce) {
  /* 尊重用戶的動畫偏好設定 */
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 科技風格滾動條 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #00c0ff, #64d8ff);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #64d8ff, #00c0ff);
}

/* 基本連結樣式 */
a {
  font-weight: 500;
  color: #00c0ff;
  text-decoration: inherit;
  transition: color 0.25s;
}

a:hover {
  color: #64d8ff;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
}

/* 🔍 搜索框樣式修復 - 確保底線顯示 */
.MuiTextField-root .MuiInput-underline::before,
.MuiInput-underline::before {
  border-bottom: 1px solid rgba(255, 255, 255, 0.42) !important;
  border-left: none !important;
  border-right: none !important;
  border-top: none !important;
}

.MuiTextField-root .MuiInput-underline::after,
.MuiInput-underline::after {
  border-bottom: 2px solid #00c0ff !important;
  border-left: none !important;
  border-right: none !important;
  border-top: none !important;
}

.MuiTextField-root .MuiInput-underline:hover:not(.Mui-disabled)::before,
.MuiInput-underline:hover:not(.Mui-disabled)::before {
  border-bottom: 2px solid rgba(255, 255, 255, 0.87) !important;
  border-left: none !important;
  border-right: none !important;
  border-top: none !important;
}

/* 淺色模式下的搜索框樣式 */
.light-theme .MuiTextField-root .MuiInput-underline::before,
.light-theme .MuiInput-underline::before {
  border-bottom: 1px solid rgba(0, 0, 0, 0.42) !important;
  border-left: none !important;
  border-right: none !important;
  border-top: none !important;
}

.light-theme .MuiTextField-root .MuiInput-underline:hover:not(.Mui-disabled)::before,
.light-theme .MuiInput-underline:hover:not(.Mui-disabled)::before {
  border-bottom: 2px solid rgba(0, 0, 0, 0.87) !important;
  border-left: none !important;
  border-right: none !important;
  border-top: none !important;
}

/* 🎨 Banner 響應式優化 - 修正垂直排列重疊問題 */
@media (max-width: 899px) {
  .hero-banner {
    min-height: 580px !important; /* 確保垂直排列有足夠空間 */
  }

  /* 確保按鈕不會與底部內容重疊 */
  .hero-banner .MuiButton-root {
    margin-bottom: 16px !important;
  }
}

/* 🎨 筆電螢幕優化 */
@media (min-width: 900px) and (max-width: 1440px) and (max-height: 900px) {
  .hero-banner {
    min-height: 500px !important;
  }
}

/* 🎨 小型筆電優化 (13" 及以下) */
@media (min-width: 900px) and (max-width: 1366px) and (max-height: 768px) {
  .hero-banner {
    min-height: 480px !important;
    max-height: 550px !important;
  }
}