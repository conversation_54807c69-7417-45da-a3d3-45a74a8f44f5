import React, { useEffect, useRef, useState } from 'react';
import { Box } from '@mui/material';
import { useTheme } from '../contexts/ThemeContext';

// 🎨 明日方舟風格動態背景組件
const ArknightsBackground = () => {
  const { isDarkMode } = useTheme();
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const particlesRef = useRef([]);
  const geometryRef = useRef([]);

  // 粒子類別
  class Particle {
    constructor(canvas) {
      this.canvas = canvas;
      this.reset();
      this.y = Math.random() * canvas.height;
    }

    reset() {
      this.x = Math.random() * this.canvas.width;
      this.y = this.canvas.height + 10;
      this.size = Math.random() * 3 + 1;
      this.speedY = Math.random() * 2 + 0.5;
      this.speedX = (Math.random() - 0.5) * 0.5;
      this.opacity = Math.random() * 0.5 + 0.2;
      this.life = 1;
      this.decay = Math.random() * 0.01 + 0.005;
    }

    update() {
      this.y -= this.speedY;
      this.x += this.speedX;
      this.life -= this.decay;
      
      if (this.y < -10 || this.life <= 0) {
        this.reset();
      }
    }

    draw(ctx) {
      ctx.save();
      ctx.globalAlpha = this.opacity * this.life;
      ctx.fillStyle = isDarkMode ? '#00c0ff' : '#1976d2';
      ctx.beginPath();
      ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
      ctx.fill();
      ctx.restore();
    }
  }

  // 幾何圖形類別
  class GeometryShape {
    constructor(canvas) {
      this.canvas = canvas;
      this.x = Math.random() * canvas.width;
      this.y = Math.random() * canvas.height;
      this.size = Math.random() * 20 + 10;
      this.rotation = 0;
      this.rotationSpeed = (Math.random() - 0.5) * 0.02;
      this.opacity = Math.random() * 0.3 + 0.1;
      this.type = Math.floor(Math.random() * 3); // 0: 圓形, 1: 三角形, 2: 六邊形
    }

    update() {
      this.rotation += this.rotationSpeed;
    }

    draw(ctx) {
      ctx.save();
      ctx.globalAlpha = this.opacity;
      ctx.strokeStyle = isDarkMode ? '#00c0ff' : '#1976d2';
      ctx.lineWidth = 1;
      ctx.translate(this.x, this.y);
      ctx.rotate(this.rotation);

      ctx.beginPath();
      switch (this.type) {
        case 0: // 圓形
          ctx.arc(0, 0, this.size, 0, Math.PI * 2);
          break;
        case 1: // 三角形
          ctx.moveTo(0, -this.size);
          ctx.lineTo(-this.size * 0.866, this.size * 0.5);
          ctx.lineTo(this.size * 0.866, this.size * 0.5);
          ctx.closePath();
          break;
        case 2: // 六邊形
          for (let i = 0; i < 6; i++) {
            const angle = (i * Math.PI) / 3;
            const x = Math.cos(angle) * this.size;
            const y = Math.sin(angle) * this.size;
            if (i === 0) ctx.moveTo(x, y);
            else ctx.lineTo(x, y);
          }
          ctx.closePath();
          break;
      }
      ctx.stroke();
      ctx.restore();
    }
  }

  // 初始化畫布
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // 初始化粒子
    particlesRef.current = [];
    for (let i = 0; i < 50; i++) {
      particlesRef.current.push(new Particle(canvas));
    }

    // 初始化幾何圖形
    geometryRef.current = [];
    for (let i = 0; i < 15; i++) {
      geometryRef.current.push(new GeometryShape(canvas));
    }

    return () => {
      window.removeEventListener('resize', resizeCanvas);
    };
  }, []);

  // 動畫循環
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 更新和繪製粒子
      particlesRef.current.forEach(particle => {
        particle.update();
        particle.draw(ctx);
      });

      // 更新和繪製幾何圖形
      geometryRef.current.forEach(shape => {
        shape.update();
        shape.draw(ctx);
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isDarkMode]);

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: -2,
        pointerEvents: 'none',
      }}
    >
      <canvas
        ref={canvasRef}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
        }}
      />
      
      {/* 🎨 明日方舟風格裝飾元素 */}

      {/* 旋轉圓環 */}
      <Box
        sx={{
          position: 'absolute',
          top: '10%',
          right: '10%',
          width: '200px',
          height: '200px',
          backgroundImage: 'url(/assets/circle_01.png)',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          opacity: isDarkMode ? 0.6 : 0.4,
          animation: 'geometryRotate 30s linear infinite',
        }}
      />

      {/* 十字架裝飾 */}
      <Box
        sx={{
          position: 'absolute',
          bottom: '20%',
          left: '8%',
          width: '80px',
          height: '80px',
          backgroundImage: 'url(/assets/cross.png)',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          opacity: isDarkMode ? 0.5 : 0.3,
          animation: 'pulseGlow 4s ease-in-out infinite',
        }}
      />

      {/* 浮動粒子裝飾 */}
      <Box
        sx={{
          position: 'absolute',
          top: '30%',
          right: '20%',
          width: '60px',
          height: '60px',
          backgroundImage: 'url(/assets/firefly.png)',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          opacity: isDarkMode ? 0.7 : 0.5,
          animation: 'particleFloat 8s ease-in-out infinite',
        }}
      />

      {/* 科技圓環 2 */}
      <Box
        sx={{
          position: 'absolute',
          top: '60%',
          left: '5%',
          width: '120px',
          height: '120px',
          backgroundImage: 'url(/assets/circle_03.png)',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          opacity: isDarkMode ? 0.4 : 0.3,
          animation: 'geometryRotate 25s linear infinite reverse',
        }}
      />

      {/* 小型裝飾圓環 */}
      <Box
        sx={{
          position: 'absolute',
          top: '80%',
          right: '30%',
          width: '80px',
          height: '80px',
          backgroundImage: 'url(/assets/circle_05.png)',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          opacity: isDarkMode ? 0.6 : 0.4,
          animation: 'pulseGlow 6s ease-in-out infinite',
        }}
      />
    </Box>
  );
};

export default ArknightsBackground;
