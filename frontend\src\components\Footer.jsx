import React from 'react';
import {
  Box,
  Container,
  Grid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  IconButt<PERSON>,
  Divider
} from '@mui/material';
import FacebookIcon from '@mui/icons-material/Facebook';
import InstagramIcon from '@mui/icons-material/Instagram';
import TwitterIcon from '@mui/icons-material/Twitter';
import YouTubeIcon from '@mui/icons-material/YouTube';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';

const Footer = () => {
  const footerStyle = {
    backgroundColor: 'rgba(26, 26, 26, 0.95)',
    backdropFilter: 'blur(10px)',
    borderTop: '1px solid rgba(0, 192, 255, 0.3)',
    color: 'rgba(255, 255, 255, 0.87)',
    mt: 8,
    pt: 6,
    pb: 4,
    position: 'relative',
    zIndex: 10
  };

  const linkStyle = {
    color: 'rgba(255, 255, 255, 0.7)',
    textDecoration: 'none',
    transition: 'color 0.3s ease',
    '&:hover': {
      color: '#00c0ff',
      textDecoration: 'none'
    }
  };

  const sectionTitleStyle = {
    color: '#00c0ff',
    fontWeight: 'bold',
    mb: 2,
    fontSize: '1.1rem'
  };

  const socialIconStyle = {
    color: 'rgba(255, 255, 255, 0.7)',
    transition: 'color 0.3s ease, transform 0.3s ease',
    '&:hover': {
      color: '#00c0ff',
      transform: 'translateY(-2px)'
    }
  };

  return (
    <Box sx={footerStyle}>
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* 關於我們 */}
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="h6" sx={sectionTitleStyle}>
              關於我們
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Link href="#" sx={linkStyle}>
                公司簡介
              </Link>
              <Link href="#" sx={linkStyle}>
                服務條款
              </Link>
              <Link href="#" sx={linkStyle}>
                隱私政策
              </Link>
              <Link href="#" sx={linkStyle}>
                下載 App
              </Link>
            </Box>
          </Grid>

          {/* 客戶服務 */}
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="h6" sx={sectionTitleStyle}>
              客戶服務
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Link href="#" sx={linkStyle}>
                主辦單位登錄
              </Link>
              <Link href="#" sx={linkStyle}>
                票券驗證服務
              </Link>
              <Link href="#" sx={linkStyle}>
                安全購票
              </Link>
              <Link href="#" sx={linkStyle}>
                常見問題
              </Link>
            </Box>
          </Grid>

          {/* 聯絡 SYNCTIX */}
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="h6" sx={sectionTitleStyle}>
              聯絡 SYNCTIX
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <EmailIcon sx={{ fontSize: '1rem', color: '#00c0ff' }} />
                <Link href="mailto:<EMAIL>" sx={linkStyle}>
                  客服信箱
                </Link>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PhoneIcon sx={{ fontSize: '1rem', color: '#00c0ff' }} />
                <Link href="tel:+886-2-1234-5678" sx={linkStyle}>
                  客服專線
                </Link>
              </Box>
              <Link href="#" sx={linkStyle}>
                進入 SYNCTIX
              </Link>
              <Link href="#" sx={linkStyle}>
                合作提案
              </Link>
            </Box>
          </Grid>

          {/* 追蹤 SYNCTIX */}
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="h6" sx={sectionTitleStyle}>
              追蹤 SYNCTIX
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton sx={socialIconStyle}>
                  <FacebookIcon />
                </IconButton>
                <IconButton sx={socialIconStyle}>
                  <InstagramIcon />
                </IconButton>
                <IconButton sx={socialIconStyle}>
                  <TwitterIcon />
                </IconButton>
                <IconButton sx={socialIconStyle}>
                  <YouTubeIcon />
                </IconButton>
              </Box>
              <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.6)' }}>
                關注我們獲取最新活動資訊
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* 分隔線 */}
        <Divider sx={{ 
          my: 4, 
          borderColor: 'rgba(0, 192, 255, 0.2)',
          background: 'linear-gradient(90deg, transparent, rgba(0, 192, 255, 0.3), transparent)'
        }} />

        {/* 版權資訊 */}
        <Box sx={{ 
          display: 'flex', 
          flexDirection: { xs: 'column', md: 'row' },
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: 2
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box
              component="img"
              src="/icon/icon.png"
              alt="Synctix Logo"
              sx={{
                height: 32,
                width: 32,
                filter: 'brightness(0) invert(1)'
              }}
            />
            <Typography variant="h6" sx={{ 
              color: '#00c0ff', 
              fontWeight: 'bold',
              fontFamily: 'monospace'
            }}>
              SYNCTIX
            </Typography>
          </Box>
          
          <Typography variant="body2" sx={{ 
            color: 'rgba(255, 255, 255, 0.6)',
            textAlign: { xs: 'center', md: 'right' }
          }}>
            © 2024 SYNCTIX 票務平台 | 由 WALLONG INTERNET ENTERTAINMENT LIMITED 提供技術支援
          </Typography>
        </Box>

        {/* 語言選擇 */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center',
          gap: 2,
          mt: 3,
          pt: 2,
          borderTop: '1px solid rgba(255, 255, 255, 0.1)'
        }}>
          <Link href="#" sx={{ ...linkStyle, fontSize: '0.875rem' }}>
            繁體中文
          </Link>
          <Typography sx={{ color: 'rgba(255, 255, 255, 0.3)' }}>|</Typography>
          <Link href="#" sx={{ ...linkStyle, fontSize: '0.875rem' }}>
            English
          </Link>
          <Typography sx={{ color: 'rgba(255, 255, 255, 0.3)' }}>|</Typography>
          <Link href="#" sx={{ ...linkStyle, fontSize: '0.875rem' }}>
            日本語
          </Link>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
