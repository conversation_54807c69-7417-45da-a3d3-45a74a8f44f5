Metadata-Version: 2.1
Name: jupyterlab_pygments
Version: 0.3.0
Summary: Pygments theme using JupyterLab CSS variables
Project-URL: Homepage, https://github.com/jupyterlab/jupyterlab_pygments
Project-URL: Bug Tracker, https://github.com/jupyterlab/jupyterlab_pygments/issues
Project-URL: Repository, https://github.com/jupyterlab/jupyterlab_pygments.git
Author-email: Jupyter Development Team <<EMAIL>>
License: Copyright (c) 2015 Project Jupyter Contributors
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
        1. Redistributions of source code must retain the above copyright notice, this
           list of conditions and the following disclaimer.
        
        2. Redistributions in binary form must reproduce the above copyright notice,
           this list of conditions and the following disclaimer in the documentation
           and/or other materials provided with the distribution.
        
        3. Neither the name of the copyright holder nor the names of its
           contributors may be used to endorse or promote products derived from
           this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
        AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
        IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
        DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
        SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
License-File: LICENSE
Classifier: Framework :: Jupyter
Classifier: Framework :: Jupyter :: JupyterLab
Classifier: Framework :: Jupyter :: JupyterLab :: 4
Classifier: Framework :: Jupyter :: JupyterLab :: Extensions
Classifier: Framework :: Jupyter :: JupyterLab :: Extensions :: Prebuilt
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Python: >=3.8
Description-Content-Type: text/markdown

# JupyterLab Pygments Theme

This package contains a syntax coloring theme for [pygments](http://pygments.org/) making use of
the JupyterLab CSS variables.

The goal is to enable the use of JupyterLab's themes with pygments-generated HTML.

## Screencast

In the following screencast, we demonstrate how Pygments-highlighted code can make use of the JupyterLab theme.

![pygments screencast](pygments.gif)

## Installation

`jupyterlab_pygments` can be installed with the conda package manager

```
conda install -c conda-forge jupyterlab_pygments
```

or from pypi

```
pip install jupyterlab_pygments
```

## Dependencies

- `jupyterlab_pygments` requires [pygments](http://pygments.org) version `2.4.1`.
- The CSS variables used by the theme correspond to the CodeMirror syntex coloring
  theme defined in the NPM package [@jupyterlab/codemirror](https://www.npmjs.com/package/@jupyterlab/codemirror). Supported versions for `@jupyterlab/codemirror`'s CSS include `0.19.1`, `^1.0`, and, `^2.0`.

## Limitations

Pygments-generated HTML and CSS classes are not granular enough to reproduce
all of the details of codemirror (the JavaScript text editor used by JupyterLab).

This includes the ability to differentiate properties from general names.

## License

`jupyterlab_pygments` uses a shared copyright model that enables all contributors to maintain the
copyright on their contributions. All code is licensed under the terms of the revised [BSD license](LICENSE).
