{"title": "Text Editor", "description": "Text editor settings.", "jupyter.lab.setting-icon": "ui-components:text-editor", "jupyter.lab.setting-icon-label": "Editor", "jupyter.lab.menus": {"main": [{"id": "jp-mainmenu-file", "items": [{"type": "submenu", "submenu": {"id": "jp-mainmenu-file-new", "items": [{"command": "fileeditor:create-new", "rank": 30}, {"command": "fileeditor:create-new-markdown-file", "rank": 30}]}}]}, {"id": "jp-mainmenu-view", "items": [{"type": "separator", "rank": 40}, {"type": "submenu", "submenu": {"id": "jp-mainmenu-view-codemirror-language", "label": "Text Editor Syntax Highlighting"}, "rank": 40}, {"type": "separator", "rank": 40}]}, {"id": "jp-mainmenu-settings", "items": [{"type": "separator", "rank": 30}, {"type": "submenu", "submenu": {"id": "jp-mainmenu-settings-fileeditorindent", "label": "Text Editor Indentation", "items": [{"command": "fileeditor:change-tabs"}, {"command": "fileeditor:change-tabs", "args": {"size": "1"}}, {"command": "fileeditor:change-tabs", "args": {"size": "2"}}, {"command": "fileeditor:change-tabs", "args": {"size": "4"}}, {"command": "fileeditor:change-tabs", "args": {"size": "8"}}]}, "rank": 30}, {"command": "fileeditor:toggle-autoclosing-brackets-universal", "rank": 30}, {"command": "fileeditor:change-font-size", "rank": 30, "args": {"delta": 1, "isMenu": true}}, {"command": "fileeditor:change-font-size", "rank": 30, "args": {"delta": -1, "isMenu": true}}, {"type": "submenu", "submenu": {"id": "jp-mainmenu-settings-codemirror-theme", "label": "Text Editor Theme", "items": []}, "rank": 31}, {"type": "separator", "rank": 39}]}], "context": [{"command": "fileeditor:undo", "selector": ".jp-FileEditor", "rank": 1}, {"command": "fileeditor:redo", "selector": ".jp-FileEditor", "rank": 2}, {"command": "fileeditor:cut", "selector": ".jp-FileEditor", "rank": 3}, {"command": "fileeditor:copy", "selector": ".jp-FileEditor", "rank": 4}, {"command": "fileeditor:paste", "selector": ".jp-FileEditor", "rank": 5}, {"command": "fileeditor:select-all", "selector": ".jp-FileEditor", "rank": 6}, {"command": "fileeditor:create-console", "selector": ".jp-FileEditor", "rank": 10}, {"command": "fileeditor:markdown-preview", "selector": ".jp-FileEditor", "rank": 11}]}, "jupyter.lab.toolbars": {"Editor": []}, "jupyter.lab.transform": true, "properties": {"editorConfig": {"title": "Editor Configuration", "description": "The configuration for all text editors; it will override the CodeMirror default configuration.\nIf `fontFamily`, `fontSize` or `lineHeight` are `null`,\nvalues from current theme are used.", "type": "object", "default": {"lineNumbers": true}}, "scrollPastEnd": {"title": "Scroll behavior", "description": "Whether to scroll past the end of text document.", "type": "boolean", "default": true}, "toolbar": {"title": "Text editor toolbar items", "description": "Note: To disable a toolbar item,\ncopy it to User Preferences and add the\n\"disabled\" key. Toolbar description:", "items": {"$ref": "#/definitions/toolbarItem"}, "type": "array", "default": []}}, "additionalProperties": false, "type": "object", "definitions": {"toolbarItem": {"properties": {"name": {"title": "Unique name", "type": "string"}, "args": {"title": "Command arguments", "type": "object"}, "command": {"title": "Command id", "type": "string", "default": ""}, "disabled": {"title": "Whether the item is ignored or not", "type": "boolean", "default": false}, "icon": {"title": "Item icon id", "description": "If defined, it will override the command icon", "type": "string"}, "label": {"title": "Item label", "description": "If defined, it will override the command label", "type": "string"}, "caption": {"title": "Item caption", "description": "If defined, it will override the command caption", "type": "string"}, "type": {"title": "Item type", "type": "string", "enum": ["command", "spacer"]}, "rank": {"title": "Item rank", "type": "number", "minimum": 0, "default": 50}}, "required": ["name"], "additionalProperties": false, "type": "object"}}}