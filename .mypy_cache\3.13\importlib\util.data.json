{".class": "MypyFile", "_fullname": "importlib.util", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LazyLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["importlib._abc.Loader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "importlib.util.LazyLoader", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "importlib.util.LazyLoader", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "importlib.util", "mro": ["importlib.util.LazyLoader", "importlib._abc.Loader", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "loader"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.util.LazyLoader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "loader"], "arg_types": ["importlib.util.LazyLoader", "importlib._abc.Loader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LazyLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exec_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.util.LazyLoader.exec_module", "name": "exec_module", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "module"], "arg_types": ["importlib.util.LazyLoader", "types.ModuleType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exec_module of LazyLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "loader"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "importlib.util.LazyLoader.factory", "name": "factory", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "loader"], "arg_types": [{".class": "TypeType", "item": "importlib.util.LazyLoader"}, "importlib._abc.Loader"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "factory of LazyLoader", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "importlib.util.LazyLoader", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "importlib.util.LazyLoader.factory", "name": "factory", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "loader"], "arg_types": [{".class": "TypeType", "item": "importlib.util.LazyLoader"}, "importlib._abc.Loader"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "factory of LazyLoader", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "importlib.util.LazyLoader", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "importlib.util.LazyLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "importlib.util.LazyLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MAGIC_NUMBER": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.MAGIC_NUMBER", "kind": "Gdef"}, "ParamSpec": {".class": "SymbolTableNode", "cross_ref": "typing.ParamSpec", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_P": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "ParamSpecExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "importlib.util._P", "name": "_P", "upper_bound": "builtins.object", "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.util.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.util.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.util.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.util.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.util.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.util.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cache_from_source": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.cache_from_source", "kind": "Gdef"}, "decode_source": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.decode_source", "kind": "Gdef"}, "find_spec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["name", "package"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.util.find_spec", "name": "find_spec", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["name", "package"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_spec", "ret_type": {".class": "UnionType", "items": ["_frozen_importlib.ModuleSpec", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef", "module_hidden": true, "module_public": false}, "module_from_spec": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib.module_from_spec", "kind": "Gdef"}, "resolve_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["name", "package"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.util.resolve_name", "name": "resolve_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["name", "package"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "source_from_cache": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.source_from_cache", "kind": "Gdef"}, "source_hash": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["source_bytes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.util.source_hash", "name": "source_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["source_bytes"], "arg_types": ["_collections_abc.<PERSON><PERSON>er"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "source_hash", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "spec_from_file_location": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.spec_from_file_location", "kind": "Gdef"}, "spec_from_loader": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib.spec_from_loader", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\importlib\\util.pyi"}