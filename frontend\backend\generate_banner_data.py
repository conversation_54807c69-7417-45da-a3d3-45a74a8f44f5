#!/usr/bin/env python3
"""
生成主頁海報輪播資料
"""

import json
import random
from datetime import datetime, timedelta

def generate_hero_banners(concerts):
    """從演唱會資料中選擇精選演出作為主頁海報"""
    
    # 選擇精選演出或隨機選擇
    featured_concerts = [c for c in concerts if c.get('featured', False)]
    if len(featured_concerts) < 5:
        # 如果精選演出不足，隨機補充
        non_featured = [c for c in concerts if not c.get('featured', False)]
        additional = random.sample(non_featured, min(5 - len(featured_concerts), len(non_featured)))
        featured_concerts.extend(additional)
    
    # 限制為5個海報
    selected_concerts = featured_concerts[:5]
    
    banners = []
    for i, concert in enumerate(selected_concerts):
        banner = {
            "id": i + 1,
            "title": concert["name"],
            "subtitle": f"{concert['artist']} 演唱會",
            "description": concert["description"][:100] + "..." if len(concert["description"]) > 100 else concert["description"],
            "image": concert["poster_url"],
            "date": f"{concert['date']} {concert['time']}",
            "location": concert["location"],
            "concertId": concert["id"]
        }
        banners.append(banner)
    
    return banners

def main():
    """主函數"""
    # 讀取演唱會資料
    try:
        with open("data/concerts.json", "r", encoding="utf-8") as f:
            concerts = json.load(f)
    except FileNotFoundError:
        print("❌ 請先執行 generate_concert_data.py 生成演唱會資料")
        return
    
    # 生成海報資料
    banners = generate_hero_banners(concerts)
    
    # 保存海報資料
    with open("data/hero_banners.json", "w", encoding="utf-8") as f:
        json.dump(banners, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 已生成 {len(banners)} 個主頁海報")
    print("📁 資料已保存至 data/hero_banners.json")

if __name__ == "__main__":
    main()
