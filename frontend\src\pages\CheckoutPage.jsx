import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Card,
  CardContent,
  Divider,
  Stepper,
  Step,
  StepLabel,
  Alert
} from '@mui/material';
import {
  Person,
  Email,
  Phone,
  ArrowBack,
  ArrowForward,
  ConfirmationNumber
} from '@mui/icons-material';

function CheckoutPage() {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();

  const [orderData, setOrderData] = useState(null);
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    email: '',
    phone: ''
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (location.state) {
      setOrderData(location.state);
    } else {
      // 如果沒有訂單資料，返回首頁
      navigate('/');
    }
  }, [location.state, navigate]);

  // 自動填入會員資料
  useEffect(() => {
    if (isAuthenticated() && user) {
      setCustomerInfo({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || ''
      });
    }
  }, [user, isAuthenticated]);

  const handleInputChange = (field) => (event) => {
    setCustomerInfo(prev => ({
      ...prev,
      [field]: event.target.value
    }));
    // 清除該欄位的錯誤
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!customerInfo.name.trim()) {
      newErrors.name = '請輸入姓名';
    }

    if (!customerInfo.email.trim()) {
      newErrors.email = '請輸入電子郵件';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customerInfo.email)) {
      newErrors.email = '請輸入有效的電子郵件格式';
    }

    if (!customerInfo.phone.trim()) {
      newErrors.phone = '請輸入手機號碼';
    } else if (!/^09\d{8}$/.test(customerInfo.phone.replace(/[-\s]/g, ''))) {
      newErrors.phone = '請輸入有效的手機號碼格式（09xxxxxxxx）';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleProceedToPayment = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    
    try {
      // 創建訂單
      const orderRequest = {
        concert_id: orderData.event.id,
        ticket_type_id: orderData.ticketType.id,
        quantity: orderData.quantity,
        customer_info: customerInfo
      };

      // 如果用戶已登入，添加用戶 ID
      if (isAuthenticated() && user) {
        orderRequest.user_id = user.id;
      }

      const response = await fetch('http://localhost:8000/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderRequest)
      });

      if (!response.ok) {
        throw new Error('創建訂單失敗');
      }

      const result = await response.json();
      
      // 導向付款頁面
      navigate('/payment', { 
        state: { 
          order: result.order,
          customerInfo 
        } 
      });
      
    } catch (error) {
      console.error('創建訂單錯誤:', error);
      setErrors({ submit: '創建訂單失敗，請稍後再試' });
    } finally {
      setLoading(false);
    }
  };

  if (!orderData) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Typography>載入中...</Typography>
      </Box>
    );
  }

  const containerStyle = {
    minHeight: '100vh',
    backgroundColor: '#121212',
    color: 'white',
    paddingY: '2rem'
  };

  const paperStyle = {
    backgroundColor: 'rgba(36, 36, 36, 0.95)',
    backdropFilter: 'blur(10px)',
    border: '1px solid #444',
    borderRadius: '0',
    clipPath: 'polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 15px 100%, 0 calc(100% - 15px))',
    padding: '2rem',
    marginBottom: '2rem'
  };

  const textFieldStyle = {
    '& .MuiOutlinedInput-root': {
      color: 'white',
      '& fieldset': {
        borderColor: '#444'
      },
      '&:hover fieldset': {
        borderColor: '#00c0ff'
      },
      '&.Mui-focused fieldset': {
        borderColor: '#00c0ff'
      }
    },
    '& .MuiInputLabel-root': {
      color: '#aaa',
      '&.Mui-focused': {
        color: '#00c0ff'
      }
    }
  };

  const steps = ['選擇票種', '填寫資料', '付款', '完成'];

  return (
    <Box sx={containerStyle}>
      <Container maxWidth="lg">
        {/* 進度指示器 */}
        <Paper elevation={0} sx={paperStyle}>
          <Stepper activeStep={1} alternativeLabel sx={{
            '& .MuiStepLabel-label': { color: '#aaa' },
            '& .MuiStepLabel-label.Mui-active': { color: '#00c0ff' },
            '& .MuiStepLabel-label.Mui-completed': { color: '#4caf50' },
            '& .MuiStepIcon-root': { color: '#444' },
            '& .MuiStepIcon-root.Mui-active': { color: '#00c0ff' },
            '& .MuiStepIcon-root.Mui-completed': { color: '#4caf50' }
          }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Paper>

        <Grid container spacing={4}>
          {/* 左側：訂單摘要 */}
          <Grid item xs={12} md={4}>
            <Paper elevation={0} sx={paperStyle}>
              <Typography variant="h6" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 2 }}>
                <ConfirmationNumber sx={{ mr: 1, verticalAlign: 'middle' }} />
                訂單摘要
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <img 
                  src={orderData.event.poster_url} 
                  alt={orderData.event.name}
                  style={{
                    width: '100%',
                    height: 'auto',
                    clipPath: 'polygon(0 0, 100% 0, 100% calc(100% - 15px), calc(100% - 15px) 100%, 0 100%)'
                  }}
                />
              </Box>

              <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold', mb: 1 }}>
                {orderData.event.name}
              </Typography>
              <Typography variant="body2" sx={{ color: '#aaa', mb: 1 }}>
                演出者：{orderData.event.artist}
              </Typography>
              <Typography variant="body2" sx={{ color: '#aaa', mb: 1 }}>
                日期：{orderData.event.date} {orderData.event.time}
              </Typography>
              <Typography variant="body2" sx={{ color: '#aaa', mb: 2 }}>
                地點：{orderData.event.location}
              </Typography>

              <Divider sx={{ backgroundColor: '#444', my: 2 }} />

              <Typography variant="body2" sx={{ color: '#aaa', mb: 1 }}>
                票種：{orderData.ticketType.name}
              </Typography>
              <Typography variant="body2" sx={{ color: '#aaa', mb: 1 }}>
                數量：{orderData.quantity} 張
              </Typography>
              <Typography variant="body2" sx={{ color: '#aaa', mb: 1 }}>
                單價：NT$ {orderData.ticketType.price.toLocaleString()}
              </Typography>
              
              <Divider sx={{ backgroundColor: '#444', my: 2 }} />
              
              <Typography variant="h6" sx={{ color: '#00c0ff', fontWeight: 'bold' }}>
                總金額：NT$ {orderData.totalAmount.toLocaleString()}
              </Typography>
            </Paper>
          </Grid>

          {/* 右側：個人資料表單 */}
          <Grid item xs={12} md={8}>
            <Paper elevation={0} sx={paperStyle}>
              <Typography variant="h5" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 3 }}>
                <Person sx={{ mr: 1, verticalAlign: 'middle' }} />
                填寫購票資料
              </Typography>

              {errors.submit && (
                <Alert severity="error" sx={{ mb: 3, backgroundColor: 'rgba(244, 67, 54, 0.1)', color: '#ff6b6b' }}>
                  {errors.submit}
                </Alert>
              )}

              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="姓名"
                    value={customerInfo.name}
                    onChange={handleInputChange('name')}
                    error={!!errors.name}
                    helperText={errors.name}
                    sx={textFieldStyle}
                    InputProps={{
                      startAdornment: <Person sx={{ color: '#00c0ff', mr: 1 }} />
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="電子郵件"
                    type="email"
                    value={customerInfo.email}
                    onChange={handleInputChange('email')}
                    error={!!errors.email}
                    helperText={errors.email}
                    sx={textFieldStyle}
                    InputProps={{
                      startAdornment: <Email sx={{ color: '#00c0ff', mr: 1 }} />
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="手機號碼"
                    value={customerInfo.phone}
                    onChange={handleInputChange('phone')}
                    error={!!errors.phone}
                    helperText={errors.phone || '格式：09xxxxxxxx'}
                    sx={textFieldStyle}
                    InputProps={{
                      startAdornment: <Phone sx={{ color: '#00c0ff', mr: 1 }} />
                    }}
                  />
                </Grid>
              </Grid>

              <Divider sx={{ backgroundColor: '#444', my: 3 }} />

              {/* 操作按鈕 */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  onClick={() => navigate(-1)}
                  sx={{
                    color: '#aaa',
                    borderColor: '#aaa',
                    '&:hover': {
                      backgroundColor: 'rgba(170, 170, 170, 0.1)',
                      borderColor: '#ccc'
                    }
                  }}
                >
                  <ArrowBack sx={{ mr: 1 }} />
                  返回上一步
                </Button>

                <Button
                  variant="contained"
                  onClick={handleProceedToPayment}
                  disabled={loading}
                  sx={{
                    backgroundColor: '#00c0ff',
                    color: 'black',
                    fontWeight: 'bold',
                    padding: '10px 30px',
                    clipPath: 'polygon(10% 0, 100% 0, 90% 100%, 0% 100%)',
                    '&:hover': {
                      backgroundColor: '#64d8ff',
                      boxShadow: '0 0 20px rgba(0, 192, 255, 0.5)'
                    },
                    '&:disabled': {
                      backgroundColor: '#555',
                      color: '#888'
                    }
                  }}
                >
                  {loading ? '處理中...' : '前往付款'}
                  <ArrowForward sx={{ ml: 1 }} />
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}

export default CheckoutPage;
