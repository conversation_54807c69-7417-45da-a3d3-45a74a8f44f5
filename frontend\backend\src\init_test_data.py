"""
初始化測試數據腳本
用於添加測試用戶、訂單等數據
"""

import hashlib
from datetime import datetime, timedelta
from database import init_database
import os

def init_test_data():
    """初始化測試數據"""
    # 初始化資料庫
    current_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.dirname(current_dir)
    db = init_database(base_dir)
    
    print("開始初始化測試數據...")
    
    # 添加測試用戶
    test_users = [
        {
            "id": "user-001",
            "email": "<EMAIL>",
            "password": hashlib.sha256("123456".encode()).hexdigest(),
            "name": "張小明",
            "phone": "0912-345-678",
            "is_admin": False
        },
        {
            "id": "user-002", 
            "email": "<EMAIL>",
            "password": hashlib.sha256("123456".encode()).hexdigest(),
            "name": "李小華",
            "phone": "0923-456-789",
            "is_admin": False
        },
        {
            "id": "user-003",
            "email": "<EMAIL>", 
            "password": hashlib.sha256("123456".encode()).hexdigest(),
            "name": "王小美",
            "phone": "0934-567-890",
            "is_admin": False
        }
    ]
    
    for user in test_users:
        # 檢查用戶是否已存在
        existing = db.get_by_field("users", "email", user["email"])
        if not existing:
            db.insert("users", user)
            print(f"添加測試用戶: {user['name']} ({user['email']})")
    
    # 添加測試訂單
    test_orders = [
        {
            "id": "order-001",
            "user_id": "user-001",
            "concert_id": 1,
            "concert_name": "電子音樂製作人 2024 經典重現演唱會",
            "customer_info": {
                "name": "張小明",
                "email": "<EMAIL>",
                "phone": "0912-345-678"
            },
            "items": [{
                "ticket_type_id": 2,
                "ticket_type_name": "VIP搖滾區",
                "price": 3807,
                "quantity": 2
            }],
            "total_amount": 7614,
            "status": "paid",
            "payment_method": "credit_card",
            "paid_at": (datetime.now() - timedelta(days=5)).isoformat(),
            "qr_code": "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=SYNCTIX-order-001"
        },
        {
            "id": "order-002",
            "user_id": "user-001", 
            "concert_id": 2,
            "concert_name": "台北流行音樂節",
            "customer_info": {
                "name": "張小明",
                "email": "<EMAIL>",
                "phone": "0912-345-678"
            },
            "items": [{
                "ticket_type_id": 1,
                "ticket_type_name": "一般座位",
                "price": 1800,
                "quantity": 1
            }],
            "total_amount": 1800,
            "status": "paid",
            "payment_method": "line_pay",
            "paid_at": (datetime.now() - timedelta(days=10)).isoformat(),
            "qr_code": "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=SYNCTIX-order-002"
        },
        {
            "id": "order-003",
            "user_id": "user-002",
            "concert_id": 3,
            "concert_name": "搖滾音樂節",
            "customer_info": {
                "name": "李小華",
                "email": "<EMAIL>", 
                "phone": "0923-456-789"
            },
            "items": [{
                "ticket_type_id": 3,
                "ticket_type_name": "學生票",
                "price": 1200,
                "quantity": 1
            }],
            "total_amount": 1200,
            "status": "pending",
            "payment_method": "",
            "paid_at": None,
            "qr_code": None
        }
    ]
    
    for order in test_orders:
        # 檢查訂單是否已存在
        existing = db.get_by_id("orders", order["id"])
        if not existing:
            db.insert("orders", order)
            print(f"添加測試訂單: {order['id']} - {order['concert_name']}")
    
    # 統計資訊
    total_users = db.count("users")
    total_orders = db.count("orders")
    total_concerts = db.count("concerts")
    
    print(f"\n測試數據初始化完成！")
    print(f"用戶數量: {total_users}")
    print(f"訂單數量: {total_orders}")
    print(f"演唱會數量: {total_concerts}")
    print(f"\n測試帳號:")
    print(f"一般用戶: <EMAIL> / 123456")
    print(f"一般用戶: <EMAIL> / 123456") 
    print(f"一般用戶: <EMAIL> / 123456")
    print(f"管理員: <EMAIL> / admin123")

if __name__ == "__main__":
    init_test_data()
