<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard</title>
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        background-color: #eef2f7;
        margin: 0;
        padding: 20px;
        box-sizing: border-box;
        color: #333;
      }
      #dashboard-container {
        background-color: #ffffff;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        text-align: center;
        width: 100%;
        max-width: 500px;
        box-sizing: border-box;
      }
      h1 {
        color: #2c3e50;
        margin-bottom: 20px;
        font-size: 2.5em;
      }
      p {
        font-size: 1.2em;
        margin-bottom: 30px;
        color: #555;
      }
      button {
        background-color: #dc3545;
        color: white;
        padding: 12px 25px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 1.1em;
        transition: background-color 0.3s ease;
        width: 100%;
        max-width: 200px;
        box-sizing: border-box;
      }
      button:hover {
        background-color: #c82333;
      }
    </style>
  </head>
  <body>
    <div id="dashboard-container">
      <h1>歡迎！</h1>
      <p>您已成功登入。</p>
      <button id="logoutButton">登出</button>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const token = localStorage.getItem("access_token");
        if (!token) {
          // 如果沒有 token，導回登入頁面
          window.location.href = "/";
        }

        document
          .getElementById("logoutButton")
          .addEventListener("click", () => {
            // 清除 token
            localStorage.removeItem("access_token");
            // 導回登入頁面
            window.location.href = "/";
          });
      });
    </script>
  </body>
</html>
