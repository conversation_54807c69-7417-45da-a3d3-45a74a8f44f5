#!/usr/bin/env python3
"""
背景圖片轉換腳本
將背景圖片轉換為 webp 格式以提升效能
"""

import os
from PIL import Image
import glob

# 背景圖片目錄
BACKGROUND_DIR = "../public/background"
WEBP_QUALITY = 90  # 背景圖片使用較高品質

def convert_image_to_webp(input_path, output_path):
    """將圖片轉換為 webp 格式"""
    try:
        print(f"正在轉換: {os.path.basename(input_path)}")
        
        with Image.open(input_path) as img:
            # 轉換為 RGB 模式（webp 不支援某些模式）
            if img.mode in ('RGBA', 'LA', 'P'):
                # 對於背景圖片，如果有透明度，保持 RGBA 模式
                if img.mode == 'RGBA':
                    # 保持 RGBA 模式以支援透明度
                    pass
                elif img.mode == 'P':
                    img = img.convert('RGBA')
                else:
                    # 創建白色背景
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'RGBA':
                        background.paste(img, mask=img.split()[-1])
                    else:
                        background.paste(img)
                    img = background
            elif img.mode != 'RGB' and img.mode != 'RGBA':
                img = img.convert('RGB')
            
            # 保存為 webp
            img.save(output_path, 'WEBP', quality=WEBP_QUALITY, optimize=True)
        
        print(f"✅ 轉換完成: {os.path.basename(output_path)}")
        return True
        
    except Exception as e:
        print(f"❌ 轉換失敗 {input_path}: {str(e)}")
        return False

def main():
    """主函數"""
    print("🚀 開始轉換背景圖片...")
    
    # 檢查目錄是否存在
    if not os.path.exists(BACKGROUND_DIR):
        print(f"❌ 背景圖片目錄不存在: {BACKGROUND_DIR}")
        return
    
    # 查找所有 PNG 圖片
    png_files = glob.glob(os.path.join(BACKGROUND_DIR, "*.png"))
    
    if not png_files:
        print("❌ 沒有找到 PNG 背景圖片")
        return
    
    print(f"找到 {len(png_files)} 個 PNG 檔案")
    
    converted_count = 0
    mapping = {}
    
    for png_file in png_files:
        # 生成 webp 檔案名
        base_name = os.path.splitext(os.path.basename(png_file))[0]
        webp_file = os.path.join(BACKGROUND_DIR, f"{base_name}.webp")
        
        # 轉換圖片
        if convert_image_to_webp(png_file, webp_file):
            converted_count += 1
            # 記錄映射關係
            original_path = f"/background/{os.path.basename(png_file)}"
            webp_path = f"/background/{base_name}.webp"
            mapping[original_path] = webp_path
    
    print(f"\n✨ 完成！成功轉換 {converted_count} 張背景圖片")
    
    # 顯示映射結果
    if mapping:
        print("\n🔗 檔案映射:")
        for original, webp in mapping.items():
            print(f"  {original} -> {webp}")
        
        # 保存映射到文件
        import json
        mapping_file = os.path.join(BACKGROUND_DIR, "background_mapping.json")
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(mapping, f, ensure_ascii=False, indent=2)
        print(f"\n📋 映射已保存到: {mapping_file}")

if __name__ == "__main__":
    main()
