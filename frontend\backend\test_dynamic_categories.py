#!/usr/bin/env python3
"""
測試動態分類管理功能
"""

import json
import requests

def test_categories_api():
    """測試分類 API"""
    print("🧪 測試分類 API...")
    
    try:
        response = requests.get("http://localhost:8000/api/categories")
        response.raise_for_status()
        data = response.json()
        
        print(f"✅ 分類 API 正常運作")
        print(f"📊 總分類數量: {data['total']}")
        print(f"🏷️  所有分類: {', '.join(data['categories'][:10])}..." if len(data['categories']) > 10 else f"🏷️  所有分類: {', '.join(data['categories'])}")
        
        return data['categories']
        
    except Exception as e:
        print(f"❌ 分類 API 測試失敗: {e}")
        return []

def test_category_filtering(categories):
    """測試分類篩選功能"""
    print("\n🔍 測試分類篩選功能...")
    
    # 測試幾個分類
    test_categories = categories[:5] if len(categories) >= 5 else categories
    
    for category in test_categories:
        try:
            # URL 編碼中文
            import urllib.parse
            encoded_category = urllib.parse.quote(category)
            
            response = requests.get(f"http://localhost:8000/api/concerts?category={encoded_category}&limit=3")
            response.raise_for_status()
            data = response.json()
            
            print(f"✅ 分類 '{category}' 篩選成功，找到 {data['total']} 個演唱會")
            
            # 驗證返回的演唱會確實包含該分類
            for concert in data['concerts']:
                if category not in concert['categories']:
                    print(f"❌ 錯誤：演唱會 '{concert['name']}' 不包含分類 '{category}'")
                    return False
                    
        except Exception as e:
            print(f"❌ 分類 '{category}' 篩選失敗: {e}")
            return False
    
    print("✅ 所有分類篩選測試通過")
    return True

def test_concerts_count():
    """測試演唱會總數"""
    print("\n📊 測試演唱會總數...")
    
    try:
        response = requests.get("http://localhost:8000/api/concerts?limit=1")
        response.raise_for_status()
        data = response.json()
        
        total_concerts = data['total']
        print(f"✅ 總演唱會數量: {total_concerts}")
        
        if total_concerts >= 292:
            print("✅ 演唱會數量符合預期（≥292）")
            return True
        else:
            print(f"❌ 演唱會數量不足，預期 ≥292，實際 {total_concerts}")
            return False
            
    except Exception as e:
        print(f"❌ 演唱會總數測試失敗: {e}")
        return False

def test_search_functionality():
    """測試搜尋功能"""
    print("\n🔍 測試搜尋功能...")
    
    try:
        # 測試搜尋藝人
        response = requests.get("http://localhost:8000/api/search?q=電子&limit=5")
        response.raise_for_status()
        data = response.json()
        
        print(f"✅ 搜尋 '電子' 找到 {len(data['results'])} 個結果")
        
        # 測試搜尋 + 分類篩選
        import urllib.parse
        encoded_category = urllib.parse.quote("電子音樂")
        response = requests.get(f"http://localhost:8000/api/search?q=演唱會&category={encoded_category}&limit=3")
        response.raise_for_status()
        data = response.json()
        
        print(f"✅ 搜尋 '演唱會' + 分類 '電子音樂' 找到 {len(data['results'])} 個結果")
        
        return True
        
    except Exception as e:
        print(f"❌ 搜尋功能測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始測試動態分類管理功能\n")
    
    # 測試分類 API
    categories = test_categories_api()
    if not categories:
        print("❌ 分類 API 測試失敗，停止後續測試")
        return
    
    # 測試分類篩選
    if not test_category_filtering(categories):
        print("❌ 分類篩選測試失敗")
        return
    
    # 測試演唱會總數
    if not test_concerts_count():
        print("❌ 演唱會總數測試失敗")
        return
    
    # 測試搜尋功能
    if not test_search_functionality():
        print("❌ 搜尋功能測試失敗")
        return
    
    print("\n🎉 所有測試通過！動態分類管理功能正常運作")
    print("\n📈 功能總結:")
    print("✅ 動態分類管理：根據現有數據自動生成分類")
    print("✅ 分類篩選：支援按分類篩選演唱會")
    print("✅ 搜尋功能：支援關鍵字搜尋 + 分類篩選")
    print("✅ 圖片管理：自動下載並轉換為 WebP 格式")
    print("✅ 數據完整性：292 個演唱會，35 個分類")

if __name__ == "__main__":
    main()
