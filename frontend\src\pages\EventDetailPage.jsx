import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  Button,
  Chip,
  Divider,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  CalendarToday,
  LocationOn,
  AttachMoney,
  Schedule,
  Info,
  Warning,
  ConfirmationNumber,
  ShoppingCart
} from '@mui/icons-material';

function EventDetailPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [event, setEvent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // 從 FastAPI 後端獲取活動詳情
    const fetchEventDetail = async () => {
      try {
        setLoading(true);

        // 從後端 API 獲取活動詳情
        const response = await fetch(`http://127.0.0.1:8000/api/concerts/${id}`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const eventData = await response.json();
        setEvent(eventData);

      } catch (err) {
        console.error('獲取活動詳情時發生錯誤:', err);
        setError('無法載入活動資訊，請檢查網路連線或稍後再試。');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchEventDetail();
    }
  }, [id]);

  const handlePurchase = (ticketType) => {
    // 導向購票頁面
    navigate(`/purchase/${id}`, { state: { event, selectedTicketType: ticketType } });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <CircularProgress sx={{ color: '#00c0ff' }} />
      </Box>
    );
  }

  if (error) {
    return (
      <Container sx={{ mt: 4 }}>
        <Alert severity="error" sx={{ backgroundColor: '#332222', color: 'white' }}>
          {error}
        </Alert>
      </Container>
    );
  }

  const containerStyle = {
    minHeight: '100vh',
    backgroundColor: '#121212',
    color: 'white',
    paddingY: '2rem'
  };

  const paperStyle = {
    backgroundColor: 'rgba(36, 36, 36, 0.95)',
    backdropFilter: 'blur(10px)',
    border: '1px solid #444',
    borderRadius: '0',
    clipPath: 'polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 15px 100%, 0 calc(100% - 15px))',
    padding: '2rem',
    marginBottom: '2rem'
  };

  const ticketCardStyle = {
    backgroundColor: 'rgba(36, 36, 36, 0.8)',
    border: '1px solid #00c0ff',
    borderRadius: '0',
    clipPath: 'polygon(10px 0, 100% 0, calc(100% - 10px) 100%, 0 100%)',
    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
    '&:hover': {
      transform: 'translateY(-3px)',
      boxShadow: '0 0 15px rgba(0, 192, 255, 0.5)'
    }
  };

  return (
    <Box sx={containerStyle}>
      <Container maxWidth="lg">
        {/* 主要資訊區塊 */}
        <Paper elevation={0} sx={paperStyle}>
          <Grid container spacing={4}>
            {/* 左側：海報圖片 */}
            <Grid item xs={12} md={4}>
              <Box sx={{
                position: 'relative',
                clipPath: 'polygon(0 0, 100% 0, 100% calc(100% - 20px), calc(100% - 20px) 100%, 0 100%)',
                overflow: 'hidden'
              }}>
                <img 
                  src={event.poster_url}
                  alt={event.name}
                  style={{
                    width: '100%',
                    height: 'auto',
                    display: 'block'
                  }}
                />
              </Box>
            </Grid>

            {/* 右側：活動資訊 */}
            <Grid item xs={12} md={8}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="h3" component="h1" sx={{ 
                  fontWeight: 'bold', 
                  color: '#00c0ff',
                  textShadow: '0 0 10px #00c0ff',
                  mb: 1
                }}>
                  {event.name}
                </Typography>
                <Typography variant="h5" sx={{ color: '#aaa', mb: 2 }}>
                  {event.artist}
                </Typography>
                
                {/* 分類標籤 */}
                <Box sx={{ mb: 3 }}>
                  {event.categories.map((category, index) => (
                    <Chip 
                      key={index}
                      label={category}
                      sx={{
                        backgroundColor: 'rgba(0, 192, 255, 0.2)',
                        color: '#00c0ff',
                        border: '1px solid #00c0ff',
                        mr: 1,
                        mb: 1
                      }}
                    />
                  ))}
                </Box>

                {/* 基本資訊 */}
                <List>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <CalendarToday sx={{ color: '#00c0ff' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="演出日期"
                      secondary={`${event.date} ${event.time}`}
                      primaryTypographyProps={{ color: '#00c0ff', fontWeight: 'bold' }}
                      secondaryTypographyProps={{ color: 'white', fontSize: '1.1rem' }}
                    />
                  </ListItem>
                  
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <LocationOn sx={{ color: '#00c0ff' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="演出地點"
                      secondary={`${event.location} - ${event.address}`}
                      primaryTypographyProps={{ color: '#00c0ff', fontWeight: 'bold' }}
                      secondaryTypographyProps={{ color: 'white', fontSize: '1.1rem' }}
                    />
                  </ListItem>
                  
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Schedule sx={{ color: '#00c0ff' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="售票時間"
                      secondary={event.ticketSaleStart}
                      primaryTypographyProps={{ color: '#00c0ff', fontWeight: 'bold' }}
                      secondaryTypographyProps={{ color: 'white', fontSize: '1.1rem' }}
                    />
                  </ListItem>
                </List>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* 活動簡介 */}
        <Paper elevation={0} sx={paperStyle}>
          <Typography variant="h5" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 2 }}>
            活動簡介
          </Typography>
          <Typography variant="body1" sx={{ color: '#ddd', lineHeight: 1.8 }}>
            {event.description}
          </Typography>
        </Paper>

        {/* 票券資訊 */}
        <Paper elevation={0} sx={paperStyle}>
          <Typography variant="h5" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 3 }}>
            <ConfirmationNumber sx={{ mr: 1, verticalAlign: 'middle' }} />
            票券資訊
          </Typography>
          
          <Grid container spacing={3}>
            {event.ticketTypes.map((ticket) => (
              <Grid item xs={12} sm={6} md={3} key={ticket.id}>
                <Card sx={ticketCardStyle}>
                  <CardContent>
                    <Typography variant="h6" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 1 }}>
                      {ticket.name}
                    </Typography>
                    <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold', mb: 1 }}>
                      NT$ {ticket.price.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" sx={{ 
                      color: ticket.available > 0 ? '#4caf50' : '#f44336',
                      mb: 2 
                    }}>
                      剩餘 {ticket.available} / {ticket.total} 張
                    </Typography>
                    <Button
                      fullWidth
                      variant="contained"
                      disabled={ticket.available === 0}
                      onClick={() => handlePurchase(ticket)}
                      sx={{
                        backgroundColor: ticket.available > 0 ? '#00c0ff' : '#555',
                        color: ticket.available > 0 ? 'black' : '#888',
                        fontWeight: 'bold',
                        clipPath: 'polygon(5% 0, 100% 0, 95% 100%, 0% 100%)',
                        '&:hover': {
                          backgroundColor: ticket.available > 0 ? '#64d8ff' : '#555',
                          boxShadow: ticket.available > 0 ? '0 0 15px rgba(0, 192, 255, 0.5)' : 'none'
                        }
                      }}
                    >
                      <ShoppingCart sx={{ mr: 1 }} />
                      {ticket.available > 0 ? '立即購買' : '已售完'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Paper>

        {/* 重要事項 */}
        <Paper elevation={0} sx={paperStyle}>
          <Typography variant="h5" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 3 }}>
            <Warning sx={{ mr: 1, verticalAlign: 'middle' }} />
            重要事項
          </Typography>
          <List>
            {event.importantNotes.map((note, index) => (
              <ListItem key={index} sx={{ px: 0 }}>
                <ListItemIcon>
                  <Info sx={{ color: '#ffa726' }} />
                </ListItemIcon>
                <ListItemText
                  primary={note}
                  primaryTypographyProps={{ color: '#ddd' }}
                />
              </ListItem>
            ))}
          </List>
        </Paper>

        {/* 購票方式說明 */}
        <Paper elevation={0} sx={paperStyle}>
          <Typography variant="h5" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 3 }}>
            購票方式說明
          </Typography>
          <Grid container spacing={2}>
            {event.purchaseInstructions.map((instruction, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Box sx={{
                  textAlign: 'center',
                  padding: '1.5rem',
                  backgroundColor: 'rgba(0, 192, 255, 0.1)',
                  border: '1px solid #00c0ff',
                  clipPath: 'polygon(10% 0, 100% 0, 90% 100%, 0% 100%)'
                }}>
                  <Typography variant="h4" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 1 }}>
                    {index + 1}
                  </Typography>
                  <Typography variant="body1" sx={{ color: 'white' }}>
                    {instruction}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Paper>

        {/* 注意事項 */}
        <Paper elevation={0} sx={paperStyle}>
          <Typography variant="h5" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 3 }}>
            注意事項
          </Typography>
          <Alert severity="warning" sx={{
            backgroundColor: 'rgba(255, 167, 38, 0.1)',
            color: '#ffa726',
            border: '1px solid #ffa726',
            '& .MuiAlert-icon': {
              color: '#ffa726'
            }
          }}>
            <Typography variant="body2" sx={{ mb: 2, fontWeight: 'bold' }}>
              購票前請詳閱以下條款：
            </Typography>
            <List dense>
              {event.legalNotices.map((notice, index) => (
                <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                  <ListItemText
                    primary={`• ${notice}`}
                    primaryTypographyProps={{ color: '#ffa726', fontSize: '0.9rem' }}
                  />
                </ListItem>
              ))}
            </List>
          </Alert>
        </Paper>

        {/* 座位圖區域 */}
        <Paper elevation={0} sx={paperStyle}>
          <Typography variant="h5" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 3 }}>
            場地座位圖
          </Typography>
          <Box sx={{
            backgroundColor: '#1a1a1a',
            border: '1px solid #444',
            borderRadius: '8px',
            padding: '2rem',
            textAlign: 'center',
            minHeight: '300px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Typography variant="h6" sx={{ color: '#666' }}>
              座位圖將在此顯示
              <br />
              <Typography variant="body2" sx={{ mt: 1 }}>
                （此為示意圖區域，實際座位圖將由主辦方提供）
              </Typography>
            </Typography>
          </Box>
        </Paper>

        {/* 快速購票按鈕 */}
        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <Button
            variant="contained"
            size="large"
            onClick={() => navigate(`/purchase/${id}`, { state: { event } })}
            sx={{
              backgroundColor: '#00c0ff',
              color: 'black',
              fontWeight: 'bold',
              padding: '15px 40px',
              fontSize: '1.2rem',
              clipPath: 'polygon(10% 0, 100% 0, 90% 100%, 0% 100%)',
              '&:hover': {
                backgroundColor: '#64d8ff',
                boxShadow: '0 0 25px rgba(0, 192, 255, 0.6)'
              }
            }}
          >
            <ShoppingCart sx={{ mr: 2 }} />
            立即購票
          </Button>
        </Box>
      </Container>
    </Box>
  );
}

export default EventDetailPage;
