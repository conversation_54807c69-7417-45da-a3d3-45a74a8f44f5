import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  IconButton,
  Fade,
  useTheme
} from '@mui/material';
import {
  ArrowBackIos,
  ArrowForwardIos,
  PlayArrow,
  CalendarToday,
  LocationOn
} from '@mui/icons-material';
import axios from 'axios';

const HeroBanner = () => {
  const [banners, setBanners] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const theme = useTheme();

  // 獲取海報資料
  useEffect(() => {
    const fetchBanners = async () => {
      try {
        const response = await axios.get('http://localhost:8000/api/hero-banners');
        setBanners(response.data.banners);
        setLoading(false);
      } catch (error) {
        console.error('獲取海報資料失敗:', error);
        setLoading(false);
      }
    };

    fetchBanners();
  }, []);

  // 自動輪播
  useEffect(() => {
    if (banners.length === 0) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === banners.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000); // 每5秒切換

    return () => clearInterval(interval);
  }, [banners.length]);

  // 手動切換
  const handlePrevious = () => {
    setCurrentIndex(currentIndex === 0 ? banners.length - 1 : currentIndex - 1);
  };

  const handleNext = () => {
    setCurrentIndex(currentIndex === banners.length - 1 ? 0 : currentIndex + 1);
  };

  // 跳轉到活動詳情
  const handleViewEvent = () => {
    if (banners[currentIndex]) {
      navigate(`/event/${banners[currentIndex].concertId}`);
    }
  };

  if (loading || banners.length === 0) {
    return (
      <Box sx={{
        height: '10vh',
        background: 'linear-gradient(135deg, #1a237e 0%, #121212 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Typography variant="h6" sx={{ color: 'white' }}>
          載入中...
        </Typography>
      </Box>
    );
  }

  const currentBanner = banners[currentIndex];

  return (
    <Box
      className="hero-banner"
      sx={{
        position: 'relative',
        // 響應式高度設置，垂直排列時需要更多空間
        minHeight: {
          xs: '550px',  // 手機垂直排列需要更多空間
          sm: '600px',  // 平板垂直排列
          md: '500px',  // 小筆電水平排列
          lg: '50vh'    // 大螢幕水平排列
        },
        height: { xs: 'auto', md: 'auto', lg: '40vh' },
        maxHeight: { xs: 'none', md: 'none', lg: '600px' }, // 小螢幕不限制最大高度
        overflow: 'hidden',
        background: `linear-gradient(
          135deg,
          rgba(0, 0, 0, 0.7) 0%,
          rgba(0, 0, 0, 0.4) 50%,
          rgba(0, 0, 0, 0.8) 100%
        ), url(${currentBanner.image})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        borderRadius: '0 0 24px 24px',
        margin: '0 16px',
        marginBottom: '32px',
        boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
      }}>
      {/* 現代化毛玻璃效果層 */}
      <Box sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backdropFilter: 'blur(12px) saturate(150%)',
        WebkitBackdropFilter: 'blur(12px) saturate(150%)',
        background: 'rgba(0, 0, 0, 0.2)',
        borderRadius: '0 0 24px 24px',
        zIndex: 1,
      }} />

      {/* 精緻的漸層覆蓋層 */}
      <Box sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: `
          linear-gradient(
            135deg,
            rgba(0, 192, 255, 0.1) 0%,
            transparent 30%,
            transparent 70%,
            rgba(255, 107, 53, 0.08) 100%
          )
        `,
        borderRadius: '0 0 24px 24px',
        zIndex: 2,
      }} />

      {/* 主要內容 */}
      <Container maxWidth="lg" sx={{
        position: 'relative',
        zIndex: 3,
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        py: { xs: 3, md: 2 }, // 垂直排列時增加上下內邊距
        px: { xs: 2, md: 3 }  // 調整左右內邊距
      }}>
        <Fade in={true} timeout={1000} key={currentIndex}>
          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: { xs: 'center', md: 'center' },
            width: '100%',
            gap: { xs: 3, md: 4 }, // 增加垂直排列時的間距
            py: { xs: 1, md: 0 },
            minHeight: { xs: 'auto', md: 'auto' } // 確保有足夠高度
          }}>
            {/* 左側：活動海報 */}
            <Box sx={{
              // 響應式海報尺寸 - 垂直排列時使用較小尺寸
              flex: { xs: '0 0 160px', sm: '0 0 180px', md: '0 0 220px', lg: '0 0 240px' },
              height: { xs: '200px', sm: '220px', md: '280px', lg: '320px' },
              position: 'relative',
              borderRadius: '12px',
              overflow: 'hidden',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4)',
              border: '2px solid rgba(255, 255, 255, 0.1)',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: '0 12px 40px rgba(0, 192, 255, 0.3)',
              },
              transition: 'all 0.3s ease',
              // 垂直排列時居中顯示
              alignSelf: { xs: 'center', md: 'auto' }
            }}>
              <img
                src={currentBanner.image}
                alt={currentBanner.title}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                }}
              />
              {/* 海報上的光暈效果 */}
              <Box sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                background: 'linear-gradient(135deg, rgba(0, 192, 255, 0.1) 0%, transparent 50%)',
                pointerEvents: 'none',
              }} />
            </Box>

            {/* 右側：活動資訊 */}
            <Box sx={{
              flex: 1,
              color: 'white',
              textAlign: { xs: 'center', md: 'left' },
              width: { xs: '100%', md: 'auto' }
            }}>
              <Typography
                variant="h2"
                component="h1"
                sx={{
                  fontWeight: 'bold',
                  textShadow: '0 0 20px rgba(0, 192, 255, 0.8)',
                  mb: { xs: 0.5, md: 2 }, // 減少垂直排列時的間距
                  fontSize: { xs: '1.4rem', sm: '1.8rem', md: '2.5rem', lg: '3rem' },
                  lineHeight: { xs: 1.1, md: 1.3 }
                }}
              >
                {currentBanner.title}
              </Typography>

              <Typography
                variant="h5"
                sx={{
                  color: '#00c0ff',
                  mb: { xs: 1, md: 3 }, // 減少垂直排列時的間距
                  fontWeight: 500,
                  fontSize: { xs: '0.9rem', sm: '1.1rem', md: '1.5rem' }
                }}
              >
                {currentBanner.subtitle}
              </Typography>

              <Typography
                variant="body1"
                sx={{
                  mb: { xs: 1.5, md: 3 }, // 減少垂直排列時的間距
                  fontSize: { xs: '0.85rem', sm: '0.95rem', md: '1.1rem' },
                  lineHeight: 1.5,
                  maxWidth: { xs: '100%', md: '500px' },
                  display: { xs: 'none', sm: 'block' } // 在極小螢幕隱藏描述以節省空間
                }}
              >
                {currentBanner.description}
              </Typography>

              {/* 活動詳情 */}
              <Box sx={{
                mb: { xs: 1.5, md: 4 },
                display: 'flex',
                flexDirection: 'column',
                alignItems: { xs: 'center', md: 'flex-start' } // 垂直排列時置中
              }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  mb: 1,
                  flexWrap: { xs: 'wrap', sm: 'nowrap' },
                  justifyContent: { xs: 'center', md: 'flex-start' } // 垂直排列時置中
                }}>
                  <CalendarToday sx={{
                    mr: 1,
                    color: '#00c0ff',
                    fontSize: { xs: '1rem', md: '1.2rem' }
                  }} />
                  <Typography variant="body1" sx={{
                    fontSize: { xs: '0.9rem', md: '1rem' }
                  }}>
                    {currentBanner.date}
                  </Typography>
                </Box>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  flexWrap: { xs: 'wrap', sm: 'nowrap' },
                  justifyContent: { xs: 'center', md: 'flex-start' } // 垂直排列時置中
                }}>
                  <LocationOn sx={{
                    mr: 1,
                    color: '#00c0ff',
                    fontSize: { xs: '1rem', md: '1.2rem' }
                  }} />
                  <Typography variant="body1" sx={{
                    fontSize: { xs: '0.9rem', md: '1rem' }
                  }}>
                    {currentBanner.location}
                  </Typography>
                </Box>
              </Box>

              {/* 操作按鈕 */}
              <Box sx={{
                display: 'flex',
                justifyContent: { xs: 'center', md: 'flex-start' }, // 垂直排列時置中
                width: '100%'
              }}>
                <Button
                  variant="contained"
                  size="large"
                  onClick={handleViewEvent}
                  startIcon={<PlayArrow />}
                  sx={{
                    background: 'linear-gradient(135deg, #00c0ff 0%, #64d8ff 100%)',
                    color: 'white',
                    fontWeight: 'bold',
                    padding: { xs: '10px 20px', md: '14px 32px' }, // 垂直排列時使用較小的按鈕
                    fontSize: { xs: '0.9rem', md: '1.1rem' },
                    borderRadius: '8px',
                    textTransform: 'none',
                    boxShadow: '0 4px 20px rgba(0, 192, 255, 0.3)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    mb: { xs: 1, md: 0 }, // 垂直排列時添加底部間距
                    '&:hover': {
                      background: 'linear-gradient(135deg, #64d8ff 0%, #00c0ff 100%)',
                      boxShadow: '0 8px 30px rgba(0, 192, 255, 0.5)',
                      transform: 'translateY(-2px)',
                    },
                    transition: 'all 0.3s ease',
                  }}
                >
                  立即查看
                </Button>
              </Box>
            </Box>
          </Box>
        </Fade>
      </Container>

      {/* 導航按鈕 */}
      <IconButton
        onClick={handlePrevious}
        sx={{
          position: 'absolute',
          left: 24,
          top: '50%',
          transform: 'translateY(-50%)',
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          backdropFilter: 'blur(8px)',
          color: 'white',
          zIndex: 4,
          width: 48,
          height: 48,
          borderRadius: '12px',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          '&:hover': {
            backgroundColor: 'rgba(0, 192, 255, 0.8)',
            transform: 'translateY(-50%) scale(1.1)',
          },
          transition: 'all 0.3s ease',
        }}
      >
        <ArrowBackIos />
      </IconButton>

      <IconButton
        onClick={handleNext}
        sx={{
          position: 'absolute',
          right: 24,
          top: '50%',
          transform: 'translateY(-50%)',
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          backdropFilter: 'blur(8px)',
          color: 'white',
          zIndex: 4,
          width: 48,
          height: 48,
          borderRadius: '12px',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          '&:hover': {
            backgroundColor: 'rgba(0, 192, 255, 0.8)',
            transform: 'translateY(-50%) scale(1.1)',
          },
          transition: 'all 0.3s ease',
        }}
      >
        <ArrowForwardIos />
      </IconButton>

      {/* 指示器 */}
      {banners.length > 0 && (
        <Box sx={{
          position: 'absolute',
          bottom: 20,
          left: '50%',
          transform: 'translateX(-50%)',
          display: 'flex',
          gap: 2,
          zIndex: 10,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          backdropFilter: 'blur(10px)',
          padding: '12px 20px',
          borderRadius: '25px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4)',
        }}>
          {banners.map((_, index) => (
            <Box
              key={index}
              onClick={() => setCurrentIndex(index)}
              sx={{
                width: index === currentIndex ? 32 : 12,
                height: 12,
                borderRadius: '6px',
                backgroundColor: index === currentIndex ? '#00c0ff' : 'rgba(255, 255, 255, 0.9)',
                border: index === currentIndex ? 'none' : '2px solid rgba(255, 255, 255, 0.8)',
                cursor: 'pointer',
                transition: 'all 0.4s ease',
                boxShadow: index === currentIndex ? '0 0 12px rgba(0, 192, 255, 0.6)' : '0 0 6px rgba(255, 255, 255, 0.4)',
                '&:hover': {
                  backgroundColor: index === currentIndex ? '#64d8ff' : '#ffffff',
                  border: index === currentIndex ? 'none' : '2px solid #ffffff',
                  boxShadow: index === currentIndex ? '0 0 20px rgba(0, 192, 255, 0.8)' : '0 0 12px rgba(255, 255, 255, 0.8)',
                  transform: 'scale(1.1)',
                }
              }}
            />
          ))}
        </Box>
      )}
    </Box>
  );
};

export default HeroBanner;
