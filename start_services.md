# 🎵 Synctix 票務平台啟動指南

## 📋 系統概述

我已經成功為您創建了一個功能豐富的演唱會票務平台，包含：

### ✨ 新功能特色
- **100場豐富演唱會資料** - 自動生成的多樣化演出資訊
- **20個音樂類別** - 涵蓋流行、搖滾、爵士、電子等各種音樂風格
- **智能分類篩選** - 可展開/收起的類別選擇器
- **高性能圖片** - 所有圖片已轉換為 WebP 格式並優化
- **後端 API 整合** - 完整的 FastAPI 後端服務
- **響應式設計** - 支援桌面和行動裝置

### 🎨 視覺改進
- **新的分類篩選組件** - 更美觀且功能完整的類別選擇器
- **優化的搜尋介面** - 毛玻璃效果的搜尋欄
- **動態海報輪播** - 使用真實演唱會資料
- **明日方舟風格卡片** - 保持原有的視覺風格

## 🚀 啟動步驟

### 1. 啟動後端服務
```bash
cd frontend/backend
uv run python src/main.py
```
後端將在 http://localhost:8000 運行

### 2. 啟動前端服務
```bash
cd frontend
npm run dev
```
前端將在 http://localhost:5173 運行

## 📊 API 端點

### 演唱會相關
- `GET /api/concerts` - 獲取演唱會列表（支援分類和狀態篩選）
- `GET /api/concerts/{id}` - 獲取特定演唱會詳情
- `GET /api/categories` - 獲取所有音樂類別
- `GET /api/featured` - 獲取精選演唱會
- `GET /api/hero-banners` - 獲取主頁海報資料
- `GET /api/search?q={keyword}` - 搜尋演唱會

### 圖片服務
- `GET /images/concerts/{filename}` - 獲取演唱會海報圖片

## 🎯 主要改進

### 後端優化
1. **資料生成腳本** - 自動下載並轉換圖片為 WebP 格式
2. **豐富的假資料** - 100場演唱會，涵蓋20種音樂類別
3. **靜態文件服務** - 直接提供圖片資源
4. **API 標準化** - 統一的回應格式和錯誤處理

### 前端優化
1. **CategoryFilter 組件** - 可展開的分類篩選器
2. **後端整合** - 所有資料來源改為後端 API
3. **圖片路徑修正** - 正確載入後端提供的圖片
4. **搜尋介面改進** - 更美觀的搜尋欄設計

## 🔧 技術細節

### 資料結構
- **演唱會資料**: 包含完整的票種、價格、場地資訊
- **海報資料**: 自動從精選演唱會生成輪播海報
- **圖片優化**: 所有圖片壓縮為 WebP 格式，提升載入速度

### 性能優化
- **圖片壓縮**: 原始圖片轉換為 WebP，減少 60-80% 檔案大小
- **後端篩選**: 分類篩選在後端執行，減少前端負載
- **響應式設計**: 針對不同螢幕尺寸優化顯示

## 🎵 使用說明

1. **瀏覽演唱會**: 主頁顯示所有可用的演出
2. **分類篩選**: 點擊音樂類別快速篩選演出
3. **搜尋功能**: 輸入關鍵字搜尋演出、藝人或場地
4. **查看詳情**: 點擊演出卡片查看完整資訊
5. **海報輪播**: 主頁頂部展示精選演出

## 📝 注意事項

- 確保後端服務先啟動，前端才能正常載入資料
- 圖片資源存放在 `frontend/backend/images/concerts/` 目錄
- 演唱會資料存放在 `frontend/backend/data/` 目錄
- 所有 API 調用已更新為使用 `localhost:8000`

## 🎉 完成狀態

✅ 後端 API 服務完整運行
✅ 100場演唱會資料生成完成
✅ 圖片下載並轉換為 WebP 格式
✅ 前端組件更新完成
✅ 分類篩選功能實現
✅ 搜尋功能正常運作
✅ 海報輪播功能正常

您的 Synctix 票務平台現在已經擁有豐富的演唱會資料和完整的功能！🎵
