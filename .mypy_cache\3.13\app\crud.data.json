{".class": "MypyFile", "_fullname": "app.crud", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.crud.Session", "name": "Session", "type": {".class": "AnyType", "missing_import_name": "app.crud.Session", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.crud.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.crud.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.crud.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.crud.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.crud.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.crud.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "create_user": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["db", "user"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.crud.create_user", "name": "create_user", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["db", "user"], "arg_types": [{".class": "AnyType", "missing_import_name": "app.crud.Session", "source_any": null, "type_of_any": 3}, "app.schemas.UserCreate"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_user", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_user_from_google": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["db", "user_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.crud.create_user_from_google", "name": "create_user_from_google", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["db", "user_info"], "arg_types": [{".class": "AnyType", "missing_import_name": "app.crud.Session", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_user_from_google", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_user_by_email": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["db", "email"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.crud.get_user_by_email", "name": "get_user_by_email", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["db", "email"], "arg_types": [{".class": "AnyType", "missing_import_name": "app.crud.Session", "source_any": null, "type_of_any": 3}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_user_by_email", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_user_by_google_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["db", "google_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.crud.get_user_by_google_id", "name": "get_user_by_google_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["db", "google_id"], "arg_types": [{".class": "AnyType", "missing_import_name": "app.crud.Session", "source_any": null, "type_of_any": 3}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_user_by_google_id", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_user_by_username": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["db", "username"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.crud.get_user_by_username", "name": "get_user_by_username", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["db", "username"], "arg_types": [{".class": "AnyType", "missing_import_name": "app.crud.Session", "source_any": null, "type_of_any": 3}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_user_by_username", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "models": {".class": "SymbolTableNode", "cross_ref": "app.models", "kind": "Gdef"}, "schemas": {".class": "SymbolTableNode", "cross_ref": "app.schemas", "kind": "Gdef"}, "security": {".class": "SymbolTableNode", "cross_ref": "app.security", "kind": "Gdef"}}, "path": "D:\\Repos\\gjunedu-platinum-2025\\pybackend\\src\\app\\crud.py"}