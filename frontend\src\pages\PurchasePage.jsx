import React, { useState, useEffect } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Paper,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  FormControlLabel,
  Checkbox,
  Grid,
  Card,
  CardContent,
  Divider,
  <PERSON>ert,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import {
  ShoppingCart,
  ConfirmationNumber,
  AttachMoney,
  Person,
  ArrowBack,
  ArrowForward
} from '@mui/icons-material';

function PurchasePage() {
  const { id } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  
  const [event, setEvent] = useState(null);
  const [selectedTicketType, setSelectedTicketType] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    // 從 location.state 獲取活動資料，或者重新從API獲取
    if (location.state?.event) {
      setEvent(location.state.event);
      if (location.state.selectedTicketType) {
        setSelectedTicketType(location.state.selectedTicketType.id);
      }
    } else {
      // 如果沒有傳遞資料，模擬從API獲取
      const mockEvent = {
        id: parseInt(id),
        name: "GJUN Rock Fest 2025",
        artist: "The Code Breakers",
        date: "2025-08-15",
        time: "19:30",
        location: "Taipei Arena",
        poster_url: "https://via.placeholder.com/300x400.png?text=GJUN+Rock+Fest",
        ticketTypes: [
          { id: 1, name: "VIP搖滾區", price: 3500, available: 50, total: 100 },
          { id: 2, name: "前排站票", price: 2500, available: 150, total: 200 },
          { id: 3, name: "一般座位", price: 1800, available: 300, total: 500 },
          { id: 4, name: "學生票", price: 1200, available: 80, total: 100 }
        ]
      };
      setEvent(mockEvent);
    }
  }, [id, location.state]);

  const handleTicketTypeChange = (e) => {
    setSelectedTicketType(e.target.value);
    setErrors(prev => ({ ...prev, ticketType: '' }));
  };

  const handleQuantityChange = (e) => {
    const value = parseInt(e.target.value);
    if (value > 0 && value <= 10) {
      setQuantity(value);
      setErrors(prev => ({ ...prev, quantity: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!selectedTicketType) {
      newErrors.ticketType = '請選擇票種';
    }

    if (quantity < 1 || quantity > 10) {
      newErrors.quantity = '票數必須在1-10張之間';
    }

    if (!agreeTerms) {
      newErrors.agreeTerms = '請同意服務條款';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleProceedToCheckout = () => {
    if (!validateForm()) {
      return;
    }

    const selectedTicket = event.ticketTypes.find(t => t.id === selectedTicketType);
    const orderData = {
      event,
      ticketType: selectedTicket,
      quantity,
      totalAmount: selectedTicket.price * quantity
    };

    navigate('/checkout', { state: orderData });
  };

  const getSelectedTicket = () => {
    return event?.ticketTypes.find(t => t.id === selectedTicketType);
  };

  const getTotalAmount = () => {
    const ticket = getSelectedTicket();
    return ticket ? ticket.price * quantity : 0;
  };

  if (!event) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Typography>載入中...</Typography>
      </Box>
    );
  }

  const containerStyle = {
    minHeight: '100vh',
    backgroundColor: '#121212',
    color: 'white',
    paddingY: '2rem'
  };

  const paperStyle = {
    backgroundColor: 'rgba(36, 36, 36, 0.95)',
    backdropFilter: 'blur(10px)',
    border: '1px solid #444',
    borderRadius: '0',
    clipPath: 'polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 15px 100%, 0 calc(100% - 15px))',
    padding: '2rem',
    marginBottom: '2rem'
  };

  const formControlStyle = {
    '& .MuiOutlinedInput-root': {
      color: 'white',
      '& fieldset': {
        borderColor: '#444'
      },
      '&:hover fieldset': {
        borderColor: '#00c0ff'
      },
      '&.Mui-focused fieldset': {
        borderColor: '#00c0ff'
      }
    },
    '& .MuiInputLabel-root': {
      color: '#aaa',
      '&.Mui-focused': {
        color: '#00c0ff'
      }
    },
    '& .MuiSelect-icon': {
      color: '#00c0ff'
    }
  };

  const steps = ['選擇票種', '填寫資料', '付款', '完成'];

  return (
    <Box sx={containerStyle}>
      <Container maxWidth="lg">
        {/* 進度指示器 */}
        <Paper elevation={0} sx={paperStyle}>
          <Stepper activeStep={0} alternativeLabel sx={{
            '& .MuiStepLabel-label': { color: '#aaa' },
            '& .MuiStepLabel-label.Mui-active': { color: '#00c0ff' },
            '& .MuiStepIcon-root': { color: '#444' },
            '& .MuiStepIcon-root.Mui-active': { color: '#00c0ff' }
          }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Paper>

        <Grid container spacing={4}>
          {/* 左側：活動資訊 */}
          <Grid item xs={12} md={4}>
            <Paper elevation={0} sx={paperStyle}>
              <Typography variant="h6" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 2 }}>
                活動資訊
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <img 
                  src={event.poster_url} 
                  alt={event.name}
                  style={{
                    width: '100%',
                    height: 'auto',
                    clipPath: 'polygon(0 0, 100% 0, 100% calc(100% - 15px), calc(100% - 15px) 100%, 0 100%)'
                  }}
                />
              </Box>

              <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold', mb: 1 }}>
                {event.name}
              </Typography>
              <Typography variant="body2" sx={{ color: '#aaa', mb: 1 }}>
                演出者：{event.artist}
              </Typography>
              <Typography variant="body2" sx={{ color: '#aaa', mb: 1 }}>
                日期：{event.date} {event.time}
              </Typography>
              <Typography variant="body2" sx={{ color: '#aaa' }}>
                地點：{event.location}
              </Typography>
            </Paper>
          </Grid>

          {/* 右側：購票表單 */}
          <Grid item xs={12} md={8}>
            <Paper elevation={0} sx={paperStyle}>
              <Typography variant="h5" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 3 }}>
                <ShoppingCart sx={{ mr: 1, verticalAlign: 'middle' }} />
                選擇票券
              </Typography>

              <Grid container spacing={3}>
                {/* 票種選擇 */}
                <Grid item xs={12}>
                  <FormControl fullWidth error={!!errors.ticketType} sx={formControlStyle}>
                    <InputLabel>票種</InputLabel>
                    <Select
                      value={selectedTicketType}
                      onChange={handleTicketTypeChange}
                      label="票種"
                    >
                      {event.ticketTypes.map((ticket) => (
                        <MenuItem 
                          key={ticket.id} 
                          value={ticket.id}
                          disabled={ticket.available === 0}
                        >
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                            <span>{ticket.name}</span>
                            <span>NT$ {ticket.price.toLocaleString()}</span>
                            <span style={{ color: ticket.available > 0 ? '#4caf50' : '#f44336' }}>
                              (剩餘 {ticket.available} 張)
                            </span>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.ticketType && (
                      <Typography variant="caption" sx={{ color: '#ff6b6b', mt: 0.5 }}>
                        {errors.ticketType}
                      </Typography>
                    )}
                  </FormControl>
                </Grid>

                {/* 數量選擇 */}
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="購買數量"
                    type="number"
                    value={quantity}
                    onChange={handleQuantityChange}
                    inputProps={{ min: 1, max: 10 }}
                    error={!!errors.quantity}
                    helperText={errors.quantity || '每人限購10張'}
                    sx={formControlStyle}
                  />
                </Grid>

                {/* 總金額顯示 */}
                <Grid item xs={12} sm={6}>
                  <Card sx={{
                    backgroundColor: 'rgba(0, 192, 255, 0.1)',
                    border: '1px solid #00c0ff',
                    borderRadius: '0'
                  }}>
                    <CardContent>
                      <Typography variant="body2" sx={{ color: '#00c0ff', mb: 1 }}>
                        總金額
                      </Typography>
                      <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                        NT$ {getTotalAmount().toLocaleString()}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                {/* 服務條款同意 */}
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={agreeTerms}
                        onChange={(e) => {
                          setAgreeTerms(e.target.checked);
                          setErrors(prev => ({ ...prev, agreeTerms: '' }));
                        }}
                        sx={{
                          color: '#00c0ff',
                          '&.Mui-checked': {
                            color: '#00c0ff',
                          },
                        }}
                      />
                    }
                    label={
                      <Typography variant="body2" sx={{ color: errors.agreeTerms ? '#ff6b6b' : '#aaa' }}>
                        我已閱讀並同意{' '}
                        <Typography component="span" sx={{ color: '#00c0ff', textDecoration: 'underline', cursor: 'pointer' }}>
                          服務條款
                        </Typography>
                        {' '}與{' '}
                        <Typography component="span" sx={{ color: '#00c0ff', textDecoration: 'underline', cursor: 'pointer' }}>
                          隱私權政策
                        </Typography>
                      </Typography>
                    }
                  />
                  {errors.agreeTerms && (
                    <Typography variant="caption" sx={{ color: '#ff6b6b', display: 'block', ml: 4 }}>
                      {errors.agreeTerms}
                    </Typography>
                  )}
                </Grid>
              </Grid>

              <Divider sx={{ backgroundColor: '#444', my: 3 }} />

              {/* 操作按鈕 */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  onClick={() => navigate(`/event/${id}`)}
                  sx={{
                    color: '#aaa',
                    borderColor: '#aaa',
                    '&:hover': {
                      backgroundColor: 'rgba(170, 170, 170, 0.1)',
                      borderColor: '#ccc'
                    }
                  }}
                >
                  <ArrowBack sx={{ mr: 1 }} />
                  返回活動頁面
                </Button>

                <Button
                  variant="contained"
                  onClick={handleProceedToCheckout}
                  disabled={!selectedTicketType || !agreeTerms}
                  sx={{
                    backgroundColor: '#00c0ff',
                    color: 'black',
                    fontWeight: 'bold',
                    padding: '10px 30px',
                    clipPath: 'polygon(10% 0, 100% 0, 90% 100%, 0% 100%)',
                    '&:hover': {
                      backgroundColor: '#64d8ff',
                      boxShadow: '0 0 20px rgba(0, 192, 255, 0.5)'
                    },
                    '&:disabled': {
                      backgroundColor: '#555',
                      color: '#888'
                    }
                  }}
                >
                  繼續結帳
                  <ArrowForward sx={{ ml: 1 }} />
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}

export default PurchasePage;
