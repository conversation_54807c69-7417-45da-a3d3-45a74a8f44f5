import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Box,
  Typography,
  IconButton,
  Divider
} from '@mui/material';
import { Add, Delete } from '@mui/icons-material';

function ConcertManagementDialog({ 
  open, 
  onClose, 
  concert, 
  isEditMode, 
  onSave 
}) {
  const [formData, setFormData] = useState({
    name: '',
    artist: '',
    date: '',
    time: '',
    location: '',
    address: '',
    poster_url: '',
    description: '',
    categories: [],
    ticketSaleStart: '',
    ticketTypes: [],
    importantNotes: [],
    purchaseInstructions: [],
    legalNotices: [],
    status: 'upcoming',
    featured: false
  });

  const [newCategory, setNewCategory] = useState('');
  const [newNote, setNewNote] = useState('');
  const [newInstruction, setNewInstruction] = useState('');
  const [newLegalNotice, setNewLegalNotice] = useState('');

  useEffect(() => {
    if (concert && isEditMode) {
      setFormData({
        name: concert.name || '',
        artist: concert.artist || '',
        date: concert.date || '',
        time: concert.time || '',
        location: concert.location || '',
        address: concert.address || '',
        poster_url: concert.poster_url || '',
        description: concert.description || '',
        categories: concert.categories || [],
        ticketSaleStart: concert.ticketSaleStart || '',
        ticketTypes: concert.ticketTypes || [],
        importantNotes: concert.importantNotes || [],
        purchaseInstructions: concert.purchaseInstructions || [],
        legalNotices: concert.legalNotices || [],
        status: concert.status || 'upcoming',
        featured: concert.featured || false
      });
    } else if (!isEditMode) {
      // 重置表單
      setFormData({
        name: '',
        artist: '',
        date: '',
        time: '',
        location: '',
        address: '',
        poster_url: '',
        description: '',
        categories: [],
        ticketSaleStart: '',
        ticketTypes: [
          { name: '', price: 0, total: 0, description: '' }
        ],
        importantNotes: [],
        purchaseInstructions: [],
        legalNotices: [],
        status: 'upcoming',
        featured: false
      });
    }
  }, [concert, isEditMode, open]);

  const handleInputChange = (field) => (event) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleAddCategory = () => {
    if (newCategory.trim()) {
      setFormData(prev => ({
        ...prev,
        categories: [...prev.categories, newCategory.trim()]
      }));
      setNewCategory('');
    }
  };

  const handleRemoveCategory = (index) => {
    setFormData(prev => ({
      ...prev,
      categories: prev.categories.filter((_, i) => i !== index)
    }));
  };

  const handleTicketTypeChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      ticketTypes: prev.ticketTypes.map((ticket, i) => 
        i === index ? { ...ticket, [field]: value } : ticket
      )
    }));
  };

  const handleAddTicketType = () => {
    setFormData(prev => ({
      ...prev,
      ticketTypes: [...prev.ticketTypes, { name: '', price: 0, total: 0, description: '' }]
    }));
  };

  const handleRemoveTicketType = (index) => {
    setFormData(prev => ({
      ...prev,
      ticketTypes: prev.ticketTypes.filter((_, i) => i !== index)
    }));
  };

  const handleAddNote = () => {
    if (newNote.trim()) {
      setFormData(prev => ({
        ...prev,
        importantNotes: [...prev.importantNotes, newNote.trim()]
      }));
      setNewNote('');
    }
  };

  const handleAddInstruction = () => {
    if (newInstruction.trim()) {
      setFormData(prev => ({
        ...prev,
        purchaseInstructions: [...prev.purchaseInstructions, newInstruction.trim()]
      }));
      setNewInstruction('');
    }
  };

  const handleAddLegalNotice = () => {
    if (newLegalNotice.trim()) {
      setFormData(prev => ({
        ...prev,
        legalNotices: [...prev.legalNotices, newLegalNotice.trim()]
      }));
      setNewLegalNotice('');
    }
  };

  const handleSave = async () => {
    try {
      const url = isEditMode 
        ? `http://localhost:8000/api/admin/concerts/${concert.id}`
        : 'http://localhost:8000/api/admin/concerts';
      
      const method = isEditMode ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const result = await response.json();
        alert(isEditMode ? '活動更新成功' : '活動創建成功');
        onSave(result.concert);
        onClose();
      } else {
        const error = await response.json();
        alert(`操作失敗：${error.detail}`);
      }
    } catch (error) {
      console.error('保存活動錯誤:', error);
      alert('操作失敗，請稍後再試');
    }
  };

  const textFieldStyle = {
    '& .MuiOutlinedInput-root': {
      color: 'white',
      '& fieldset': {
        borderColor: '#444'
      },
      '&:hover fieldset': {
        borderColor: '#00c0ff'
      },
      '&.Mui-focused fieldset': {
        borderColor: '#00c0ff'
      }
    },
    '& .MuiInputLabel-root': {
      color: '#aaa',
      '&.Mui-focused': {
        color: '#00c0ff'
      }
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: 'rgba(36, 36, 36, 0.95)',
          color: 'white',
          border: '1px solid #444',
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle sx={{ color: '#00c0ff' }}>
        {isEditMode ? '編輯活動' : '新增活動'}
      </DialogTitle>
      <DialogContent sx={{ maxHeight: '70vh', overflowY: 'auto' }}>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          {/* 基本資訊 */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ color: '#00c0ff', mb: 2 }}>
              基本資訊
            </Typography>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="活動名稱"
              value={formData.name}
              onChange={handleInputChange('name')}
              required
              sx={textFieldStyle}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="演出者"
              value={formData.artist}
              onChange={handleInputChange('artist')}
              required
              sx={textFieldStyle}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="日期"
              type="date"
              value={formData.date}
              onChange={handleInputChange('date')}
              required
              sx={textFieldStyle}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="時間"
              type="time"
              value={formData.time}
              onChange={handleInputChange('time')}
              required
              sx={textFieldStyle}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="地點"
              value={formData.location}
              onChange={handleInputChange('location')}
              required
              sx={textFieldStyle}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="詳細地址"
              value={formData.address}
              onChange={handleInputChange('address')}
              sx={textFieldStyle}
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="海報圖片 URL"
              value={formData.poster_url}
              onChange={handleInputChange('poster_url')}
              sx={textFieldStyle}
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="活動描述"
              multiline
              rows={3}
              value={formData.description}
              onChange={handleInputChange('description')}
              sx={textFieldStyle}
            />
          </Grid>

          {/* 類別管理 */}
          <Grid item xs={12}>
            <Divider sx={{ backgroundColor: '#444', my: 2 }} />
            <Typography variant="h6" sx={{ color: '#00c0ff', mb: 2 }}>
              活動類別
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <TextField
                label="新增類別"
                value={newCategory}
                onChange={(e) => setNewCategory(e.target.value)}
                sx={textFieldStyle}
                size="small"
              />
              <Button
                variant="contained"
                onClick={handleAddCategory}
                sx={{ backgroundColor: '#00c0ff', color: 'black' }}
              >
                <Add />
              </Button>
            </Box>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {formData.categories.map((category, index) => (
                <Chip
                  key={index}
                  label={category}
                  onDelete={() => handleRemoveCategory(index)}
                  sx={{
                    backgroundColor: '#00c0ff',
                    color: 'black',
                    '& .MuiChip-deleteIcon': {
                      color: 'black'
                    }
                  }}
                />
              ))}
            </Box>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={onClose}
          sx={{ color: '#aaa' }}
        >
          取消
        </Button>
        <Button
          onClick={handleSave}
          sx={{
            backgroundColor: '#00c0ff',
            color: 'black',
            '&:hover': {
              backgroundColor: '#64d8ff'
            }
          }}
        >
          {isEditMode ? '更新' : '創建'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default ConcertManagementDialog;
