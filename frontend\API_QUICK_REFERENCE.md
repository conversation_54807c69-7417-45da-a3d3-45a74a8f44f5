# Synctix 前端 API 快速參考

## 🚀 快速開始

### 啟動前端
```bash
cd frontend
npm install
npm run dev
# 前端運行在 http://localhost:5173
```

### 後端 API 基礎 URL
```
http://localhost:8000
```

## 📋 核心 API 端點

### 🎫 演唱會相關
```javascript
// 獲取演唱會列表 (支援分頁、搜尋、分類篩選)
GET /api/concerts?page=1&limit=12&category=搖滾&search=關鍵字

// 獲取演唱會詳情
GET /api/concerts/{id}

// 獲取分類列表
GET /api/categories

// 獲取主頁輪播
GET /api/hero-banners
```

### 👤 用戶認證
```javascript
// 用戶登入
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "123456"
}

// 用戶註冊
POST /api/auth/register
{
  "name": "張小明",
  "email": "<EMAIL>",
  "phone": "0912345678",
  "password": "123456"
}
```

### 🛒 訂單處理
```javascript
// 創建訂單
POST /api/orders
{
  "concert_id": 1,
  "ticket_type_id": 2,
  "quantity": 2,
  "customer_info": {
    "name": "張小明",
    "email": "<EMAIL>",
    "phone": "0912345678"
  },
  "user_id": "user-001"  // 可選，登入用戶才有
}

// 處理付款
POST /api/orders/{orderId}/payment
{
  "payment_method": "credit_card"
}

// 獲取訂單詳情
GET /api/orders/{orderId}
```

### 👨‍💼 管理員 API
```javascript
// 儀表板數據
GET /api/admin/dashboard

// 活動管理
GET /api/admin/concerts/manage
POST /api/admin/concerts
PUT /api/admin/concerts/{id}
DELETE /api/admin/concerts/{id}

// 訂單管理
GET /api/admin/orders?status=paid&limit=50

// 用戶管理
GET /api/admin/users?limit=50
```

## 🔧 前端使用範例

### 獲取活動列表
```javascript
const fetchConcerts = async (page = 1, category = '', search = '') => {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: '12',
      ...(category && { category }),
      ...(search && { search })
    });
    
    const response = await fetch(`http://localhost:8000/api/concerts?${params}`);
    const data = await response.json();
    
    return data; // { concerts: [], total: number, page: number, limit: number }
  } catch (error) {
    console.error('獲取活動列表失敗:', error);
  }
};
```

### 用戶登入
```javascript
const login = async (email, password) => {
  try {
    const response = await fetch('http://localhost:8000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password })
    });
    
    if (response.ok) {
      const data = await response.json();
      return data.user; // 用戶資料
    } else {
      const error = await response.json();
      throw new Error(error.detail);
    }
  } catch (error) {
    console.error('登入失敗:', error);
    throw error;
  }
};
```

### 創建訂單
```javascript
const createOrder = async (orderData) => {
  try {
    const response = await fetch('http://localhost:8000/api/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData)
    });
    
    if (response.ok) {
      const data = await response.json();
      return data.order; // 訂單資料
    } else {
      const error = await response.json();
      throw new Error(error.detail);
    }
  } catch (error) {
    console.error('創建訂單失敗:', error);
    throw error;
  }
};
```

## 📊 重要數據結構

### 演唱會數據
```javascript
{
  "id": 1,
  "name": "搖滾音樂節 2025",
  "artist": "The Rock Band",
  "date": "2025-08-15",
  "time": "19:30",
  "location": "台北小巨蛋",
  "poster_url": "http://localhost:8000/images/concerts/concert_001.webp",
  "ticketTypes": [
    {
      "id": 1,
      "name": "VIP搖滾區",
      "price": 3500,
      "available": 50,
      "total": 100
    }
  ],
  "status": "on_sale"
}
```

### 用戶數據
```javascript
{
  "id": "user-001",
  "name": "張小明",
  "email": "<EMAIL>",
  "phone": "0912345678",
  "is_admin": false
}
```

### 訂單數據
```javascript
{
  "id": "order-001",
  "concert_name": "搖滾音樂節 2025",
  "total_amount": 7000,
  "status": "paid",
  "qr_code": "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=SYNCTIX-order-001"
}
```

## ⚠️ 重要注意事項

### 圖片 URL 處理
```javascript
// ✅ 正確：直接使用 API 返回的完整 URL
<img src={concert.poster_url} alt={concert.name} />

// ❌ 錯誤：不要再添加域名前綴
<img src={`http://localhost:8000${concert.poster_url}`} alt={concert.name} />
```

### 錯誤處理
```javascript
// 統一的錯誤處理模式
try {
  const response = await fetch(url);
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.detail || '請求失敗');
  }
  return await response.json();
} catch (error) {
  console.error('API 錯誤:', error);
  // 顯示用戶友好的錯誤訊息
  alert(error.message);
}
```

### 分頁處理
```javascript
// 分頁參數
const params = {
  page: 1,        // 頁碼 (從 1 開始)
  limit: 12,      // 每頁數量
  offset: 0       // 偏移量 (可選)
};

// 回應格式
{
  "concerts": [...],
  "total": 292,     // 總數量
  "page": 1,        // 當前頁
  "limit": 12       // 每頁數量
}
```

## 🧪 測試帳號

### 一般用戶
- **Email**: <EMAIL>
- **密碼**: 123456

### 管理員
- **Email**: <EMAIL>
- **密碼**: admin123

## 🔗 相關文檔

- [完整前端文檔](./FRONTEND_DOCUMENTATION.md)
- [後端 API 文檔](http://localhost:8000/docs)
- [系統總覽](../SYSTEM_OVERVIEW.md)

**最後更新**: 2025-07-22
