"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[6214],{26214:(t,e,n)=>{n.d(e,{diagram:()=>X});var i=n(75905);var r=n(24982);var s=n(63170);var a=n(77470);var o=n(48750);var c=function(){var t=(0,i.K2)((function(t,e,n,i){for(n=n||{},i=t.length;i--;n[t[i]]=e);return n}),"o"),e=[6,8,10,11,12,14,16,17,20,21],n=[1,9],r=[1,10],s=[1,11],a=[1,12],o=[1,13],c=[1,16],l=[1,17];var h={trace:(0,i.K2)((function t(){}),"trace"),yy:{},symbols_:{error:2,start:3,timeline:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NEWLINE:10,title:11,acc_title:12,acc_title_value:13,acc_descr:14,acc_descr_value:15,acc_descr_multiline_value:16,section:17,period_statement:18,event_statement:19,period:20,event:21,$accept:0,$end:1},terminals_:{2:"error",4:"timeline",6:"EOF",8:"SPACE",10:"NEWLINE",11:"title",12:"acc_title",13:"acc_title_value",14:"acc_descr",15:"acc_descr_value",16:"acc_descr_multiline_value",17:"section",20:"period",21:"event"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,1],[18,1],[19,1]],performAction:(0,i.K2)((function t(e,n,i,r,s,a,o){var c=a.length-1;switch(s){case 1:return a[c-1];break;case 2:this.$=[];break;case 3:a[c-1].push(a[c]);this.$=a[c-1];break;case 4:case 5:this.$=a[c];break;case 6:case 7:this.$=[];break;case 8:r.getCommonDb().setDiagramTitle(a[c].substr(6));this.$=a[c].substr(6);break;case 9:this.$=a[c].trim();r.getCommonDb().setAccTitle(this.$);break;case 10:case 11:this.$=a[c].trim();r.getCommonDb().setAccDescription(this.$);break;case 12:r.addSection(a[c].substr(8));this.$=a[c].substr(8);break;case 15:r.addTask(a[c],0,"");this.$=a[c];break;case 16:r.addEvent(a[c].substr(2));this.$=a[c];break}}),"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},t(e,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:n,12:r,14:s,16:a,17:o,18:14,19:15,20:c,21:l},t(e,[2,7],{1:[2,1]}),t(e,[2,3]),{9:18,11:n,12:r,14:s,16:a,17:o,18:14,19:15,20:c,21:l},t(e,[2,5]),t(e,[2,6]),t(e,[2,8]),{13:[1,19]},{15:[1,20]},t(e,[2,11]),t(e,[2,12]),t(e,[2,13]),t(e,[2,14]),t(e,[2,15]),t(e,[2,16]),t(e,[2,4]),t(e,[2,9]),t(e,[2,10])],defaultActions:{},parseError:(0,i.K2)((function t(e,n){if(n.recoverable){this.trace(e)}else{var i=new Error(e);i.hash=n;throw i}}),"parseError"),parse:(0,i.K2)((function t(e){var n=this,r=[0],s=[],a=[null],o=[],c=this.table,l="",h=0,d=0,u=0,p=2,f=1;var y=o.slice.call(arguments,1);var g=Object.create(this.lexer);var m={yy:{}};for(var x in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,x)){m.yy[x]=this.yy[x]}}g.setInput(e,m.yy);m.yy.lexer=g;m.yy.parser=this;if(typeof g.yylloc=="undefined"){g.yylloc={}}var b=g.yylloc;o.push(b);var k=g.options&&g.options.ranges;if(typeof m.yy.parseError==="function"){this.parseError=m.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function v(t){r.length=r.length-2*t;a.length=a.length-t;o.length=o.length-t}(0,i.K2)(v,"popStack");function _(){var t;t=s.pop()||g.lex()||f;if(typeof t!=="number"){if(t instanceof Array){s=t;t=s.pop()}t=n.symbols_[t]||t}return t}(0,i.K2)(_,"lex");var w,K,S,$,E,T,I={},R,A,L,M;while(true){S=r[r.length-1];if(this.defaultActions[S]){$=this.defaultActions[S]}else{if(w===null||typeof w=="undefined"){w=_()}$=c[S]&&c[S][w]}if(typeof $==="undefined"||!$.length||!$[0]){var C="";M=[];for(R in c[S]){if(this.terminals_[R]&&R>p){M.push("'"+this.terminals_[R]+"'")}}if(g.showPosition){C="Parse error on line "+(h+1)+":\n"+g.showPosition()+"\nExpecting "+M.join(", ")+", got '"+(this.terminals_[w]||w)+"'"}else{C="Parse error on line "+(h+1)+": Unexpected "+(w==f?"end of input":"'"+(this.terminals_[w]||w)+"'")}this.parseError(C,{text:g.match,token:this.terminals_[w]||w,line:g.yylineno,loc:b,expected:M})}if($[0]instanceof Array&&$.length>1){throw new Error("Parse Error: multiple actions possible at state: "+S+", token: "+w)}switch($[0]){case 1:r.push(w);a.push(g.yytext);o.push(g.yylloc);r.push($[1]);w=null;if(!K){d=g.yyleng;l=g.yytext;h=g.yylineno;b=g.yylloc;if(u>0){u--}}else{w=K;K=null}break;case 2:A=this.productions_[$[1]][1];I.$=a[a.length-A];I._$={first_line:o[o.length-(A||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(A||1)].first_column,last_column:o[o.length-1].last_column};if(k){I._$.range=[o[o.length-(A||1)].range[0],o[o.length-1].range[1]]}T=this.performAction.apply(I,[l,d,h,m.yy,$[1],a,o].concat(y));if(typeof T!=="undefined"){return T}if(A){r=r.slice(0,-1*A*2);a=a.slice(0,-1*A);o=o.slice(0,-1*A)}r.push(this.productions_[$[1]][0]);a.push(I.$);o.push(I._$);L=c[r[r.length-2]][r[r.length-1]];r.push(L);break;case 3:return true}}return true}),"parse")};var d=function(){var t={EOF:1,parseError:(0,i.K2)((function t(e,n){if(this.yy.parser){this.yy.parser.parseError(e,n)}else{throw new Error(e)}}),"parseError"),setInput:(0,i.K2)((function(t,e){this.yy=e||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this}),"setInput"),input:(0,i.K2)((function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);if(e){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t}),"input"),unput:(0,i.K2)((function(t){var e=t.length;var n=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-e);this.offset-=e;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(n.length-1){this.yylineno-=n.length-1}var r=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:n?(n.length===i.length?this.yylloc.first_column:0)+i[i.length-n.length].length-n[0].length:this.yylloc.first_column-e};if(this.options.ranges){this.yylloc.range=[r[0],r[0]+this.yyleng-e]}this.yyleng=this.yytext.length;return this}),"unput"),more:(0,i.K2)((function(){this._more=true;return this}),"more"),reject:(0,i.K2)((function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this}),"reject"),less:(0,i.K2)((function(t){this.unput(this.match.slice(t))}),"less"),pastInput:(0,i.K2)((function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,i.K2)((function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,i.K2)((function(){var t=this.pastInput();var e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"}),"showPosition"),test_match:(0,i.K2)((function(t,e){var n,i,r;if(this.options.backtrack_lexer){r={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){r.yylloc.range=this.yylloc.range.slice(0)}}i=t[0].match(/(?:\r\n?|\n).*/g);if(i){this.yylineno+=i.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];n=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(n){return n}else if(this._backtrack){for(var s in r){this[s]=r[s]}return false}return false}),"test_match"),next:(0,i.K2)((function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,e,n,i;if(!this._more){this.yytext="";this.match=""}var r=this._currentRules();for(var s=0;s<r.length;s++){n=this._input.match(this.rules[r[s]]);if(n&&(!e||n[0].length>e[0].length)){e=n;i=s;if(this.options.backtrack_lexer){t=this.test_match(n,r[s]);if(t!==false){return t}else if(this._backtrack){e=false;continue}else{return false}}else if(!this.options.flex){break}}}if(e){t=this.test_match(e,r[i]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}}),"next"),lex:(0,i.K2)((function t(){var e=this.next();if(e){return e}else{return this.lex()}}),"lex"),begin:(0,i.K2)((function t(e){this.conditionStack.push(e)}),"begin"),popState:(0,i.K2)((function t(){var e=this.conditionStack.length-1;if(e>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}}),"popState"),_currentRules:(0,i.K2)((function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}}),"_currentRules"),topState:(0,i.K2)((function t(e){e=this.conditionStack.length-1-Math.abs(e||0);if(e>=0){return this.conditionStack[e]}else{return"INITIAL"}}),"topState"),pushState:(0,i.K2)((function t(e){this.begin(e)}),"pushState"),stateStackSize:(0,i.K2)((function t(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":true},performAction:(0,i.K2)((function t(e,n,i,r){var s=r;switch(i){case 0:break;case 1:break;case 2:return 10;break;case 3:break;case 4:break;case 5:return 4;break;case 6:return 11;break;case 7:this.begin("acc_title");return 12;break;case 8:this.popState();return"acc_title_value";break;case 9:this.begin("acc_descr");return 14;break;case 10:this.popState();return"acc_descr_value";break;case 11:this.begin("acc_descr_multiline");break;case 12:this.popState();break;case 13:return"acc_descr_multiline_value";break;case 14:return 17;break;case 15:return 21;break;case 16:return 20;break;case 17:return 6;break;case 18:return"INVALID";break}}),"anonymous"),rules:[/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:#[^\n]*)/i,/^(?:timeline\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:section\s[^:\n]+)/i,/^(?::\s[^:\n]+)/i,/^(?:[^#:\n]+)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[12,13],inclusive:false},acc_descr:{rules:[10],inclusive:false},acc_title:{rules:[8],inclusive:false},INITIAL:{rules:[0,1,2,3,4,5,6,7,9,11,14,15,16,17,18],inclusive:true}}};return t}();h.lexer=d;function u(){this.yy={}}(0,i.K2)(u,"Parser");u.prototype=h;h.Parser=u;return new u}();c.parser=c;var l=c;var h={};(0,i.VA)(h,{addEvent:()=>_,addSection:()=>x,addTask:()=>v,addTaskOrg:()=>w,clear:()=>m,default:()=>S,getCommonDb:()=>g,getSections:()=>b,getTasks:()=>k});var d="";var u=0;var p=[];var f=[];var y=[];var g=(0,i.K2)((()=>i.Wt),"getCommonDb");var m=(0,i.K2)((function(){p.length=0;f.length=0;d="";y.length=0;(0,i.IU)()}),"clear");var x=(0,i.K2)((function(t){d=t;p.push(t)}),"addSection");var b=(0,i.K2)((function(){return p}),"getSections");var k=(0,i.K2)((function(){let t=K();const e=100;let n=0;while(!t&&n<e){t=K();n++}f.push(...y);return f}),"getTasks");var v=(0,i.K2)((function(t,e,n){const i={id:u++,section:d,type:d,task:t,score:e?e:0,events:n?[n]:[]};y.push(i)}),"addTask");var _=(0,i.K2)((function(t){const e=y.find((t=>t.id===u-1));e.events.push(t)}),"addEvent");var w=(0,i.K2)((function(t){const e={section:d,type:d,description:t,task:t,classes:[]};f.push(e)}),"addTaskOrg");var K=(0,i.K2)((function(){const t=(0,i.K2)((function(t){return y[t].processed}),"compileTask");let e=true;for(const[n,i]of y.entries()){t(n);e=e&&i.processed}return e}),"compileTasks");var S={clear:m,getCommonDb:g,addSection:x,getSections:b,getTasks:k,addTask:v,addTaskOrg:w,addEvent:_};var $=12;var E=(0,i.K2)((function(t,e){const n=t.append("rect");n.attr("x",e.x);n.attr("y",e.y);n.attr("fill",e.fill);n.attr("stroke",e.stroke);n.attr("width",e.width);n.attr("height",e.height);n.attr("rx",e.rx);n.attr("ry",e.ry);if(e.class!==void 0){n.attr("class",e.class)}return n}),"drawRect");var T=(0,i.K2)((function(t,e){const n=15;const s=t.append("circle").attr("cx",e.cx).attr("cy",e.cy).attr("class","face").attr("r",n).attr("stroke-width",2).attr("overflow","visible");const a=t.append("g");a.append("circle").attr("cx",e.cx-n/3).attr("cy",e.cy-n/3).attr("r",1.5).attr("stroke-width",2).attr("fill","#666").attr("stroke","#666");a.append("circle").attr("cx",e.cx+n/3).attr("cy",e.cy-n/3).attr("r",1.5).attr("stroke-width",2).attr("fill","#666").attr("stroke","#666");function o(t){const i=(0,r.JLW)().startAngle(Math.PI/2).endAngle(3*(Math.PI/2)).innerRadius(n/2).outerRadius(n/2.2);t.append("path").attr("class","mouth").attr("d",i).attr("transform","translate("+e.cx+","+(e.cy+2)+")")}(0,i.K2)(o,"smile");function c(t){const i=(0,r.JLW)().startAngle(3*Math.PI/2).endAngle(5*(Math.PI/2)).innerRadius(n/2).outerRadius(n/2.2);t.append("path").attr("class","mouth").attr("d",i).attr("transform","translate("+e.cx+","+(e.cy+7)+")")}(0,i.K2)(c,"sad");function l(t){t.append("line").attr("class","mouth").attr("stroke",2).attr("x1",e.cx-5).attr("y1",e.cy+7).attr("x2",e.cx+5).attr("y2",e.cy+7).attr("class","mouth").attr("stroke-width","1px").attr("stroke","#666")}(0,i.K2)(l,"ambivalent");if(e.score>3){o(a)}else if(e.score<3){c(a)}else{l(a)}return s}),"drawFace");var I=(0,i.K2)((function(t,e){const n=t.append("circle");n.attr("cx",e.cx);n.attr("cy",e.cy);n.attr("class","actor-"+e.pos);n.attr("fill",e.fill);n.attr("stroke",e.stroke);n.attr("r",e.r);if(n.class!==void 0){n.attr("class",n.class)}if(e.title!==void 0){n.append("title").text(e.title)}return n}),"drawCircle");var R=(0,i.K2)((function(t,e){const n=e.text.replace(/<br\s*\/?>/gi," ");const i=t.append("text");i.attr("x",e.x);i.attr("y",e.y);i.attr("class","legend");i.style("text-anchor",e.anchor);if(e.class!==void 0){i.attr("class",e.class)}const r=i.append("tspan");r.attr("x",e.x+e.textMargin*2);r.text(n);return i}),"drawText");var A=(0,i.K2)((function(t,e){function n(t,e,n,i,r){return t+","+e+" "+(t+n)+","+e+" "+(t+n)+","+(e+i-r)+" "+(t+n-r*1.2)+","+(e+i)+" "+t+","+(e+i)}(0,i.K2)(n,"genPoints");const r=t.append("polygon");r.attr("points",n(e.x,e.y,50,20,7));r.attr("class","labelBox");e.y=e.y+e.labelMargin;e.x=e.x+.5*e.labelMargin;R(t,e)}),"drawLabel");var L=(0,i.K2)((function(t,e,n){const i=t.append("g");const r=H();r.x=e.x;r.y=e.y;r.fill=e.fill;r.width=n.width;r.height=n.height;r.class="journey-section section-type-"+e.num;r.rx=3;r.ry=3;E(i,r);O(n)(e.text,i,r.x,r.y,r.width,r.height,{class:"journey-section section-type-"+e.num},n,e.colour)}),"drawSection");var M=-1;var C=(0,i.K2)((function(t,e,n){const i=e.x+n.width/2;const r=t.append("g");M++;const s=300+5*30;r.append("line").attr("id","task"+M).attr("x1",i).attr("y1",e.y).attr("x2",i).attr("y2",s).attr("class","task-line").attr("stroke-width","1px").attr("stroke-dasharray","4 2").attr("stroke","#666");T(r,{cx:i,cy:300+(5-e.score)*30,score:e.score});const a=H();a.x=e.x;a.y=e.y;a.fill=e.fill;a.width=n.width;a.height=n.height;a.class="task task-type-"+e.num;a.rx=3;a.ry=3;E(r,a);O(n)(e.task,r,a.x,a.y,a.width,a.height,{class:"task"},n,e.colour)}),"drawTask");var N=(0,i.K2)((function(t,e){const n=E(t,{x:e.startx,y:e.starty,width:e.stopx-e.startx,height:e.stopy-e.starty,fill:e.fill,class:"rect"});n.lower()}),"drawBackgroundRect");var P=(0,i.K2)((function(){return{x:0,y:0,fill:void 0,"text-anchor":"start",width:100,height:100,textMargin:0,rx:0,ry:0}}),"getTextObj");var H=(0,i.K2)((function(){return{x:0,y:0,width:100,anchor:"start",height:100,rx:0,ry:0}}),"getNoteRect");var O=function(){function t(t,e,n,i,s,a,o,c){const l=e.append("text").attr("x",n+s/2).attr("y",i+a/2+5).style("font-color",c).style("text-anchor","middle").text(t);r(l,o)}(0,i.K2)(t,"byText");function e(t,e,n,i,s,a,o,c,l){const{taskFontSize:h,taskFontFamily:d}=c;const u=t.split(/<br\s*\/?>/gi);for(let p=0;p<u.length;p++){const t=p*h-h*(u.length-1)/2;const c=e.append("text").attr("x",n+s/2).attr("y",i).attr("fill",l).style("text-anchor","middle").style("font-size",h).style("font-family",d);c.append("tspan").attr("x",n+s/2).attr("dy",t).text(u[p]);c.attr("y",i+a/2).attr("dominant-baseline","central").attr("alignment-baseline","central");r(c,o)}}(0,i.K2)(e,"byTspan");function n(t,n,i,s,a,o,c,l){const h=n.append("switch");const d=h.append("foreignObject").attr("x",i).attr("y",s).attr("width",a).attr("height",o).attr("position","fixed");const u=d.append("xhtml:div").style("display","table").style("height","100%").style("width","100%");u.append("div").attr("class","label").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t);e(t,h,i,s,a,o,c,l);r(u,c)}(0,i.K2)(n,"byFo");function r(t,e){for(const n in e){if(n in e){t.attr(n,e[n])}}}(0,i.K2)(r,"_setTextAttrs");return function(i){return i.textPlacement==="fo"?n:i.textPlacement==="old"?t:e}}();var j=(0,i.K2)((function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",5).attr("refY",2).attr("markerWidth",6).attr("markerHeight",4).attr("orient","auto").append("path").attr("d","M 0,0 V 4 L6,2 Z")}),"initGraphics");function D(t,e){t.each((function(){var t=(0,r.Ltv)(this),n=t.text().split(/(\s+|<br>)/).reverse(),i,s=[],a=1.1,o=t.attr("y"),c=parseFloat(t.attr("dy")),l=t.text(null).append("tspan").attr("x",0).attr("y",o).attr("dy",c+"em");for(let r=0;r<n.length;r++){i=n[n.length-1-r];s.push(i);l.text(s.join(" ").trim());if(l.node().getComputedTextLength()>e||i==="<br>"){s.pop();l.text(s.join(" ").trim());if(i==="<br>"){s=[""]}else{s=[i]}l=t.append("tspan").attr("x",0).attr("y",o).attr("dy",a+"em").text(i)}}}))}(0,i.K2)(D,"wrap");var z=(0,i.K2)((function(t,e,n,i){const r=n%$-1;const s=t.append("g");e.section=r;s.attr("class",(e.class?e.class+" ":"")+"timeline-node "+("section-"+r));const a=s.append("g");const o=s.append("g");const c=o.append("text").text(e.descr).attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle").call(D,e.width);const l=c.node().getBBox();const h=i.fontSize?.replace?i.fontSize.replace("px",""):i.fontSize;e.height=l.height+h*1.1*.5+e.padding;e.height=Math.max(e.height,e.maxHeight);e.width=e.width+2*e.padding;o.attr("transform","translate("+e.width/2+", "+e.padding/2+")");B(a,e,r,i);return e}),"drawNode");var W=(0,i.K2)((function(t,e,n){const i=t.append("g");const r=i.append("text").text(e.descr).attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle").call(D,e.width);const s=r.node().getBBox();const a=n.fontSize?.replace?n.fontSize.replace("px",""):n.fontSize;i.remove();return s.height+a*1.1*.5+e.padding}),"getVirtualNodeHeight");var B=(0,i.K2)((function(t,e,n){const i=5;t.append("path").attr("id","node-"+e.id).attr("class","node-bkg node-"+e.type).attr("d",`M0 ${e.height-i} v${-e.height+2*i} q0,-5 5,-5 h${e.width-2*i} q5,0 5,5 v${e.height-i} H0 Z`);t.append("line").attr("class","node-line-"+n).attr("x1",0).attr("y1",e.height).attr("x2",e.width).attr("y2",e.height)}),"defaultBkg");var F={drawRect:E,drawCircle:I,drawSection:L,drawText:R,drawLabel:A,drawTask:C,drawBackgroundRect:N,getTextObj:P,getNoteRect:H,initGraphics:j,drawNode:z,getVirtualNodeHeight:W};var V=(0,i.K2)((function(t,e,n,s){const a=(0,i.D7)();const o=a.leftMargin??50;i.Rm.debug("timeline",s.db);const c=a.securityLevel;let l;if(c==="sandbox"){l=(0,r.Ltv)("#i"+e)}const h=c==="sandbox"?(0,r.Ltv)(l.nodes()[0].contentDocument.body):(0,r.Ltv)("body");const d=h.select("#"+e);d.append("g");const u=s.db.getTasks();const p=s.db.getCommonDb().getDiagramTitle();i.Rm.debug("task",u);F.initGraphics(d);const f=s.db.getSections();i.Rm.debug("sections",f);let y=0;let g=0;let m=0;let x=0;let b=50+o;let k=50;x=50;let v=0;let _=true;f.forEach((function(t){const e={number:v,descr:t,section:v,width:150,padding:20,maxHeight:y};const n=F.getVirtualNodeHeight(d,e,a);i.Rm.debug("sectionHeight before draw",n);y=Math.max(y,n+20)}));let w=0;let K=0;i.Rm.debug("tasks.length",u.length);for(const[r,E]of u.entries()){const t={number:r,descr:E,section:E.section,width:150,padding:20,maxHeight:g};const e=F.getVirtualNodeHeight(d,t,a);i.Rm.debug("taskHeight before draw",e);g=Math.max(g,e+20);w=Math.max(w,E.events.length);let n=0;for(const i of E.events){const t={descr:i,section:E.section,number:E.section,width:150,padding:20,maxHeight:50};n+=F.getVirtualNodeHeight(d,t,a)}K=Math.max(K,n)}i.Rm.debug("maxSectionHeight before draw",y);i.Rm.debug("maxTaskHeight before draw",g);if(f&&f.length>0){f.forEach((t=>{const e=u.filter((e=>e.section===t));const n={number:v,descr:t,section:v,width:200*Math.max(e.length,1)-50,padding:20,maxHeight:y};i.Rm.debug("sectionNode",n);const r=d.append("g");const s=F.drawNode(r,n,v,a);i.Rm.debug("sectionNode output",s);r.attr("transform",`translate(${b}, ${x})`);k+=y+50;if(e.length>0){G(d,e,v,b,k,g,a,w,K,y,false)}b+=200*Math.max(e.length,1);k=x;v++}))}else{_=false;G(d,u,v,b,k,g,a,w,K,y,true)}const S=d.node().getBBox();i.Rm.debug("bounds",S);if(p){d.append("text").text(p).attr("x",S.width/2-o).attr("font-size","4ex").attr("font-weight","bold").attr("y",20)}m=_?y+g+150:g+100;const $=d.append("g").attr("class","lineWrapper");$.append("line").attr("x1",o).attr("y1",m).attr("x2",S.width+3*o).attr("y2",m).attr("stroke-width",4).attr("stroke","black").attr("marker-end","url(#arrowhead)");(0,i.ot)(void 0,d,a.timeline?.padding??50,a.timeline?.useMaxWidth??false)}),"draw");var G=(0,i.K2)((function(t,e,n,r,s,a,o,c,l,h,d){for(const u of e){const e={descr:u.task,section:n,number:n,width:150,padding:20,maxHeight:a};i.Rm.debug("taskNode",e);const c=t.append("g").attr("class","taskWrapper");const p=F.drawNode(c,e,n,o);const f=p.height;i.Rm.debug("taskHeight after draw",f);c.attr("transform",`translate(${r}, ${s})`);a=Math.max(a,f);if(u.events){const e=t.append("g").attr("class","lineWrapper");let i=a;s+=100;i=i+U(t,u.events,n,r,s,o);s-=100;e.append("line").attr("x1",r+190/2).attr("y1",s+a).attr("x2",r+190/2).attr("y2",s+a+(d?a:h)+l+120).attr("stroke-width",2).attr("stroke","black").attr("marker-end","url(#arrowhead)").attr("stroke-dasharray","5,5")}r=r+200;if(d&&!o.timeline?.disableMulticolor){n++}}s=s-10}),"drawTasks");var U=(0,i.K2)((function(t,e,n,r,s,a){let o=0;const c=s;s=s+100;for(const l of e){const e={descr:l,section:n,number:n,width:150,padding:20,maxHeight:50};i.Rm.debug("eventNode",e);const c=t.append("g").attr("class","eventWrapper");const h=F.drawNode(c,e,n,a);const d=h.height;o=o+d;c.attr("transform",`translate(${r}, ${s})`);s=s+10+d}s=c;return o}),"drawEvents");var q={setConf:(0,i.K2)((()=>{}),"setConf"),draw:V};var J=(0,i.K2)((t=>{let e="";for(let n=0;n<t.THEME_COLOR_LIMIT;n++){t["lineColor"+n]=t["lineColor"+n]||t["cScaleInv"+n];if((0,s.A)(t["lineColor"+n])){t["lineColor"+n]=(0,a.A)(t["lineColor"+n],20)}else{t["lineColor"+n]=(0,o.A)(t["lineColor"+n],20)}}for(let n=0;n<t.THEME_COLOR_LIMIT;n++){const i=""+(17-3*n);e+=`\n    .section-${n-1} rect, .section-${n-1} path, .section-${n-1} circle, .section-${n-1} path  {\n      fill: ${t["cScale"+n]};\n    }\n    .section-${n-1} text {\n     fill: ${t["cScaleLabel"+n]};\n    }\n    .node-icon-${n-1} {\n      font-size: 40px;\n      color: ${t["cScaleLabel"+n]};\n    }\n    .section-edge-${n-1}{\n      stroke: ${t["cScale"+n]};\n    }\n    .edge-depth-${n-1}{\n      stroke-width: ${i};\n    }\n    .section-${n-1} line {\n      stroke: ${t["cScaleInv"+n]} ;\n      stroke-width: 3;\n    }\n\n    .lineWrapper line{\n      stroke: ${t["cScaleLabel"+n]} ;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n    `}return e}),"genSections");var Y=(0,i.K2)((t=>`\n  .edge {\n    stroke-width: 3;\n  }\n  ${J(t)}\n  .section-root rect, .section-root path, .section-root circle  {\n    fill: ${t.git0};\n  }\n  .section-root text {\n    fill: ${t.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .eventWrapper  {\n   filter: brightness(120%);\n  }\n`),"getStyles");var Z=Y;var X={db:h,renderer:q,parser:l,styles:Z}}}]);