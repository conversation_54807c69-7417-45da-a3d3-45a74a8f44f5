/**
 * 響應式設計除錯工具
 * 幫助檢測不同螢幕尺寸下的顯示問題
 */

// 常見的筆電螢幕尺寸
const LAPTOP_SCREEN_SIZES = [
  { name: '13" MacBook Air', width: 1440, height: 900 },
  { name: '13" MacBook Pro', width: 1440, height: 900 },
  { name: '14" MacBook Pro', width: 1512, height: 982 },
  { name: '15" MacBook Pro', width: 1680, height: 1050 },
  { name: '13" Windows Laptop', width: 1366, height: 768 },
  { name: '14" Windows Laptop', width: 1920, height: 1080 },
  { name: '15" Windows Laptop', width: 1920, height: 1080 },
  { name: '小型筆電', width: 1280, height: 720 },
];

// 檢查當前螢幕尺寸
export const getCurrentScreenInfo = () => {
  const width = window.innerWidth;
  const height = window.innerHeight;
  const devicePixelRatio = window.devicePixelRatio || 1;
  
  // 計算實際像素
  const actualWidth = width * devicePixelRatio;
  const actualHeight = height * devicePixelRatio;
  
  // 判斷螢幕類型
  let screenType = 'Unknown';
  if (width < 768) {
    screenType = 'Mobile';
  } else if (width < 1024) {
    screenType = 'Tablet';
  } else if (width < 1440) {
    screenType = 'Small Laptop';
  } else if (width < 1920) {
    screenType = 'Large Laptop';
  } else {
    screenType = 'Desktop';
  }
  
  return {
    viewport: { width, height },
    actual: { width: actualWidth, height: actualHeight },
    devicePixelRatio,
    screenType
  };
};

// 檢查 Banner 是否會被裁切
export const checkBannerClipping = () => {
  const screenInfo = getCurrentScreenInfo();
  const { width, height } = screenInfo.viewport;
  
  // Banner 的最小內容高度需求
  const minContentHeight = {
    xs: 400,  // 手機
    sm: 450,  // 平板
    md: 500,  // 小筆電
    lg: 320   // 大螢幕（使用 40vh）
  };
  
  let breakpoint = 'xs';
  if (width >= 1200) breakpoint = 'lg';
  else if (width >= 900) breakpoint = 'md';
  else if (width >= 600) breakpoint = 'sm';
  
  const requiredHeight = minContentHeight[breakpoint];
  const availableHeight = breakpoint === 'lg' ? height * 0.4 : requiredHeight;
  
  const isClipped = availableHeight < requiredHeight;
  
  return {
    screenInfo,
    breakpoint,
    requiredHeight,
    availableHeight,
    isClipped,
    recommendations: isClipped ? getClippingRecommendations(screenInfo) : []
  };
};

// 獲取裁切問題的建議
const getClippingRecommendations = (screenInfo) => {
  const recommendations = [];
  
  if (screenInfo.viewport.height < 800) {
    recommendations.push('螢幕高度較小，建議減少 Banner 內容或使用垂直滾動');
  }
  
  if (screenInfo.viewport.width < 1366) {
    recommendations.push('螢幕寬度較小，建議使用垂直佈局');
  }
  
  if (screenInfo.devicePixelRatio > 1.5) {
    recommendations.push('高 DPI 螢幕，注意圖片清晰度');
  }
  
  return recommendations;
};

// 顯示響應式除錯資訊
export const showResponsiveDebugInfo = () => {
  const bannerCheck = checkBannerClipping();
  
  console.group('📱 響應式設計除錯資訊');
  console.log('螢幕資訊:', bannerCheck.screenInfo);
  console.log('當前斷點:', bannerCheck.breakpoint);
  console.log('所需高度:', bannerCheck.requiredHeight + 'px');
  console.log('可用高度:', bannerCheck.availableHeight + 'px');
  console.log('是否被裁切:', bannerCheck.isClipped ? '❌ 是' : '✅ 否');
  
  if (bannerCheck.recommendations.length > 0) {
    console.log('建議:');
    bannerCheck.recommendations.forEach(rec => console.log('  -', rec));
  }
  console.groupEnd();
  
  return bannerCheck;
};

// 測試不同螢幕尺寸
export const testDifferentScreenSizes = () => {
  console.group('💻 筆電螢幕尺寸測試');
  
  LAPTOP_SCREEN_SIZES.forEach(screen => {
    // 模擬螢幕尺寸
    const mockWindow = {
      innerWidth: screen.width,
      innerHeight: screen.height,
      devicePixelRatio: 1
    };
    
    // 計算 Banner 高度
    const vh40 = screen.height * 0.4;
    const minHeight = screen.width < 1200 ? 500 : vh40;
    
    const isClipped = minHeight > vh40 && screen.width >= 1200;
    
    console.log(`${screen.name} (${screen.width}x${screen.height}):`);
    console.log(`  40vh = ${vh40}px, 最小高度 = ${minHeight}px`);
    console.log(`  狀態: ${isClipped ? '❌ 可能被裁切' : '✅ 正常顯示'}`);
  });
  
  console.groupEnd();
};

// 監聽視窗大小變化
export const startResponsiveMonitoring = () => {
  let resizeTimeout;
  
  const handleResize = () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      const debugInfo = showResponsiveDebugInfo();
      
      // 如果檢測到裁切問題，顯示警告
      if (debugInfo.isClipped) {
        console.warn('⚠️ Banner 可能在當前螢幕尺寸下被裁切！');
      }
    }, 300);
  };
  
  window.addEventListener('resize', handleResize);
  
  // 初始檢查
  setTimeout(() => {
    showResponsiveDebugInfo();
    testDifferentScreenSizes();
  }, 1000);
  
  return () => {
    window.removeEventListener('resize', handleResize);
    clearTimeout(resizeTimeout);
  };
};

// 在開發模式下自動啟動監控
if (process.env.NODE_ENV === 'development') {
  // 延遲啟動，確保頁面已載入
  setTimeout(() => {
    startResponsiveMonitoring();
  }, 2000);
}
