# 2025 第四屆金匠獎
## 選題：題目二、演唱會票務平台
### 題目內容
建置一套整合性的演唱會票務平臺，能同時處理數場活動的票務銷售，並可因應高流量、高併發的搶票行為。

### 基本功能項目
1. 活動展示功能：多位歌手、多活動資訊展示（時間、地點、票價），支援搜尋與篩選。
2. 使用者管理：註冊、登入、身分驗證；訂單查詢與票券管理。
3. 購票流程：選擇場次、區域、張數；模擬付款與完成購票流程。
4. 後台管理：主辦方可上架活動、設計票種與票價；查詢銷售紀錄與剩餘票數。
5. 高併發處理：必須能處理模擬「千人同時搶票」的情境；防止超賣與資料錯亂。
6. 風控機制：限購機制、CAPTCHA、防黃牛、IP限制等。
7. 支援RWD響應式介面。

### 加分項目
1. 效能處理：使用快取、排隊機制、佇列系統...等技術處理併發請求。
2. 即時監控與視覺化：顯示票券銷售熱度圖、剩餘票統計、場次熱門排行榜...等。
3. AI加值功能：預測熱門場次 、推薦演唱會給使用者、智能問答助理...等。
4. DevOps實務：使用 CI/CD 工具部署、自動化測試、容器化...等。
5. 使用者體驗強化：支援座位圖視覺化選位、快速搜尋與收藏功能...等。

### 前端所有功能
---
金匠獎前端
主要頁面
登入介面
註冊介面
探索活動
就是把所有能訂票的活動都放這邊

對於每個活動

主介紹頁面
購買介面（這邊可以選要買那個場次？買多少？）
個人資料填寫介面
付錢介面
購買結果介面
功能介面區
會員登入
登入介面
要至少有以下的功能(input)

帳號
密碼
驗證碼
然後兩個按鈕

登入鈕
登出鈕
註冊介面
要至少有以下的功能(input)

使用者名稱
Email
密碼
密碼確認
出生年月日
生理性別
機器人辨別
其他可能功能(input)

偏好活動地區
然後一個按鈕

註冊鈕
探索活動
放廣告的地方
一些活動的類別(讓使用者能依照喜好去看)
像是學習、演出、展覽、聯誼…
各個活動的路口
搜尋input
回主頁的地方
主介紹頁面
簡介(反正就是一堆字)
節目資訊
時間
地點
價格
開放買票的時間
票區圖
就是一堆位子，讓人看可以選哪的
重要事項
反正就是一堆規則
購票方式說明
不然使用者可能不會購票
注意事項
更多規則，但可能跟法律有關
活動票券
票種
販售時間
售價
還有搭配購票的按鈕
購買介面（這邊可以選要買那個場次？買多少？）
跟上面的活動票券其實差不多
但你可以

選票種
選票的數量
購買鍵
我已經閱讀並同意服務條款與隱私權等按鈕
個人資料填寫介面
上面可能要顯示你買了啥
姓名
Email
手機
付錢介面
就是讓你填信用卡的地方
啊我沒去查，我沒有票要買:3
購買結果介面
人名
票的資訊
花的錢
可能有個Qrcode
時間
購買了什麼票介面
顯示你買了啥票
那些票的資訊
點進去可能可以用
---

# 我的分配任務-負責網頁前端撰寫

## 目前環境 React (JavaScript) + TailwindCSS


## 主頁部分

主要頁面包含：

登入介面

註冊介面

探索活動

就是把所有能訂票的活動都放這邊（路由）

### 功能清單

顯示探索活動 Banner

導到登入介面

導到註冊介面

增加項目