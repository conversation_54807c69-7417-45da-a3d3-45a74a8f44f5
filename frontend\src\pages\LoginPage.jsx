import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  Divider
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  AccountCircle,
  Lock,
  Security
} from '@mui/icons-material';

function LoginPage() {
  const navigate = useNavigate();
  const { login } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const result = await login(formData.email, formData.password);

      if (result.success) {
        // 登入成功，導向首頁或個人頁面
        if (result.user.is_admin) {
          navigate('/admin');
        } else {
          navigate('/');
        }
      } else {
        setError(result.error || '登入失敗');
      }
    } catch (error) {
      setError('登入失敗，請稍後再試');
    } finally {
      setLoading(false);
    }
  };

  const containerStyle = {
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    background: 'linear-gradient(135deg, #1a237e 0%, #121212 50%, #1a237e 100%)',
    position: 'relative',
    overflow: 'hidden'
  };

  const paperStyle = {
    padding: '3rem',
    backgroundColor: 'rgba(36, 36, 36, 0.95)',
    backdropFilter: 'blur(10px)',
    border: '1px solid #00c0ff',
    borderRadius: '0',
    clipPath: 'polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 20px 100%, 0 calc(100% - 20px))',
    color: 'white',
    maxWidth: '450px',
    width: '100%',
    position: 'relative',
    boxShadow: '0 0 30px rgba(0, 192, 255, 0.3)'
  };

  const textFieldStyle = {
    '& .MuiOutlinedInput-root': {
      color: 'white',
      '& fieldset': {
        borderColor: '#444',
        borderWidth: '1px'
      },
      '&:hover fieldset': {
        borderColor: '#00c0ff'
      },
      '&.Mui-focused fieldset': {
        borderColor: '#00c0ff',
        borderWidth: '2px'
      }
    },
    '& .MuiInputLabel-root': {
      color: '#aaa',
      '&.Mui-focused': {
        color: '#00c0ff'
      }
    },
    '& .MuiInputAdornment-root .MuiSvgIcon-root': {
      color: '#00c0ff'
    }
  };

  const buttonStyle = {
    backgroundColor: '#00c0ff',
    color: 'black',
    fontWeight: 'bold',
    padding: '12px 0',
    clipPath: 'polygon(10% 0, 100% 0, 90% 100%, 0% 100%)',
    '&:hover': {
      backgroundColor: '#64d8ff',
      boxShadow: '0 0 20px rgba(0, 192, 255, 0.5)'
    },
    '&:disabled': {
      backgroundColor: '#555',
      color: '#888'
    }
  };

  return (
    <Box sx={containerStyle}>
      {/* 背景裝飾 */}
      <Box sx={{
        position: 'absolute',
        top: 0, left: 0, width: '100%', height: '100%',
        backgroundImage: 'url(https://www.transparenttextures.com/patterns/hexellence.png)',
        opacity: 0.1,
        zIndex: 0
      }}/>

      <Container maxWidth="sm">
        <Paper elevation={0} sx={paperStyle}>
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography variant="h4" component="h1" sx={{
              fontWeight: 'bold',
              color: '#00c0ff',
              textShadow: '0 0 10px #00c0ff',
              mb: 1
            }}>
              系統登入
            </Typography>
            <Typography variant="body2" sx={{ color: '#aaa' }}>
              歡迎回到 Synctix
            </Typography>
          </Box>

          {error && (
            <Alert severity="error" sx={{
              mb: 3,
              backgroundColor: 'rgba(211, 47, 47, 0.1)',
              color: '#ff6b6b',
              border: '1px solid rgba(211, 47, 47, 0.3)'
            }}>
              {error}
            </Alert>
          )}

          <form onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label="電子郵件"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              required
              sx={{ ...textFieldStyle, mb: 3 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <AccountCircle />
                  </InputAdornment>
                )
              }}
            />

            <TextField
              fullWidth
              label="密碼"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleInputChange}
              required
              sx={{ ...textFieldStyle, mb: 3 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                      sx={{ color: '#00c0ff' }}
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />



            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={loading}
              sx={{ ...buttonStyle, mb: 3 }}
            >
              {loading ? '登入中...' : '登入'}
            </Button>

            <Divider sx={{ backgroundColor: '#444', mb: 3 }} />

            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" sx={{ color: '#aaa', mb: 2 }}>
                還沒有帳號嗎？
              </Typography>
              <Button
                component={Link}
                to="/register"
                variant="outlined"
                sx={{
                  color: '#00c0ff',
                  borderColor: '#00c0ff',
                  clipPath: 'polygon(5% 0, 100% 0, 95% 100%, 0% 100%)',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 192, 255, 0.1)',
                    borderColor: '#64d8ff'
                  }
                }}
              >
                立即註冊
              </Button>
            </Box>
          </form>
        </Paper>
      </Container>
    </Box>
  );
}

export default LoginPage;
