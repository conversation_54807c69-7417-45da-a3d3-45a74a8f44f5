{".class": "MypyFile", "_fullname": "app.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Base": {".class": "SymbolTableNode", "cross_ref": "app.database.Base", "kind": "Gdef"}, "Boolean": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.models.Boolean", "name": "Boolean", "type": {".class": "AnyType", "missing_import_name": "app.models.Boolean", "source_any": null, "type_of_any": 3}}}, "Column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.models.Column", "name": "Column", "type": {".class": "AnyType", "missing_import_name": "app.models.Column", "source_any": null, "type_of_any": 3}}}, "Integer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.models.Integer", "name": "Integer", "type": {".class": "AnyType", "missing_import_name": "app.models.Integer", "source_any": null, "type_of_any": 3}}}, "String": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.models.String", "name": "String", "type": {".class": "AnyType", "missing_import_name": "app.models.String", "source_any": null, "type_of_any": 3}}}, "User": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.models.User", "name": "User", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.models.User", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.models", "mro": ["app.models.User", "builtins.object"], "names": {".class": "SymbolTable", "__tablename__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.models.User.__tablename__", "name": "__tablename__", "type": "builtins.str"}}, "email": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.User.email", "name": "email", "type": {".class": "AnyType", "missing_import_name": "app.models.Column", "source_any": {".class": "AnyType", "missing_import_name": "app.models.Column", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "google_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.User.google_id", "name": "google_id", "type": {".class": "AnyType", "missing_import_name": "app.models.Column", "source_any": {".class": "AnyType", "missing_import_name": "app.models.Column", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "hashed_password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.User.hashed_password", "name": "hashed_password", "type": {".class": "AnyType", "missing_import_name": "app.models.Column", "source_any": {".class": "AnyType", "missing_import_name": "app.models.Column", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.User.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": "app.models.Column", "source_any": {".class": "AnyType", "missing_import_name": "app.models.Column", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "is_active": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.User.is_active", "name": "is_active", "type": {".class": "AnyType", "missing_import_name": "app.models.Column", "source_any": {".class": "AnyType", "missing_import_name": "app.models.Column", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "username": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.User.username", "name": "username", "type": {".class": "AnyType", "missing_import_name": "app.models.Column", "source_any": {".class": "AnyType", "missing_import_name": "app.models.Column", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.User.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.models.User", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "D:\\Repos\\gjunedu-platinum-2025\\pybackend\\src\\app\\models.py"}