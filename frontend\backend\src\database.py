"""
簡易 JSON 資料庫系統
支援數據持久化和基本 CRUD 操作
"""

import json
import os
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
import threading

class SimpleJSONDB:
    """簡易 JSON 資料庫"""
    
    def __init__(self, base_dir: str):
        self.base_dir = base_dir
        self.data_dir = os.path.join(base_dir, "data")
        self.lock = threading.Lock()
        
        # 確保資料目錄存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 初始化各個資料表
        self.tables = {
            "concerts": "concerts.json",
            "users": "users.json", 
            "orders": "orders.json",
            "hero_banners": "hero_banners.json"
        }
        
        # 載入所有資料
        self.data = {}
        for table_name, filename in self.tables.items():
            self.data[table_name] = self._load_table(filename)
    
    def _load_table(self, filename: str) -> List[Dict]:
        """載入資料表"""
        file_path = os.path.join(self.data_dir, filename)
        if os.path.exists(file_path):
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        return data
                    else:
                        return []
            except (json.JSONDecodeError, Exception) as e:
                print(f"載入 {filename} 時發生錯誤: {e}")
                return []
        return []
    
    def _save_table(self, table_name: str) -> bool:
        """儲存資料表"""
        if table_name not in self.tables:
            return False
            
        filename = self.tables[table_name]
        file_path = os.path.join(self.data_dir, filename)
        
        try:
            with self.lock:
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(self.data[table_name], f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"儲存 {filename} 時發生錯誤: {e}")
            return False
    
    def get_all(self, table_name: str) -> List[Dict]:
        """獲取資料表所有記錄"""
        return self.data.get(table_name, []).copy()
    
    def get_by_id(self, table_name: str, record_id: Any) -> Optional[Dict]:
        """根據 ID 獲取記錄"""
        for record in self.data.get(table_name, []):
            if record.get("id") == record_id:
                return record.copy()
        return None
    
    def get_by_field(self, table_name: str, field: str, value: Any) -> List[Dict]:
        """根據欄位值獲取記錄"""
        results = []
        for record in self.data.get(table_name, []):
            if record.get(field) == value:
                results.append(record.copy())
        return results
    
    def insert(self, table_name: str, record: Dict) -> bool:
        """插入新記錄"""
        if table_name not in self.data:
            self.data[table_name] = []
        
        # 如果沒有 ID，自動生成
        if "id" not in record:
            record["id"] = str(uuid.uuid4())
        
        # 添加時間戳
        if "created_at" not in record:
            record["created_at"] = datetime.now().isoformat()
        
        record["updated_at"] = datetime.now().isoformat()
        
        self.data[table_name].append(record)
        return self._save_table(table_name)
    
    def update(self, table_name: str, record_id: Any, updates: Dict) -> bool:
        """更新記錄"""
        for i, record in enumerate(self.data.get(table_name, [])):
            if record.get("id") == record_id:
                # 更新欄位
                record.update(updates)
                record["updated_at"] = datetime.now().isoformat()
                return self._save_table(table_name)
        return False
    
    def delete(self, table_name: str, record_id: Any) -> bool:
        """刪除記錄"""
        original_length = len(self.data.get(table_name, []))
        self.data[table_name] = [
            record for record in self.data.get(table_name, [])
            if record.get("id") != record_id
        ]
        
        if len(self.data[table_name]) < original_length:
            return self._save_table(table_name)
        return False
    
    def query(self, table_name: str, filters: Dict = None, 
              sort_by: str = None, reverse: bool = False,
              limit: int = None, offset: int = 0) -> List[Dict]:
        """查詢記錄"""
        records = self.data.get(table_name, []).copy()
        
        # 篩選
        if filters:
            for field, value in filters.items():
                if isinstance(value, list):
                    # 支援 IN 查詢
                    records = [r for r in records if r.get(field) in value]
                else:
                    records = [r for r in records if r.get(field) == value]
        
        # 排序
        if sort_by:
            try:
                records.sort(key=lambda x: x.get(sort_by, ""), reverse=reverse)
            except Exception:
                pass
        
        # 分頁
        if limit:
            records = records[offset:offset + limit]
        else:
            records = records[offset:]
        
        return records
    
    def count(self, table_name: str, filters: Dict = None) -> int:
        """計算記錄數量"""
        records = self.data.get(table_name, [])
        
        if filters:
            for field, value in filters.items():
                if isinstance(value, list):
                    records = [r for r in records if r.get(field) in value]
                else:
                    records = [r for r in records if r.get(field) == value]
        
        return len(records)
    
    def backup(self, backup_dir: str) -> bool:
        """備份所有資料"""
        try:
            os.makedirs(backup_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            for table_name, filename in self.tables.items():
                backup_filename = f"{timestamp}_{filename}"
                backup_path = os.path.join(backup_dir, backup_filename)
                
                with open(backup_path, "w", encoding="utf-8") as f:
                    json.dump(self.data[table_name], f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"備份失敗: {e}")
            return False

# 全域資料庫實例
db = None

def init_database(base_dir: str) -> SimpleJSONDB:
    """初始化資料庫"""
    global db
    db = SimpleJSONDB(base_dir)
    return db

def get_database() -> SimpleJSONDB:
    """獲取資料庫實例"""
    global db
    if db is None:
        raise RuntimeError("資料庫尚未初始化，請先調用 init_database()")
    return db
