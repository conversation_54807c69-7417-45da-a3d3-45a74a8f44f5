import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import {
  Box,
  Container,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  TextField,
  Button,
  InputAdornment,
  Pagination
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import HeroBanner from '../components/HeroBanner';
import CategoryFilter from '../components/CategoryFilter';

// --- 明日方舟風格卡片元件 ---
const ArknightsCard = ({ concert }) => {
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);

  const handleCardClick = () => {
    navigate(`/event/${concert.id}`);
  };

  const cardStyle = {
    height: '100%',
    backgroundColor: '#242424',
    color: 'rgba(255, 255, 255, 0.87)',
    border: '1px solid #444',
    clipPath: 'polygon(0 0, 100% 0, 100% calc(100% - 20px), calc(100% - 20px) 100%, 0 100%)',
    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
    transform: isHovered ? 'translateY(-5px)' : 'translateY(0)',
    boxShadow: isHovered ? '0 0 15px rgba(0, 192, 255, 0.7)' : 'none',
    cursor: 'pointer',
  };

  const imageContainerStyle = {
    height: '200px',
    clipPath: 'polygon(0 0, 100% 0, 100% 100%, 10% 100%)',
    overflow: 'hidden',
  };

  const imageStyle = {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    transition: 'transform 0.3s ease',
    transform: isHovered ? 'scale(1.1)' : 'scale(1)',
  };

  return (
    <Card
      style={cardStyle}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleCardClick}
    >
      <Box style={imageContainerStyle}>
        <img src={concert.poster_url} alt={concert.name} style={imageStyle} />
      </Box>
      <CardContent>
        <Typography gutterBottom variant="h6" component="div" sx={{ fontWeight: 'bold', color: '#00c0ff' }}>
          {concert.name}
        </Typography>
        <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
          <strong>演出者:</strong> {concert.artist}
        </Typography>
        <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
          <strong>日期:</strong> {concert.date}
        </Typography>
        <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
          <strong>地點:</strong> {concert.location}
        </Typography>
      </CardContent>
    </Card>
  );
};

// --- 探索活動主頁面元件 ---
function ExplorePage() {
  const [concerts, setConcerts] = useState([]);
  const [filteredConcerts, setFilteredConcerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 12; // 每頁顯示12個活動


  // 獲取所有演唱會資料
  useEffect(() => {
    const fetchConcerts = async () => {
      try {
        setLoading(true);
        const response = await axios.get('http://localhost:8000/api/concerts');
        setConcerts(response.data.concerts);
        setFilteredConcerts(response.data.concerts);
        setLoading(false);
      } catch (error) {
        console.error('抓取活動資料時發生錯誤:', error);
        setError('無法載入活動資料，請檢查後端服務是否已啟動。');
        setLoading(false);
      }
    };

    fetchConcerts();
  }, []);

  // 搜尋和篩選功能
  useEffect(() => {
    const fetchFilteredConcerts = async () => {
      try {
        let url = 'http://localhost:8000/api/concerts?';
        const params = new URLSearchParams();

        if (selectedCategory) {
          params.append('category', selectedCategory);
        }

        url += params.toString();

        const response = await axios.get(url);
        let filtered = response.data.concerts;

        // 根據搜尋關鍵字篩選（前端篩選）
        if (searchTerm) {
          filtered = filtered.filter(concert =>
            concert.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            concert.artist.toLowerCase().includes(searchTerm.toLowerCase()) ||
            concert.location.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }

        setFilteredConcerts(filtered);
        setCurrentPage(1); // 重置到第一頁
      } catch (error) {
        console.error('篩選活動時發生錯誤:', error);
        // 如果 API 調用失敗，回退到前端篩選
        let filtered = concerts;

        if (searchTerm) {
          filtered = filtered.filter(concert =>
            concert.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            concert.artist.toLowerCase().includes(searchTerm.toLowerCase()) ||
            concert.location.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }

        if (selectedCategory) {
          filtered = filtered.filter(concert =>
            concert.categories && concert.categories.includes(selectedCategory)
          );
        }

        setFilteredConcerts(filtered);
        setCurrentPage(1); // 重置到第一頁
      }
    };

    fetchFilteredConcerts();
  }, [concerts, searchTerm, selectedCategory]);

  // 處理搜尋
  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };

  // 處理分類篩選
  const handleCategoryFilter = (category) => {
    setSelectedCategory(category);
  };

  // 處理分頁變更
  const handlePageChange = (event, value) => {
    setCurrentPage(value);
    // 滾動到頁面頂部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // 計算分頁相關數據
  const totalPages = Math.ceil(filteredConcerts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const concertsToDisplay = filteredConcerts.slice(startIndex, endIndex);



  if (loading) {
    return <Container sx={{ textAlign: 'center', my: 4 }}><CircularProgress sx={{ color: '#00c0ff' }} /></Container>;
  }

  if (error) {
    return <Container sx={{ my: 4 }}><Alert severity="error" sx={{ backgroundColor: '#332222', color: 'white' }}>{error}</Alert></Container>;
  }

  return (
    <Box sx={{ width: '100%' }}>
      {/* --- 海報輪播區塊 --- */}
      <HeroBanner />

      {/* --- 篩選與內容區塊 --- */}
      <Container maxWidth="lg" sx={{ mt: 4, position: 'relative', zIndex: 3 }}>
        {/* 搜尋欄 */}
        <Box sx={{
          mb: 4,
          display: 'flex',
          justifyContent: 'center',
          backgroundColor: 'rgba(26, 26, 26, 0.8)',
          backdropFilter: 'blur(10px)',
          borderRadius: 2,
          p: 3,
          border: '1px solid rgba(0, 192, 255, 0.3)'
        }}>
          <TextField
            variant="outlined"
            placeholder="搜尋活動、藝人或地點..."
            value={searchTerm}
            onChange={handleSearch}
            sx={{
              width: '100%',
              maxWidth: '600px',
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(255, 255, 255, 0.05)',
                '& fieldset': {
                  borderColor: 'rgba(0, 192, 255, 0.5)',
                },
                '&:hover fieldset': {
                  borderColor: '#00c0ff',
                },
                '&.Mui-focused fieldset': {
                  borderColor: '#00c0ff',
                },
              },
              '& .MuiInputBase-input': {
                color: 'white',
              },
              '& .MuiInputBase-input::placeholder': {
                color: '#aaa',
                opacity: 1
              }
            }}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon sx={{ color: '#00c0ff' }} />
                  </InputAdornment>
                ),
              }
            }}
          />
        </Box>

        {/* 分類篩選 */}
        <CategoryFilter
          selectedCategory={selectedCategory}
          onCategoryChange={handleCategoryFilter}
        />

        {/* 搜尋結果統計 */}
        <Box sx={{ mb: 3, textAlign: 'center' }}>
          <Typography variant="h6" sx={{ color: '#00c0ff' }}>
            {searchTerm || selectedCategory ?
              `找到 ${filteredConcerts.length} 個活動` :
              `共有 ${concerts.length} 個精彩活動`
            }
          </Typography>
          {(searchTerm || selectedCategory) && (
            <Typography variant="body2" sx={{ color: '#aaa', mt: 1 }}>
              {searchTerm && `搜尋關鍵字: "${searchTerm}"`}
              {searchTerm && selectedCategory && ' | '}
              {selectedCategory && `分類: ${selectedCategory}`}
            </Typography>
          )}
          {filteredConcerts.length > 0 && (
            <Typography variant="body2" sx={{ color: '#aaa', mt: 1 }}>
              第 {currentPage} 頁，共 {totalPages} 頁 | 顯示第 {startIndex + 1}-{Math.min(endIndex, filteredConcerts.length)} 個活動
            </Typography>
          )}
        </Box>

        <Grid container spacing={4}>
          {concertsToDisplay.length > 0 ? (
            concertsToDisplay.map(concert => (
              <Grid item key={concert.id} xs={12} sm={6} md={4}>
                <ArknightsCard concert={concert} />
              </Grid>
            ))
          ) : (
            <Grid item xs={12}>
              <Box sx={{
                textAlign: 'center',
                py: 8,
                backgroundColor: 'rgba(36, 36, 36, 0.5)',
                borderRadius: '8px',
                border: '1px dashed #444'
              }}>
                <Typography variant="h6" sx={{ color: '#666', mb: 2 }}>
                  沒有找到符合條件的活動
                </Typography>
                <Typography variant="body2" sx={{ color: '#888' }}>
                  試試調整搜尋關鍵字或選擇不同的分類
                </Typography>
                <Button
                  variant="outlined"
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory('');
                  }}
                  sx={{
                    mt: 2,
                    color: '#00c0ff',
                    borderColor: '#00c0ff',
                    '&:hover': {
                      backgroundColor: 'rgba(0, 192, 255, 0.1)',
                    }
                  }}
                >
                  清除篩選條件
                </Button>
              </Box>
            </Grid>
          )}
        </Grid>

        {/* 分頁組件 */}
        {filteredConcerts.length > itemsPerPage && (
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            mt: 6,
            mb: 4
          }}>
            <Pagination
              count={totalPages}
              page={currentPage}
              onChange={handlePageChange}
              size="large"
              sx={{
                '& .MuiPaginationItem-root': {
                  color: 'rgba(255, 255, 255, 0.7)',
                  borderColor: 'rgba(255, 255, 255, 0.3)',
                  backgroundColor: 'rgba(26, 26, 26, 0.8)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  margin: '0 4px',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 192, 255, 0.2)',
                    borderColor: '#00c0ff',
                    color: '#00c0ff',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 15px rgba(0, 192, 255, 0.3)'
                  },
                  '&.Mui-selected': {
                    backgroundColor: '#00c0ff',
                    color: '#000',
                    borderColor: '#00c0ff',
                    fontWeight: 'bold',
                    '&:hover': {
                      backgroundColor: '#0099cc',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 6px 20px rgba(0, 192, 255, 0.4)'
                    }
                  },
                  '&.MuiPaginationItem-ellipsis': {
                    color: 'rgba(255, 255, 255, 0.5)',
                    border: 'none',
                    backgroundColor: 'transparent'
                  }
                }
              }}
            />
          </Box>
        )}
      </Container>
    </Box>
  );
}

export default ExplorePage;