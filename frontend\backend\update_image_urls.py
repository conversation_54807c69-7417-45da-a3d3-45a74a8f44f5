#!/usr/bin/env python3
"""
更新 main.py 中的圖片 URL
將外部 URL 替換為本地 webp 圖片路徑
"""

import json
import re
import os

# 映射文件路徑
MAPPING_FILE = "../public/images/url_mapping.json"
MAIN_PY_FILE = "src/main.py"

def load_url_mapping():
    """載入 URL 映射"""
    try:
        with open(MAPPING_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 無法載入映射文件: {str(e)}")
        return {}

def update_main_py(url_mapping):
    """更新 main.py 中的圖片 URL"""
    try:
        # 讀取 main.py 文件
        with open(MAIN_PY_FILE, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替換所有 URL
        original_content = content
        replaced_count = 0
        
        for original_url, local_path in url_mapping.items():
            # 使用正則表達式確保只替換字符串中的 URL
            pattern = f'"{re.escape(original_url)}"'
            replacement = f'"{local_path}"'
            new_content = re.sub(pattern, replacement, content)
            
            if new_content != content:
                replaced_count += content.count(original_url)
                content = new_content
        
        # 如果有變更，寫回文件
        if content != original_content:
            with open(MAIN_PY_FILE, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 成功更新 {MAIN_PY_FILE}")
            print(f"   替換了 {replaced_count} 個 URL")
        else:
            print(f"ℹ️ 沒有找到需要替換的 URL")
        
    except Exception as e:
        print(f"❌ 更新 main.py 失敗: {str(e)}")

def main():
    """主函數"""
    print("🔄 開始更新圖片 URL...")
    
    # 檢查文件是否存在
    if not os.path.exists(MAPPING_FILE):
        print(f"❌ 映射文件不存在: {MAPPING_FILE}")
        return
    
    if not os.path.exists(MAIN_PY_FILE):
        print(f"❌ main.py 文件不存在: {MAIN_PY_FILE}")
        return
    
    # 載入 URL 映射
    url_mapping = load_url_mapping()
    if not url_mapping:
        print("❌ URL 映射為空")
        return
    
    # 更新 main.py
    update_main_py(url_mapping)
    
    print("✨ 完成！")

if __name__ == "__main__":
    main()
