{"data_mtime": 1753691322, "dep_lines": [4, 7, 11, 13, 14, 15, 16, 17, 1, 3, 4, 5, 6, 8, 10, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 5, 5, 5, 5, 5, 10, 20, 10, 10, 5, 10, 5, 30, 30, 30], "dependencies": ["importlib.util", "email.utils", "anyio.to_thread", "starlette._utils", "starlette.datastructures", "starlette.exceptions", "starlette.responses", "starlette.types", "__future__", "errno", "importlib", "os", "stat", "typing", "anyio", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "5979175beccd9172c56d51387a10f14cab4c45d2", "id": "starlette.staticfiles", "ignore_all": true, "interface_hash": "b701912b0171da0102da59bd9c8eecdc811b0065", "mtime": 1752327555, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\Repos\\gjunedu-platinum-2025\\frontend\\backend\\.venv\\Lib\\site-packages\\starlette\\staticfiles.py", "plugin_data": null, "size": 8478, "suppressed": [], "version_id": "1.15.0"}