#!/usr/bin/env python3
"""
更新前端代碼中的背景圖片 URL
將 PNG 背景圖片引用替換為 webp 格式
"""

import json
import re
import os

# 映射文件路徑
MAPPING_FILE = "../public/background/background_mapping.json"
CSS_FILE = "../src/index.css"

def load_url_mapping():
    """載入 URL 映射"""
    try:
        with open(MAPPING_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 無法載入映射文件: {str(e)}")
        return {}

def update_css_file(url_mapping):
    """更新 CSS 文件中的背景圖片 URL"""
    try:
        # 讀取 CSS 文件
        with open(CSS_FILE, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替換所有 URL
        original_content = content
        replaced_count = 0
        
        for original_url, webp_url in url_mapping.items():
            # 使用正則表達式確保只替換 url() 中的路徑
            pattern = f"url\\(['\"]?{re.escape(original_url)}['\"]?\\)"
            replacement = f"url('{webp_url}')"
            
            new_content = re.sub(pattern, replacement, content)
            
            if new_content != content:
                replaced_count += content.count(original_url)
                content = new_content
        
        # 如果有變更，寫回文件
        if content != original_content:
            with open(CSS_FILE, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 成功更新 {CSS_FILE}")
            print(f"   替換了 {replaced_count} 個背景圖片 URL")
        else:
            print(f"ℹ️ 沒有找到需要替換的背景圖片 URL")
        
    except Exception as e:
        print(f"❌ 更新 CSS 文件失敗: {str(e)}")

def main():
    """主函數"""
    print("🔄 開始更新背景圖片 URL...")
    
    # 檢查文件是否存在
    if not os.path.exists(MAPPING_FILE):
        print(f"❌ 映射文件不存在: {MAPPING_FILE}")
        return
    
    if not os.path.exists(CSS_FILE):
        print(f"❌ CSS 文件不存在: {CSS_FILE}")
        return
    
    # 載入 URL 映射
    url_mapping = load_url_mapping()
    if not url_mapping:
        print("❌ URL 映射為空")
        return
    
    # 更新 CSS 文件
    update_css_file(url_mapping)
    
    print("✨ 完成！")

if __name__ == "__main__":
    main()
