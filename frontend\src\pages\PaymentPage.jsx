import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Paper,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Divider,
  Stepper,
  Step,
  StepLabel,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Payment,
  CreditCard,
  AccountBalance,
  QrCode,
  CheckCircle,
  ArrowBack,
  ConfirmationNumber
} from '@mui/icons-material';

function PaymentPage() {
  const location = useLocation();
  const navigate = useNavigate();
  
  const [orderData, setOrderData] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState('credit_card');
  const [loading, setLoading] = useState(false);
  const [paymentComplete, setPaymentComplete] = useState(false);
  const [completedOrder, setCompletedOrder] = useState(null);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  useEffect(() => {
    if (location.state?.order) {
      setOrderData(location.state.order);
    } else {
      navigate('/');
    }
  }, [location.state, navigate]);

  const handlePaymentMethodChange = (event) => {
    setPaymentMethod(event.target.value);
  };

  const handlePayment = async () => {
    setLoading(true);
    
    try {
      // 模擬付款處理時間
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const response = await fetch(`http://localhost:8000/api/orders/${orderData.id}/payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          payment_method: paymentMethod
        })
      });

      if (!response.ok) {
        throw new Error('付款處理失敗');
      }

      const result = await response.json();
      setCompletedOrder(result.order);
      setPaymentComplete(true);
      setShowSuccessDialog(true);
      
    } catch (error) {
      console.error('付款錯誤:', error);
      alert('付款失敗，請稍後再試');
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSuccessDialog = () => {
    setShowSuccessDialog(false);
    navigate('/');
  };

  if (!orderData) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Typography>載入中...</Typography>
      </Box>
    );
  }

  const containerStyle = {
    minHeight: '100vh',
    backgroundColor: '#121212',
    color: 'white',
    paddingY: '2rem'
  };

  const paperStyle = {
    backgroundColor: 'rgba(36, 36, 36, 0.95)',
    backdropFilter: 'blur(10px)',
    border: '1px solid #444',
    borderRadius: '0',
    clipPath: 'polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 15px 100%, 0 calc(100% - 15px))',
    padding: '2rem',
    marginBottom: '2rem'
  };

  const steps = ['選擇票種', '填寫資料', '付款', '完成'];

  const paymentMethods = [
    { value: 'credit_card', label: '信用卡', icon: <CreditCard /> },
    { value: 'bank_transfer', label: '銀行轉帳', icon: <AccountBalance /> },
    { value: 'line_pay', label: 'LINE Pay', icon: <QrCode /> }
  ];

  if (paymentComplete && completedOrder) {
    return (
      <Box sx={containerStyle}>
        <Container maxWidth="md">
          <Paper elevation={0} sx={paperStyle}>
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <CheckCircle sx={{ fontSize: 80, color: '#4caf50', mb: 2 }} />
              <Typography variant="h4" sx={{ color: '#4caf50', fontWeight: 'bold', mb: 2 }}>
                付款成功！
              </Typography>
              <Typography variant="h6" sx={{ color: '#aaa', mb: 4 }}>
                您的票券已成功購買
              </Typography>

              <Card sx={{
                backgroundColor: 'rgba(0, 192, 255, 0.1)',
                border: '1px solid #00c0ff',
                borderRadius: '0',
                maxWidth: 500,
                mx: 'auto',
                mb: 4
              }}>
                <CardContent>
                  <Typography variant="h6" sx={{ color: '#00c0ff', mb: 2 }}>
                    購票資訊
                  </Typography>
                  <Typography variant="body1" sx={{ color: 'white', mb: 1 }}>
                    <strong>購票人：</strong>{completedOrder.customer_info.name}
                  </Typography>
                  <Typography variant="body1" sx={{ color: 'white', mb: 1 }}>
                    <strong>演唱會：</strong>{completedOrder.concert_name}
                  </Typography>
                  <Typography variant="body1" sx={{ color: 'white', mb: 1 }}>
                    <strong>票種：</strong>{completedOrder.items[0].ticket_type_name}
                  </Typography>
                  <Typography variant="body1" sx={{ color: 'white', mb: 1 }}>
                    <strong>數量：</strong>{completedOrder.items[0].quantity} 張
                  </Typography>
                  <Typography variant="body1" sx={{ color: 'white', mb: 1 }}>
                    <strong>總金額：</strong>NT$ {completedOrder.total_amount.toLocaleString()}
                  </Typography>
                  <Typography variant="body1" sx={{ color: 'white', mb: 2 }}>
                    <strong>購票時間：</strong>{new Date(completedOrder.paid_at).toLocaleString('zh-TW')}
                  </Typography>
                  
                  <Divider sx={{ backgroundColor: '#444', my: 2 }} />
                  
                  <Button
                    variant="outlined"
                    href={completedOrder.qr_code}
                    target="_blank"
                    sx={{
                      color: '#00c0ff',
                      borderColor: '#00c0ff',
                      '&:hover': {
                        backgroundColor: 'rgba(0, 192, 255, 0.1)'
                      }
                    }}
                  >
                    <QrCode sx={{ mr: 1 }} />
                    查看 QR Code
                  </Button>
                </CardContent>
              </Card>

              <Button
                variant="contained"
                onClick={() => navigate('/')}
                sx={{
                  backgroundColor: '#00c0ff',
                  color: 'black',
                  fontWeight: 'bold',
                  padding: '10px 30px',
                  '&:hover': {
                    backgroundColor: '#64d8ff'
                  }
                }}
              >
                返回首頁
              </Button>
            </Box>
          </Paper>
        </Container>
      </Box>
    );
  }

  return (
    <Box sx={containerStyle}>
      <Container maxWidth="lg">
        {/* 進度指示器 */}
        <Paper elevation={0} sx={paperStyle}>
          <Stepper activeStep={2} alternativeLabel sx={{
            '& .MuiStepLabel-label': { color: '#aaa' },
            '& .MuiStepLabel-label.Mui-active': { color: '#00c0ff' },
            '& .MuiStepLabel-label.Mui-completed': { color: '#4caf50' },
            '& .MuiStepIcon-root': { color: '#444' },
            '& .MuiStepIcon-root.Mui-active': { color: '#00c0ff' },
            '& .MuiStepIcon-root.Mui-completed': { color: '#4caf50' }
          }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Paper>

        <Grid container spacing={4}>
          {/* 左側：訂單摘要 */}
          <Grid item xs={12} md={4}>
            <Paper elevation={0} sx={paperStyle}>
              <Typography variant="h6" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 2 }}>
                <ConfirmationNumber sx={{ mr: 1, verticalAlign: 'middle' }} />
                訂單摘要
              </Typography>
              
              <Typography variant="body2" sx={{ color: '#aaa', mb: 1 }}>
                訂單編號：{orderData.id.slice(0, 8)}...
              </Typography>
              <Typography variant="body2" sx={{ color: '#aaa', mb: 1 }}>
                演唱會：{orderData.concert_name}
              </Typography>
              <Typography variant="body2" sx={{ color: '#aaa', mb: 1 }}>
                購票人：{orderData.customer_info.name}
              </Typography>
              <Typography variant="body2" sx={{ color: '#aaa', mb: 1 }}>
                票種：{orderData.items[0].ticket_type_name}
              </Typography>
              <Typography variant="body2" sx={{ color: '#aaa', mb: 1 }}>
                數量：{orderData.items[0].quantity} 張
              </Typography>
              
              <Divider sx={{ backgroundColor: '#444', my: 2 }} />
              
              <Typography variant="h6" sx={{ color: '#00c0ff', fontWeight: 'bold' }}>
                總金額：NT$ {orderData.total_amount.toLocaleString()}
              </Typography>
            </Paper>
          </Grid>

          {/* 右側：付款方式 */}
          <Grid item xs={12} md={8}>
            <Paper elevation={0} sx={paperStyle}>
              <Typography variant="h5" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 3 }}>
                <Payment sx={{ mr: 1, verticalAlign: 'middle' }} />
                選擇付款方式
              </Typography>

              <FormControl component="fieldset" fullWidth>
                <RadioGroup
                  value={paymentMethod}
                  onChange={handlePaymentMethodChange}
                >
                  {paymentMethods.map((method) => (
                    <Card
                      key={method.value}
                      sx={{
                        backgroundColor: paymentMethod === method.value ? 'rgba(0, 192, 255, 0.1)' : 'rgba(60, 60, 60, 0.5)',
                        border: paymentMethod === method.value ? '2px solid #00c0ff' : '1px solid #444',
                        borderRadius: '0',
                        mb: 2,
                        cursor: 'pointer',
                        transition: 'all 0.3s ease'
                      }}
                      onClick={() => setPaymentMethod(method.value)}
                    >
                      <CardContent sx={{ py: 2 }}>
                        <FormControlLabel
                          value={method.value}
                          control={
                            <Radio
                              sx={{
                                color: '#00c0ff',
                                '&.Mui-checked': {
                                  color: '#00c0ff',
                                },
                              }}
                            />
                          }
                          label={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {method.icon}
                              <Typography sx={{ ml: 1, color: 'white' }}>
                                {method.label}
                              </Typography>
                            </Box>
                          }
                        />
                      </CardContent>
                    </Card>
                  ))}
                </RadioGroup>
              </FormControl>

              <Alert 
                severity="info" 
                sx={{ 
                  mt: 3, 
                  mb: 3, 
                  backgroundColor: 'rgba(33, 150, 243, 0.1)', 
                  color: '#64b5f6',
                  '& .MuiAlert-icon': { color: '#64b5f6' }
                }}
              >
                這是模擬付款，實際不會收取費用
              </Alert>

              <Divider sx={{ backgroundColor: '#444', my: 3 }} />

              {/* 操作按鈕 */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  onClick={() => navigate(-1)}
                  disabled={loading}
                  sx={{
                    color: '#aaa',
                    borderColor: '#aaa',
                    '&:hover': {
                      backgroundColor: 'rgba(170, 170, 170, 0.1)',
                      borderColor: '#ccc'
                    }
                  }}
                >
                  <ArrowBack sx={{ mr: 1 }} />
                  返回上一步
                </Button>

                <Button
                  variant="contained"
                  onClick={handlePayment}
                  disabled={loading}
                  sx={{
                    backgroundColor: '#4caf50',
                    color: 'white',
                    fontWeight: 'bold',
                    padding: '10px 30px',
                    clipPath: 'polygon(10% 0, 100% 0, 90% 100%, 0% 100%)',
                    '&:hover': {
                      backgroundColor: '#66bb6a',
                      boxShadow: '0 0 20px rgba(76, 175, 80, 0.5)'
                    },
                    '&:disabled': {
                      backgroundColor: '#555',
                      color: '#888'
                    }
                  }}
                >
                  {loading ? '處理中...' : `確認付款 NT$ ${orderData.total_amount.toLocaleString()}`}
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>

        {/* 成功對話框 */}
        <Dialog
          open={showSuccessDialog}
          onClose={handleCloseSuccessDialog}
          PaperProps={{
            sx: {
              backgroundColor: 'rgba(36, 36, 36, 0.95)',
              color: 'white',
              border: '1px solid #444'
            }
          }}
        >
          <DialogTitle sx={{ textAlign: 'center' }}>
            <CheckCircle sx={{ fontSize: 60, color: '#4caf50', mb: 1 }} />
            <Typography variant="h5" sx={{ color: '#4caf50' }}>
              付款成功！
            </Typography>
          </DialogTitle>
          <DialogContent>
            <Typography sx={{ textAlign: 'center', color: '#aaa' }}>
              您的票券已成功購買，感謝您的支持！
            </Typography>
          </DialogContent>
          <DialogActions sx={{ justifyContent: 'center' }}>
            <Button
              onClick={handleCloseSuccessDialog}
              sx={{
                backgroundColor: '#00c0ff',
                color: 'black',
                '&:hover': {
                  backgroundColor: '#64d8ff'
                }
              }}
            >
              確定
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Box>
  );
}

export default PaymentPage;
