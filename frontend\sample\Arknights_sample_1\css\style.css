@font-face {
    font-family: Spinnaker;
    src: url('../font/Spinnaker-Regular.ttf');
}
@font-face {
    font-family: Saira;
    src: url('../font/SairaSemiCondensed-Light.ttf');
}
@font-face {
    font-family: SairaMedium;
    src: url('../font/SairaSemiCondensed-Medium.ttf');
}

@keyframes rotating{
    0%{
        transform: rotate(0deg);
    }
    100%{
        transform: rotate(360deg);
    }
}

.opening-hide-enter-active, .opening-hide-leave-active{
    transition: all 0.6s 0.4s;
}
.opening-hide-enter, .opening-hide-leave-to{
    opacity: 0;
}
.overlay-hide-enter-active, .overlay-hide-leave-active{
    transition: all 0.4s;
}
.overlay-hide-enter, .overlay-hide-leave-to{
    opacity: 0;
}

body{
    background: black;
    overflow: hidden;
}
p{
    margin: 0;
}

div#opening{
    background: black;
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
}

div#loading-wrap{
    width: 100vw;
    height: 32px;
    position: absolute;
    top: calc((100vh - 32px) / 2);
}
div#loading-bar{
    position: absolute;
    bottom: 0;
    width: 100vw;
    height: 5px;
}
div#loading-bar div{
    background: #ffe63d;
    width: 0px;
    height: 5px;
    position: absolute
}
div#loading-bar div:nth-child(1){
    left: 0;
}
div#loading-bar div:nth-child(2){
    transform: scaleY(-1);
    right: 0;
}
div#loading-percentage{
    color: #ffe63d;
    width: 100vw;
    height: 20px;
}
div#loading-percentage span{
    position: absolute;
    font-family: Spinnaker;
    font-size: 20px;
}
div#loading-percentage span:nth-child(3){
    position: relative;
    text-align: center;
}

div#main{
    background: black;
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 0;
    left: 0;
}
div#background-frame div{
    background: black;
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 97;
}
div#background-image{
    position: absolute;
    /*top: calc((100vh - (100vh * 1714 / 2160)) / 2);*/
    top: 0;
    z-index: 96;
    width: 100vw;
    /*height: calc(100vh * 1714 / 2160);*/
    height: 100vh;
}
div#background-image div{
    width: 100vw;
    /*height: calc(100vh * 1714 / 2160);*/
    height: 100vh;
    background: no-repeat center center;
    background-size: cover;
}
video#background-video{
    position: absolute;
    top: 0;
    left: 0;
    min-width: 100%; 
    min-height: 100%;
}
div#background-square{
    position: absolute;
    /*top: calc(((100vh * 2304 / 2160) - 100vh) / -2);*/
    top: 0;
    left: 0;
    width: 100vw;
    /*height: calc(100vh * 2304 / 2160);*/
    height: 100vh;
    z-index: 99;
}
div#background-square div{
    background: rgba(255, 255, 255, 0.1);
    width: 5vw;
    height: 5vw;
    float: left;
    transition: box-shadow, opacity 0.5s;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0);
    opacity: 0;
}
div#background-square div:hover{
    transition: box-shadow, opacity 0s;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.65);
    opacity: 1;
}
div#background-cross{
    position: absolute;
    /*top: calc(((100vh * 2304 / 2160) - 100vh) / -2);*/
    top: 0;
    left: 0;
    width: 100vw;
    /*height: calc(100vh * 2304 / 2160);*/
    height: 100vh;
    z-index: 97;
}
div#background-cross div{
    background: url('../image/source/cross.png') no-repeat center center;
    background-size: cover;
    width: 5vw;
    height: 5vw;
    float: left;
}
div#app-wrap{
    position: absolute;
    top: 0;
    left: calc((100vw - (100vh * 2820 / 2160)) / 2);
    width: calc(100vh * 2820 / 2160);
    height: 100vh;
    color: white;
    z-index: 98;
    perspective: 1000px;
}
div#app-transform{
    width: calc(100vh * 2820 / 2160);
    height: 100%;
}
div.app{
    position: relative;
}
div.clock{
    top: 0;
    /*left: calc((100vw - (100vh * 2080 / 2160)) / 2);*/
    margin: 0 auto;
    width: calc(100vh * 2068 / 2160);
    height: 100%;
    font-family: Saira;
}
div#clock-second > div{
    position: absolute;
    width: auto;
    height: auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
div#clock-second > div div{
    text-align: center;
}
div#clock-minute > div{
    position: absolute;
    width: auto;
    height: auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
div#clock-minute > div div{
    text-align: center;
}
div#clock-hour > div{
    position: absolute;
    width: auto;
    height: auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
div#clock-hour > div div{
    text-align: center;
}
div#clock-circle > div{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
div#clock-circle > div:nth-child(1) div{
    background: url('../image/source/circle_01.png') no-repeat center center;
    background-size: cover;
    width: calc(100vh * 2068 / 2160);
    height: calc(100vh * 2068 / 2160);
    animation: rotating 240s linear infinite;
}
div#clock-circle > div:nth-child(2) div{
    background: url('../image/source/circle_02.png') no-repeat center center;
    background-size: cover;
    width: calc(100vh * 2000 / 2160);
    height: calc(100vh * 2000 / 2160);
    animation: rotating 200s linear infinite reverse;
}
div#clock-circle > div:nth-child(3) div{
    background: url('../image/source/circle_03.png') no-repeat center center;
    background-size: cover;
    width: calc(100vh * 1830 / 2160);
    height: calc(100vh * 1830 / 2160);
    animation: rotating 190s linear infinite reverse;
}
div#clock-circle > div:nth-child(4) div{
    background: url('../image/source/circle_04.png') no-repeat center center;
    background-size: cover;
    width: calc(100vh * 1500 / 2160);
    height: calc(100vh * 1500 / 2160);
    animation: rotating 180s linear infinite reverse;
    /*filter: drop-shadow(2px 8px 3px rgba(237, 167, 68, 0.4));*/
}
div#clock-circle > div:nth-child(5) div{
    background: url('../image/source/circle_05.png') no-repeat center center;
    background-size: cover;
    width: calc(100vh * 1480 / 2160);
    height: calc(100vh * 1480 / 2160);
    animation: rotating 190s linear infinite;
    /*filter: drop-shadow(2px 8px 3px rgba(255, 230, 86, 0.4));*/
}
div#clock-circle > div:nth-child(6) div{
    background: url('../image/source/circle_06.png') no-repeat center center;
    background-size: cover;
    width: calc(100vh * 1230 / 2160);
    height: calc(100vh * 1230 / 2160);
}
div#clock-circle > div:nth-child(7) div{
    background: url('../image/source/circle_07.png') no-repeat center center;
    background-size: cover;
    width: calc(100vh * 1230 / 2160);
    height: calc(100vh * 1230 / 2160);
    animation: rotating 190s linear infinite reverse;
}
div#clock-circle > div:nth-child(8) div{
    background: url('../image/source/circle_08.png') no-repeat center center;
    background-size: cover;
    width: calc(100vh * 1500 / 2160);
    height: calc(100vh * 1500 / 2160);
    animation: rotating 200s linear infinite;
}
div#clock-circle > div:nth-child(9) div{
    background: url('../image/source/center_logo.png') no-repeat center center;
    background-size: cover;
    width: calc(100vh * 396 / 2160);
    height: calc(100vh * 396 / 2160);
}
div#clock-logo-trans{
    width: calc(100vh * 448 / 2160);
    height: calc(100vh * 448 / 2160);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
div#clock-logo-trans > div{
    width: calc(100vh * 136 / 2160);
    height: calc(100vh * 136 / 2160);
    position: absolute;
}
div#clock-logo-trans > div > div{
    width: calc(100vh * 136 / 2160);
    height: calc(100vh * 136 / 2160);
}
div#clock-logo-trans > div > div div{
    background: url('../image/source/rect_logo.png') no-repeat center center;
    background-size: cover;
    width: calc(100vh * 130 / 2160);
    height: calc(100vh * 130 / 2160);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

div.line{
    float: left;
    top: calc(100vh * 500 / 2160);
    left: calc(100vh * 32 / 2160);
    width: calc(100vw * 360 / 3840);
    height: auto;
    z-index: 98;
}
div#line-title{
    width: calc(100vw * 360 / 3840);
    height: auto;
    font-family: SairaMedium;
    font-size: 1vh;
    color: ffcb82;
    text-shadow: 0px 0px 5px rgba(255, 239, 182, 0.5);
}
div#line-graph{
    margin-top: 4px;
    width: calc(100vw * 360 / 3840);
    height: auto;
}
div#line-graph > div{
    position: relative;
    width: calc(100vw * 360 / 3840);
    height: calc(100vh * 32 / 2160);
    font-size: 0.8vh;
    font-family: SairaMedium;
    margin-bottom: 6px;
    overflow: hidden;
}
div#line-graph > div *{
    position: absolute;
}
div#line-graph > div div{
    height: calc(100vh * 32 / 2160);
}
div#line-graph span{
    padding-top: calc(100vh * 1 / 2160);
    padding-left: 2px;
    opacity: 1.0;
}
div#line-graph-hour span{
    text-shadow: 0px 0px 5px rgba(255, 239, 182, 0.5);
    color: #fff6e1;
}
div#line-graph-hour div:nth-child(1){
    background: rgba(237, 167, 68, 0.15);
    width: calc(100vh * 360 / 2160);
}
div#line-graph-hour div:nth-child(2){
    background: rgba(237, 167, 68, 0.6);
    transition: width linear 0.3s;
}
div#line-graph-min span{
    text-shadow: 0px 0px 5px rgba(183, 249, 255, 0.5);
    color: #b7f9ff;
}
div#line-graph-min div:nth-child(1){
    background: rgba(92, 164, 255, 0.15);
    width: calc(100vh * 320 / 2160);
}
div#line-graph-min div:nth-child(2){
    background: rgba(92, 164, 255, 0.6);
    transition: width linear 0.3s;
}
div#line-graph-sec span{
    text-shadow: 0px 0px 5px rgba(183, 249, 255, 0.5);
    color: #b7f9ff;
}
div#line-graph-sec div:nth-child(1){
    background: rgba(92, 164, 255, 0.15);
    width: calc(100vh * 280 / 2160);
}
div#line-graph-sec div:nth-child(2){
    background: rgba(92, 164, 255, 0.6);
    transition: width linear 0.3s;
}

div.date{
    position: absolute;
    width: calc(100vh * 280 / 2160);
    height: calc(100vh * 280 / 2160);
    bottom: calc(100vh * 560 / 2160);
    left: calc(100vh * 20 / 2160);
}
div.date *{
    position: absolute;
}
div.side-circle{
    font-size: 1.5vh;
    font-family: SairaMedium;
    width: calc(100vh * 280 / 2160);
    height: calc(100vh * 280 / 2160);
}
div.side-circle div:nth-child(1){
    background: url('../image/source/side_circle_01.png') no-repeat center center;
    background-size: cover;
    width: calc(100vh * 280 / 2160);
    height: calc(100vh * 280 / 2160);
}
div.side-circle div:nth-child(2){
    background: url('../image/source/side_circle_02.png') no-repeat center center;
    background-size: cover;
    width: calc(100vh * 266 / 2160);
    height: calc(100vh * 266 / 2160);
    top: calc(((100vh * 280 / 2160) - (100vh * 266 / 2160)) / 2);
    left: calc(((100vh * 280 / 2160) - (100vh * 266 / 2160)) / 2);
}
div.side-circle span{
    position: relative;
}
div#date-wrap div:nth-of-type(1){
    animation: rotating 30s linear infinite;
}
div#date-wrap div:nth-of-type(2){
    animation: rotating 30s linear infinite reverse;
}
div#date-wrap div:nth-of-type(3){
    width: auto;
    height: auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

div.time{
    position: absolute;
    width: calc(100vh * 280 / 2160);
    height: calc(100vh * 280 / 2160);
    bottom: calc(100vh * 200 / 2160);
    left: calc(100vh * 200 / 2160);
}
div.time *{
    position: absolute;
}
div#time-wrap div:nth-of-type(1){
    animation: rotating 30s linear infinite reverse;
}
div#time-wrap div:nth-of-type(2){
    animation: rotating 30s linear infinite;
}
div#time-wrap div:nth-of-type(3){
    width: auto;
    height: auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

div.uptime{
    position: absolute;
    width: calc(100vh * 280 / 2160);
    height: calc(100vh * 280 / 2160);
    top: calc(100vh * 500 / 2160);
    right: calc(100vh * 40 / 2160);
}
div.uptime *{
    position: absolute;
}
div#uptime-wrap div:nth-of-type(1){
    animation: rotating 40s linear infinite reverse;
}
div#uptime-wrap div:nth-of-type(2){
    animation: rotating 40s linear infinite;
}
div#uptime-wrap div:nth-of-type(3){
    width: auto;
    height: auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

div.day{
    position: absolute;
    width: calc(100vh * 280 / 2160);
    height: calc(100vh * 280 / 2160);
    bottom: calc(100vh * 200 / 2160);
    right: calc(100vh * 200 / 2160);
}
div.day *{
    position: absolute;
}
div#day-wrap div:nth-of-type(1){
    animation: rotating 40s linear infinite;
}
div#day-wrap div:nth-of-type(2){
    animation: rotating 40s linear infinite reverse;
}
div#day-wrap div:nth-of-type(3){
    width: auto;
    height: auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

div#change-bg{
    position: absolute;
    top: calc(100vh * 28 / 2160);
    right: calc(100vw * 28 / 3840);
    width: calc(100vh * 62 / 2160);
    height: calc(100vh * 62 / 2160);
    z-index: 100;
}
div#change-bg div{
    background: url('../image/source/change_bg.png') no-repeat center center;
    background-size: cover;
    width: calc(100vh * 62 / 2160);
    height: calc(100vh * 62 / 2160);
    opacity: 0.3;
    transition: opacity 0.3s;
}
div#change-bg div:hover{
    opacity: 1.0;
}

div#overlay{
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.75);
    width: 100vw;
    height: 100vh;
    z-index: 9999;
}
div#overlay-wrap{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: calc(100vw * 2260 / 3840);
    height: calc(100vh * 860 / 2160);
}
div#overlay-wrap > div{
    width: calc(100vw * 2260 / 3840);
    height: calc(100vh * 405 / 2160);
}
div#overlay-wrap > div:nth-child(1){
    margin-bottom: calc(100vh * 50 / 2160);
}
div#overlay-wrap > div div{
    float: left;
    width: calc(100vw * 720 / 3840);
    height: calc(100vh * 405 / 2160);
    margin-right: calc(100vw * 50 / 3840);
    opacity: 0.5;
    transition: opacity 0.3s;
}
div#overlay-wrap > div div:last-child{
    margin-right: 0;
}
div#overlay-wrap > div div:hover{
    opacity: 1;
}
div#overlay-image div:nth-child(1){
    background: url('../image/background/1.jpg') no-repeat center center;
    background-size: cover;
    margin-left: calc(100vw * 385 / 3840);
}
div#overlay-image div:nth-child(2){
    background: url('../image/background/2.jpg') no-repeat center center;
    background-size: cover;
}
div#overlay-video div:nth-child(1){
    background: url('../image/background/3.jpg') no-repeat center center;
    background-size: cover;
}
div#overlay-video div:nth-child(2){
    background: url('../image/background/4.jpg') no-repeat center center;
    background-size: cover;
}
div#overlay-video div:nth-child(3){
    background: url('../image/background/5.jpg') no-repeat center center;
    background-size: cover;
}