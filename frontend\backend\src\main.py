import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from typing import List, Optional
from pydantic import BaseModel, EmailStr
from datetime import datetime, timedelta
import json
import os
import uuid
import hashlib
from database import init_database, get_database

app = FastAPI(title="Synctix 票務平台 API", description="Synctix 演唱會票務系統後端服務")

# 設定 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # 允許您的前端來源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化資料庫
current_dir = os.path.dirname(os.path.abspath(__file__))
base_dir = os.path.dirname(current_dir)  # 回到 backend 目錄

# 靜態文件服務
images_dir = os.path.join(base_dir, "images")
static_dir = os.path.join(base_dir, "static")
app.mount("/images", StaticFiles(directory=images_dir), name="images")
app.mount("/static", StaticFiles(directory=static_dir), name="static")
db = init_database(base_dir)

# 載入資料到資料庫（如果資料庫為空）
def initialize_data():
    """初始化資料庫數據"""
    # 檢查是否已有數據
    if db.count("concerts") == 0:
        print("正在初始化演唱會資料...")
        concerts_file = os.path.join(base_dir, "data", "concerts.json")
        if os.path.exists(concerts_file):
            with open(concerts_file, "r", encoding="utf-8") as f:
                concerts = json.load(f)
            for concert in concerts:
                db.insert("concerts", concert)
            print(f"載入了 {len(concerts)} 個演唱會資料")

    if db.count("hero_banners") == 0:
        print("正在初始化海報資料...")
        banners_file = os.path.join(base_dir, "data", "hero_banners.json")
        if os.path.exists(banners_file):
            with open(banners_file, "r", encoding="utf-8") as f:
                hero_banners = json.load(f)
            for banner in hero_banners:
                db.insert("hero_banners", banner)
            print(f"載入了 {len(hero_banners)} 個海報資料")

    # 初始化管理員用戶
    if db.count("users", {"email": "<EMAIL>"}) == 0:
        admin_user = {
            "id": "admin-001",
            "email": "<EMAIL>",
            "password": hashlib.sha256("admin123".encode()).hexdigest(),
            "name": "系統管理員",
            "phone": "0900-000-000",
            "is_admin": True
        }
        db.insert("users", admin_user)
        print("創建管理員帳號")

# 初始化數據
initialize_data()

print(f"系統啟動：演唱會 {db.count('concerts')} 個，海報 {db.count('hero_banners')} 個，用戶 {db.count('users')} 個")

# 資料庫已經初始化，不再需要內存存儲

# 資料模型定義
class TicketType(BaseModel):
    id: int
    name: str
    price: int
    available: int
    total: int
    description: Optional[str] = None

class Concert(BaseModel):
    id: int
    name: str
    artist: str
    date: str
    time: str
    location: str
    address: str
    poster_url: str
    description: str
    categories: List[str]
    ticketSaleStart: str
    ticketTypes: List[TicketType]
    importantNotes: List[str]
    purchaseInstructions: List[str]
    legalNotices: List[str]
    status: str  # "upcoming", "on_sale", "sold_out", "ended"
    featured: bool = False

# 用戶相關模型
class User(BaseModel):
    id: str
    email: str
    name: str
    phone: str
    created_at: str
    is_admin: bool = False

class UserRegistration(BaseModel):
    email: str
    password: str
    name: str
    phone: str

class UserLogin(BaseModel):
    email: str
    password: str

# 訂單相關模型
class OrderItem(BaseModel):
    ticket_type_id: int
    ticket_type_name: str
    price: int
    quantity: int

class CustomerInfo(BaseModel):
    name: str
    email: str
    phone: str

class Order(BaseModel):
    id: str
    user_id: str
    concert_id: int
    concert_name: str
    customer_info: CustomerInfo
    items: List[OrderItem]
    total_amount: int
    status: str  # "pending", "paid", "cancelled", "refunded"
    payment_method: str
    created_at: str
    paid_at: Optional[str] = None
    qr_code: Optional[str] = None

class CreateOrderRequest(BaseModel):
    concert_id: int
    ticket_type_id: int
    quantity: int
    customer_info: CustomerInfo
    user_id: Optional[str] = None

# 活動管理相關模型
class TicketTypeCreate(BaseModel):
    name: str
    price: int
    total: int
    description: str

class ConcertCreate(BaseModel):
    name: str
    artist: str
    date: str
    time: str
    location: str
    address: str
    poster_url: str
    description: str
    categories: List[str]
    ticketSaleStart: str
    ticketTypes: List[TicketTypeCreate]
    importantNotes: List[str]
    purchaseInstructions: List[str]
    legalNotices: List[str]
    status: str = "upcoming"
    featured: bool = False

class ConcertUpdate(BaseModel):
    name: Optional[str] = None
    artist: Optional[str] = None
    date: Optional[str] = None
    time: Optional[str] = None
    location: Optional[str] = None
    address: Optional[str] = None
    poster_url: Optional[str] = None
    description: Optional[str] = None
    categories: Optional[List[str]] = None
    ticketSaleStart: Optional[str] = None
    ticketTypes: Optional[List[TicketTypeCreate]] = None
    importantNotes: Optional[List[str]] = None
    purchaseInstructions: Optional[List[str]] = None
    legalNotices: Optional[List[str]] = None
    status: Optional[str] = None
    featured: Optional[bool] = None

# API 端點

@app.get("/")
def read_root():
    """根路徑，提供 API 文檔連結"""
    return {
        "message": "歡迎使用 Synctix 票務平台 API",
        "docs": "/docs",
        "version": "1.0.0"
    }


def fix_image_urls(concerts):
    """修復圖片 URL 為完整路徑"""
    for concert in concerts:
        if concert.get("poster_url") and not concert["poster_url"].startswith("http"):
            concert["poster_url"] = f"http://localhost:8000{concert['poster_url']}"
    return concerts

@app.get("/api/concerts")
def get_concerts(
    category: Optional[str] = None,
    status: Optional[str] = None,
    limit: Optional[int] = None,
    offset: Optional[int] = 0
):
    """
    獲取演唱會列表

    參數:
    - category: 篩選類別
    - status: 篩選狀態 (upcoming, on_sale, sold_out, ended)
    - limit: 限制返回數量
    - offset: 偏移量（分頁用）
    """
    # 從資料庫獲取演唱會資料
    filters = {}
    if category:
        # 注意：這裡需要特殊處理，因為 categories 是陣列
        pass  # 稍後處理
    if status:
        filters["status"] = status

    concerts = db.get_all("concerts")

    # 類別篩選（手動處理，因為是陣列欄位）
    if category:
        concerts = [c for c in concerts if category in c.get("categories", [])]

    # 狀態篩選
    if status:
        concerts = [c for c in concerts if c.get("status") == status]

    # 修復圖片 URL
    concerts = fix_image_urls(concerts)

    # 分頁
    total = len(concerts)
    if limit:
        concerts = concerts[offset:offset + limit]
    else:
        concerts = concerts[offset:]

    return {
        "concerts": concerts,
        "total": total,
        "limit": limit,
        "offset": offset
    }


@app.get("/api/concerts/{concert_id}")
def get_concert(concert_id: int):
    """獲取特定演唱會詳細資訊"""
    concert = db.get_by_id("concerts", concert_id)
    if concert:
        # 修復圖片 URL
        if concert.get("poster_url") and not concert["poster_url"].startswith("http"):
            concert["poster_url"] = f"http://localhost:8000{concert['poster_url']}"
        return concert

    raise HTTPException(status_code=404, detail="找不到指定的演唱會")


@app.get("/api/categories")
def get_categories():
    """獲取所有可用的活動類別"""
    categories = set()
    concerts = db.get_all("concerts")
    for concert in concerts:
        categories.update(concert.get("categories", []))

    return {
        "categories": sorted(list(categories)),
        "total": len(categories)
    }


@app.get("/api/featured")
def get_featured_concerts():
    """獲取精選演唱會"""
    featured_concerts = db.query("concerts", {"featured": True})
    return fix_image_urls(featured_concerts)


@app.get("/api/hero-banners")
def get_hero_banners():
    """獲取主頁海報輪播資料"""
    banners = db.get_all("hero_banners")

    # 修復圖片 URL
    for banner in banners:
        if banner.get("image") and not banner["image"].startswith("http"):
            banner["image"] = f"http://localhost:8000{banner['image']}"

    return {
        "banners": banners,
        "total": len(banners)
    }


@app.get("/api/search")
def search_concerts(
    q: str,
    category: Optional[str] = None,
    limit: Optional[int] = 10
):
    """
    搜尋演唱會

    參數:
    - q: 搜尋關鍵字 (搜尋演唱會名稱、藝人名稱、地點)
    - category: 篩選類別
    - limit: 限制返回數量
    """
    results = []
    search_term = q.lower()
    concerts = db.get_all("concerts")

    for concert in concerts:
        # 搜尋演唱會名稱、藝人、地點
        if (search_term in concert["name"].lower() or
            search_term in concert["artist"].lower() or
            search_term in concert["location"].lower()):

            # 如果有類別篩選，檢查是否符合
            if category and category not in concert.get("categories", []):
                continue

            results.append(concert)

    # 限制返回數量
    if limit:
        results = results[:limit]

    return {
        "query": q,
        "total": len(results),
        "results": results
    }


# 用戶認證相關 API

@app.post("/api/auth/register")
def register_user(user_data: UserRegistration):
    """用戶註冊"""
    # 檢查郵箱是否已存在
    existing_user = db.get_by_field("users", "email", user_data.email)
    if existing_user:
        raise HTTPException(status_code=400, detail="此郵箱已被註冊")

    # 創建新用戶
    user_id = str(uuid.uuid4())
    hashed_password = hashlib.sha256(user_data.password.encode()).hexdigest()

    new_user = {
        "id": user_id,
        "email": user_data.email,
        "password": hashed_password,
        "name": user_data.name,
        "phone": user_data.phone,
        "is_admin": False
    }

    db.insert("users", new_user)

    # 返回用戶信息（不包含密碼）
    return {
        "message": "註冊成功",
        "user": {
            "id": user_id,
            "email": user_data.email,
            "name": user_data.name,
            "phone": user_data.phone,
            "is_admin": False
        }
    }


@app.post("/api/auth/login")
def login_user(login_data: UserLogin):
    """用戶登入"""
    hashed_password = hashlib.sha256(login_data.password.encode()).hexdigest()

    # 查找用戶
    users = db.get_by_field("users", "email", login_data.email)
    if users:
        user = users[0]  # 取第一個匹配的用戶
        if user["password"] == hashed_password:
            message = "管理員登入成功" if user["is_admin"] else "登入成功"
            return {
                "message": message,
                "user": {
                    "id": user["id"],
                    "email": user["email"],
                    "name": user["name"],
                    "phone": user["phone"],
                    "is_admin": user["is_admin"]
                }
            }

    raise HTTPException(status_code=401, detail="郵箱或密碼錯誤")


@app.get("/api/users/{user_id}")
def get_user_profile(user_id: str):
    """獲取用戶資料"""
    user = db.get_by_id("users", user_id)
    if user:
        return {
            "id": user["id"],
            "email": user["email"],
            "name": user["name"],
            "phone": user["phone"],
            "is_admin": user["is_admin"],
            "created_at": user.get("created_at", "")
        }

    raise HTTPException(status_code=404, detail="找不到用戶")


# 訂單管理相關 API

@app.post("/api/orders")
def create_order(order_request: CreateOrderRequest):
    """創建訂單"""
    # 查找演唱會
    concert = db.get_by_id("concerts", order_request.concert_id)
    if not concert:
        raise HTTPException(status_code=404, detail="找不到指定的演唱會")

    # 查找票種
    ticket_type = None
    for t in concert["ticketTypes"]:
        if t["id"] == order_request.ticket_type_id:
            ticket_type = t
            break

    if not ticket_type:
        raise HTTPException(status_code=404, detail="找不到指定的票種")

    # 檢查票券庫存
    if ticket_type["available"] < order_request.quantity:
        raise HTTPException(status_code=400, detail="票券庫存不足")

    # 創建訂單
    order_id = str(uuid.uuid4())
    total_amount = ticket_type["price"] * order_request.quantity

    order = {
        "id": order_id,
        "user_id": order_request.user_id or "guest",  # 使用提供的用戶 ID 或設為訪客
        "concert_id": concert["id"],
        "concert_name": concert["name"],
        "customer_info": order_request.customer_info.model_dump(),
        "items": [{
            "ticket_type_id": ticket_type["id"],
            "ticket_type_name": ticket_type["name"],
            "price": ticket_type["price"],
            "quantity": order_request.quantity
        }],
        "total_amount": total_amount,
        "status": "pending",
        "payment_method": "",
        "paid_at": None,
        "qr_code": None
    }

    # 儲存訂單到資料庫
    db.insert("orders", order)

    # 更新票券庫存
    ticket_type["available"] -= order_request.quantity
    db.update("concerts", concert["id"], {"ticketTypes": concert["ticketTypes"]})

    return {
        "message": "訂單創建成功",
        "order": order
    }


@app.get("/api/orders/{order_id}")
def get_order(order_id: str):
    """獲取訂單詳情"""
    order = db.get_by_id("orders", order_id)
    if not order:
        raise HTTPException(status_code=404, detail="找不到指定的訂單")

    return order


@app.post("/api/orders/{order_id}/payment")
def process_payment(order_id: str, payment_data: dict):
    """處理付款"""
    order = db.get_by_id("orders", order_id)
    if not order:
        raise HTTPException(status_code=404, detail="找不到指定的訂單")

    if order["status"] != "pending":
        raise HTTPException(status_code=400, detail="訂單狀態不允許付款")

    # 模擬付款處理
    payment_method = payment_data.get("payment_method", "credit_card")

    # 更新訂單狀態
    updates = {
        "status": "paid",
        "payment_method": payment_method,
        "paid_at": datetime.now().isoformat(),
        "qr_code": f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=SYNCTIX-{order_id}"
    }

    db.update("orders", order_id, updates)

    # 獲取更新後的訂單
    updated_order = db.get_by_id("orders", order_id)

    return {
        "message": "付款成功",
        "order": updated_order
    }


@app.get("/api/users/{user_id}/orders")
def get_user_orders(user_id: str):
    """獲取用戶的所有訂單"""
    user_orders = db.query("orders",
                          filters={"user_id": user_id},
                          sort_by="created_at",
                          reverse=True)

    return {
        "orders": user_orders,
        "total": len(user_orders)
    }


# 管理員相關 API

@app.get("/api/admin/dashboard")
def get_admin_dashboard():
    """獲取管理員儀表板數據"""
    total_concerts = db.count("concerts")
    total_orders = db.count("orders")
    total_users = db.count("users", {"is_admin": False})  # 只計算一般用戶

    # 計算總收入
    paid_orders = db.query("orders", {"status": "paid"})
    total_revenue = sum(order["total_amount"] for order in paid_orders)

    # 最近訂單
    recent_orders = db.query("orders", sort_by="created_at", reverse=True, limit=10)

    # 熱門演唱會（按訂單數量）
    concert_orders = {}
    all_orders = db.get_all("orders")
    for order in all_orders:
        concert_id = order["concert_id"]
        if concert_id not in concert_orders:
            concert_orders[concert_id] = 0
        concert_orders[concert_id] += 1

    popular_concerts = []
    all_concerts = db.get_all("concerts")
    for concert in all_concerts:
        order_count = concert_orders.get(concert["id"], 0)
        popular_concerts.append({
            "concert": concert,
            "order_count": order_count
        })

    popular_concerts.sort(key=lambda x: x["order_count"], reverse=True)

    return {
        "stats": {
            "total_concerts": total_concerts,
            "total_orders": total_orders,
            "total_users": total_users,
            "total_revenue": total_revenue
        },
        "recent_orders": recent_orders,
        "popular_concerts": popular_concerts[:5]
    }


@app.get("/api/admin/orders")
def get_all_orders(
    status: Optional[str] = None,
    limit: Optional[int] = 50,
    offset: Optional[int] = 0
):
    """獲取所有訂單（管理員）"""
    filters = {}
    if status:
        filters["status"] = status

    orders = db.query("orders",
                     filters=filters,
                     sort_by="created_at",
                     reverse=True,
                     limit=limit,
                     offset=offset)

    total = db.count("orders", filters)

    return {
        "orders": orders,
        "total": total,
        "limit": limit,
        "offset": offset
    }


@app.get("/api/admin/users")
def get_all_users(limit: Optional[int] = 50, offset: Optional[int] = 0):
    """獲取所有用戶（管理員）"""
    users = db.query("users",
                    filters={"is_admin": False},  # 只返回一般用戶
                    sort_by="created_at",
                    reverse=True,
                    limit=limit,
                    offset=offset)

    # 移除密碼欄位
    for user in users:
        user.pop("password", None)

    total = db.count("users", {"is_admin": False})

    return {
        "users": users,
        "total": total,
        "limit": limit,
        "offset": offset
    }


# 活動管理相關 API (管理員專用)

@app.post("/api/admin/concerts")
def create_concert(concert_data: ConcertCreate):
    """創建新活動（管理員）"""
    # 生成新的活動 ID
    all_concerts = db.get_all("concerts")
    max_id = max([c.get("id", 0) for c in all_concerts], default=0)
    new_id = max_id + 1

    # 處理票種資料
    ticket_types = []
    for i, ticket_type in enumerate(concert_data.ticketTypes, 1):
        ticket_types.append({
            "id": i,
            "name": ticket_type.name,
            "price": ticket_type.price,
            "available": ticket_type.total,
            "total": ticket_type.total,
            "description": ticket_type.description
        })

    # 創建活動資料
    new_concert = {
        "id": new_id,
        "name": concert_data.name,
        "artist": concert_data.artist,
        "date": concert_data.date,
        "time": concert_data.time,
        "location": concert_data.location,
        "address": concert_data.address,
        "poster_url": concert_data.poster_url,
        "description": concert_data.description,
        "categories": concert_data.categories,
        "ticketSaleStart": concert_data.ticketSaleStart,
        "ticketTypes": ticket_types,
        "importantNotes": concert_data.importantNotes,
        "purchaseInstructions": concert_data.purchaseInstructions,
        "legalNotices": concert_data.legalNotices,
        "status": concert_data.status,
        "featured": concert_data.featured
    }

    # 儲存到資料庫
    db.insert("concerts", new_concert)

    return {
        "message": "活動創建成功",
        "concert": new_concert
    }


@app.put("/api/admin/concerts/{concert_id}")
def update_concert(concert_id: int, concert_data: ConcertUpdate):
    """更新活動（管理員）"""
    # 檢查活動是否存在
    existing_concert = db.get_by_id("concerts", concert_id)
    if not existing_concert:
        raise HTTPException(status_code=404, detail="找不到指定的活動")

    # 準備更新資料
    updates = {}
    for field, value in concert_data.model_dump(exclude_unset=True).items():
        if field == "ticketTypes" and value:
            # 處理票種更新
            ticket_types = []
            for i, ticket_type in enumerate(value, 1):
                ticket_types.append({
                    "id": i,
                    "name": ticket_type["name"],
                    "price": ticket_type["price"],
                    "available": ticket_type["total"],
                    "total": ticket_type["total"],
                    "description": ticket_type["description"]
                })
            updates[field] = ticket_types
        else:
            updates[field] = value

    # 更新資料庫
    db.update("concerts", concert_id, updates)

    # 獲取更新後的活動
    updated_concert = db.get_by_id("concerts", concert_id)

    return {
        "message": "活動更新成功",
        "concert": updated_concert
    }


@app.delete("/api/admin/concerts/{concert_id}")
def delete_concert(concert_id: int):
    """刪除活動（管理員）"""
    # 檢查活動是否存在
    existing_concert = db.get_by_id("concerts", concert_id)
    if not existing_concert:
        raise HTTPException(status_code=404, detail="找不到指定的活動")

    # 檢查是否有相關訂單
    related_orders = db.get_by_field("orders", "concert_id", concert_id)
    if related_orders:
        raise HTTPException(status_code=400, detail="此活動已有訂單，無法刪除")

    # 刪除活動
    db.delete("concerts", concert_id)

    return {
        "message": "活動刪除成功"
    }


@app.get("/api/admin/concerts/manage")
def get_admin_concerts(
    status: Optional[str] = None,
    category: Optional[str] = None,
    limit: Optional[int] = 50,
    offset: Optional[int] = 0
):
    """獲取所有活動（管理員）"""
    filters = {}
    if status:
        filters["status"] = status

    concerts = db.query("concerts",
                       filters=filters,
                       sort_by="created_at",
                       reverse=True,
                       limit=limit,
                       offset=offset)

    # 類別篩選（手動處理，因為是陣列欄位）
    if category:
        concerts = [c for c in concerts if category in c.get("categories", [])]

    # 修復圖片 URL
    concerts = fix_image_urls(concerts)

    total = db.count("concerts", filters)

    return {
        "concerts": concerts,
        "total": total,
        "limit": limit,
        "offset": offset
    }


def main():
    """啟動 FastAPI 服務器"""
    uvicorn.run(
        app,  # 直接使用 app 對象
        host="0.0.0.0",
        port=8000,
        reload=False,  # 直接運行時不需要 reload
        log_level="info"
    )


if __name__ == "__main__":
    main()
