import React, { useState, useEffect } from 'react';
import {
  Box,
  Chip,
  Typography,
  Collapse,
  Button,
  Grid,
  useTheme,
  useMediaQuery
} from '@mui/material';
import { ExpandMore, ExpandLess } from '@mui/icons-material';

const CategoryFilter = ({ selectedCategory, onCategoryChange, showAll = false }) => {
  const [categories, setCategories] = useState([]);
  const [expanded, setExpanded] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/categories');
      const data = await response.json();
      setCategories(data.categories);
    } catch (error) {
      console.error('獲取類別失敗:', error);
    }
  };

  // 決定顯示的類別數量
  const displayLimit = isMobile ? 6 : 8;
  const shouldShowToggle = categories.length > displayLimit;
  const displayCategories = showAll || expanded ? categories : categories.slice(0, displayLimit);

  const handleCategoryClick = (category) => {
    if (selectedCategory === category) {
      onCategoryChange(''); // 取消選擇
    } else {
      onCategoryChange(category);
    }
  };

  const handleToggleExpand = () => {
    setExpanded(!expanded);
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Typography 
        variant="h6" 
        sx={{ 
          mb: 2, 
          fontWeight: 'bold',
          color: theme.palette.mode === 'dark' ? '#fff' : '#333'
        }}
      >
        音樂類別
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <Grid container spacing={1}>
          {/* 全部類別選項 */}
          <Grid item>
            <Chip
              label="全部"
              onClick={() => onCategoryChange('')}
              variant={selectedCategory === '' ? 'filled' : 'outlined'}
              sx={{
                backgroundColor: selectedCategory === '' 
                  ? theme.palette.primary.main 
                  : 'transparent',
                color: selectedCategory === '' 
                  ? '#fff' 
                  : theme.palette.mode === 'dark' ? '#fff' : '#333',
                borderColor: theme.palette.primary.main,
                '&:hover': {
                  backgroundColor: selectedCategory === '' 
                    ? theme.palette.primary.dark 
                    : theme.palette.primary.main + '20',
                },
                transition: 'all 0.3s ease',
                fontWeight: selectedCategory === '' ? 'bold' : 'normal'
              }}
            />
          </Grid>
          
          {/* 類別選項 */}
          {displayCategories.map((category) => (
            <Grid item key={category}>
              <Chip
                label={category}
                onClick={() => handleCategoryClick(category)}
                variant={selectedCategory === category ? 'filled' : 'outlined'}
                sx={{
                  backgroundColor: selectedCategory === category 
                    ? theme.palette.primary.main 
                    : 'transparent',
                  color: selectedCategory === category 
                    ? '#fff' 
                    : theme.palette.mode === 'dark' ? '#fff' : '#333',
                  borderColor: theme.palette.primary.main,
                  '&:hover': {
                    backgroundColor: selectedCategory === category 
                      ? theme.palette.primary.dark 
                      : theme.palette.primary.main + '20',
                  },
                  transition: 'all 0.3s ease',
                  fontWeight: selectedCategory === category ? 'bold' : 'normal'
                }}
              />
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* 展開/收起按鈕 */}
      {shouldShowToggle && !showAll && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
          <Button
            onClick={handleToggleExpand}
            startIcon={expanded ? <ExpandLess /> : <ExpandMore />}
            sx={{
              color: theme.palette.primary.main,
              textTransform: 'none',
              fontWeight: 'bold',
              '&:hover': {
                backgroundColor: theme.palette.primary.main + '10',
              }
            }}
          >
            {expanded ? '收起類別' : `顯示更多類別 (+${categories.length - displayLimit})`}
          </Button>
        </Box>
      )}

      {/* 選中類別提示 */}
      {selectedCategory && (
        <Box sx={{ 
          mt: 2, 
          p: 2, 
          backgroundColor: theme.palette.primary.main + '10',
          borderRadius: 2,
          border: `1px solid ${theme.palette.primary.main}30`
        }}>
          <Typography 
            variant="body2" 
            sx={{ 
              color: theme.palette.primary.main,
              fontWeight: 'bold'
            }}
          >
            正在顯示「{selectedCategory}」類別的演出
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default CategoryFilter;
