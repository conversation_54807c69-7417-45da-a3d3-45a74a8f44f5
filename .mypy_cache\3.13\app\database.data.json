{".class": "MypyFile", "_fullname": "app.database", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Base": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.database.Base", "name": "Base", "type": {".class": "AnyType", "missing_import_name": "app.database.declarative_base", "source_any": {".class": "AnyType", "missing_import_name": "app.database.declarative_base", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "SQLALCHEMY_DATABASE_URL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.database.SQLALCHEMY_DATABASE_URL", "name": "SQLALCHEMY_DATABASE_URL", "type": "builtins.str"}}, "SessionLocal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.database.SessionLocal", "name": "SessionLocal", "type": {".class": "AnyType", "missing_import_name": "app.database.sessionmaker", "source_any": {".class": "AnyType", "missing_import_name": "app.database.sessionmaker", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.database.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.database.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.database.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.database.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.database.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.database.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "create_engine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.database.create_engine", "name": "create_engine", "type": {".class": "AnyType", "missing_import_name": "app.database.create_engine", "source_any": null, "type_of_any": 3}}}, "declarative_base": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.database.declarative_base", "name": "declarative_base", "type": {".class": "AnyType", "missing_import_name": "app.database.declarative_base", "source_any": null, "type_of_any": 3}}}, "engine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.database.engine", "name": "engine", "type": {".class": "AnyType", "missing_import_name": "app.database.create_engine", "source_any": {".class": "AnyType", "missing_import_name": "app.database.create_engine", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "get_db": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.database.get_db", "name": "get_db", "type": null}}, "sessionmaker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.database.sessionmaker", "name": "sessionmaker", "type": {".class": "AnyType", "missing_import_name": "app.database.sessionmaker", "source_any": null, "type_of_any": 3}}}}, "path": "D:\\Repos\\gjunedu-platinum-2025\\pybackend\\src\\app\\database.py"}