{"data_mtime": 1753691321, "dep_lines": [3, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 30, 30, 30, 5], "dependencies": ["app.database", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "9f66e54a3568305d80456bc558df2c222c329e75", "id": "app.models", "ignore_all": true, "interface_hash": "6001f4f7c7abbef1cc4c7c169d0560b9748188e6", "mtime": 1753690105, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\Repos\\gjunedu-platinum-2025\\pybackend\\src\\app\\models.py", "plugin_data": null, "size": 635, "suppressed": ["sqlalchemy"], "version_id": "1.15.0"}