{"data_mtime": 1753691322, "dep_lines": [12, 12, 1, 2, 3, 12, 1, 1, 1, 6, 7, 9, 10, 5, 8], "dep_prios": [10, 10, 10, 5, 5, 20, 5, 30, 30, 5, 5, 5, 5, 5, 5], "dependencies": ["app.crud", "app.models", "os", "datetime", "typing", "app", "builtins", "_frozen_importlib", "abc"], "hash": "2dace588d2bac61cb93f634d2b044bb859bfc2d1", "id": "app.security", "ignore_all": true, "interface_hash": "1759539944dea9aebb1af0e9db69b43fd701115b", "mtime": 1753690105, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\Repos\\gjunedu-platinum-2025\\pybackend\\src\\app\\security.py", "plugin_data": null, "size": 2199, "suppressed": ["google.auth.transport", "google.oauth2", "passlib.context", "sqlalchemy.orm", "dotenv", "jose"], "version_id": "1.15.0"}