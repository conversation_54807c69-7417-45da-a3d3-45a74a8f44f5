# Synctix - 演唱會票務平台
## 2025 第四屆金匠獎 - 題目二
### 專案介紹
Synctix 是一套整合性的演唱會票務平臺，能同時處理數場活動的票務銷售，並可因應高流量、高併發的搶票行為。

### 基本功能項目
1. 活動展示功能：多位歌手、多活動資訊展示（時間、地點、票價），支援搜尋與篩選。
2. 使用者管理：註冊、登入、身分驗證；訂單查詢與票券管理。
3. 購票流程：選擇場次、區域、張數；模擬付款與完成購票流程。
4. 後台管理：主辦方可上架活動、設計票種與票價；查詢銷售紀錄與剩餘票數。
5. 高併發處理：必須能處理模擬「千人同時搶票」的情境；防止超賣與資料錯亂。
6. 風控機制：限購機制、CAPTCHA、防黃牛、IP限制等。
7. 支援RWD響應式介面。

### 加分項目
1. 效能處理：使用快取、排隊機制、佇列系統...等技術處理併發請求。
2. 即時監控與視覺化：顯示票券銷售熱度圖、剩餘票統計、場次熱門排行榜...等。
3. AI加值功能：預測熱門場次 、推薦演唱會給使用者、智能問答助理...等。
4. DevOps實務：使用 CI/CD 工具部署、自動化測試、容器化...等。
5. 使用者體驗強化：支援座位圖視覺化選位、快速搜尋與收藏功能...等。