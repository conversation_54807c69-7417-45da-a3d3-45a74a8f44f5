import React from 'react';
import { IconButton, Tooltip, Box } from '@mui/material';
import { Brightness4, Brightness7 } from '@mui/icons-material';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggle = () => {
  const { isDarkMode, toggleTheme } = useTheme();

  return (
    <Tooltip title={isDarkMode ? '切換到淺色模式' : '切換到深色模式'}>
      <IconButton
        onClick={toggleTheme}
        sx={{
          backgroundColor: 'rgba(0, 192, 255, 0.1)',
          border: '1px solid rgba(0, 192, 255, 0.3)',
          color: '#00c0ff',
          transition: 'all 0.3s ease',
          '&:hover': {
            backgroundColor: 'rgba(0, 192, 255, 0.2)',
            boxShadow: '0 0 15px rgba(0, 192, 255, 0.4)',
            transform: 'scale(1.05)',
          },
        }}
      >
        {isDarkMode ? <Brightness7 /> : <Brightness4 />}
      </IconButton>
    </Tooltip>
  );
};

export default ThemeToggle;
