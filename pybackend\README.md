# Python Project Template
使用 [coke5151/python-project-template](https://github.com/coke5151/python-project-template)

## Features
- 使用 `uv` 進行套件及環境管理
- 已配置好以下插件的設定:
  - Ruff for linting and formatting
  - Mypy for static type checking
  - VSCode integration
  - Jupyter notebook

## 專案結構
```
.
├── notebooks/         # Jupyter notebook files
├── .vscode/          # VSCode configuration
├── README.md         # Project documentation
├── .gitignore       # Git ignore patterns
├── mypy.ini         # Mypy configuration
├── ruff.toml        # Ruff configuration
└── pyrightconfig.json # Pyright configuration
```

## 開發設定
1. Install `uv`:
   ```bash
   # macOS and Linux
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

   ```bash
   # Windows
   powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
   ```
2. Run Code:
   ```bash
   cd pybackend            # 請先確保你當前在 pybackend 目錄下
   uv run src/main.py      # 在虛擬環境執行 src/main.py
   uv run jupyter lab      # 用虛擬環境執行 jupyter lab
   ```
3. 如果需要新增套件
   ```bash
   cd pybackend              # 請先確保你當前在 pybackend 目錄下

   # 安裝電腦中全域的工具 (each tool has its own venv)
   uv tool install mypy ruff # 推薦安裝一下這些

   # 安裝套件，以 pandas 和 requests 為例
   uv add pandas requests  # example packages
   ```

## VSCode 插件
- 必裝
	- [Python](https://marketplace.visualstudio.com/items?itemName=ms-python.python)
	- [Pylance](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-pylance)
	- [Python Debugger](https://marketplace.visualstudio.com/items?itemName=ms-python.debugpy)
	- [Mypy Type Checker](https://marketplace.visualstudio.com/items?itemName=ms-python.mypy-type-checker)
	- [Ruff](https://marketplace.cursorapi.com/items?itemName=charliermarsh.ruff)
	- [Jupyter](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.jupyter)
- 推薦
	- [autoDocstring](https://marketplace.visualstudio.com/items?itemName=njpwerner.autodocstring)
	- [Error Lens](https://marketplace.visualstudio.com/items?itemName=usernamehw.errorlens)
	- [GitLens](https://marketplace.visualstudio.com/items?itemName=eamodio.gitlens)
	- [Git Graph](https://marketplace.visualstudio.com/items?itemName=mhutchie.git-graph)