from datetime import timed<PERSON><PERSON>
from typing import Optional

import uvicorn
from fastapi import Depends, FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, RedirectResponse
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.staticfiles import StaticFiles
from sqlalchemy.orm import Session

from app import crud, models, schemas, security
from app.database import engine, get_db

# 讓 SQLAlchemy 在啟動時根據 models.py 建立所有資料表
models.Base.metadata.create_all(bind=engine)

app = FastAPI(title="Synctix 票務平台整合 API", description="提供完整的票務系統功能，包含用戶認證和票務管理")

# 設定 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # 允許前端來源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 掛載靜態文件
app.mount("/static", StaticFiles(directory="src/static"), name="static")
app.mount("/images", StaticFiles(directory="images"), name="images")

# 初始化 JSON 資料庫
import os

from app.data_initializer import initialize_synctix_data
from app.json_database import init_json_database

current_dir = os.path.dirname(os.path.abspath(__file__))  # src 目錄
base_dir = os.path.dirname(current_dir)  # pybackend 根目錄
json_db = init_json_database(base_dir)

# 初始化票務系統數據
initialize_synctix_data(base_dir)

print(
    f"系統啟動：演唱會 {json_db.count('concerts')} 個，海報 {json_db.count('hero_banners')} 個，用戶 {json_db.count('users')} 個"
)


# --- 提供前端頁面 ---
@app.get("/", response_class=FileResponse, include_in_schema=False)
async def read_index():
    return "src/static/index.html"


@app.get("/dashboard", response_class=FileResponse, include_in_schema=False)
async def read_dashboard():
    return "src/static/dashboard.html"


@app.get("/logout", include_in_schema=False)
async def logout():
    # 後端登出邏輯 (例如清除 session 或 token)
    # 對於 JWT，通常前端會自行清除 token，後端不需要特別處理
    # 這裡只是提供一個端點，讓前端可以呼叫
    return RedirectResponse(url="/", status_code=status.HTTP_302_FOUND)


# --- 註冊 API ---
@app.post(
    "/register", response_model=schemas.UserResponse, status_code=status.HTTP_201_CREATED, tags=["Authentication"]
)
def register_user(user: schemas.UserCreate, db: Session = Depends(get_db)):
    """
    處理使用者註冊。
    - 接收使用者名稱、Email 和密碼。
    - 檢查使用者名稱或 Email 是否已被註冊。
    - 成功後將使用者資料存入資料庫。
    """
    # 檢查使用者是否已存在
    db_user_by_email = crud.get_user_by_email(db, email=user.email)
    if db_user_by_email:
        raise HTTPException(status_code=400, detail="Email already registered")

    db_user_by_username = crud.get_user_by_username(db, username=user.username)
    if db_user_by_username:
        raise HTTPException(status_code=400, detail="Username already registered")

    # 這裡可以加上驗證碼或 reCAPTCHA 的後端驗證邏輯
    # if not verify_captcha(user.captcha_token):
    #     raise HTTPException(status_code=400, detail="Invalid CAPTCHA")

    return crud.create_user(db=db, user=user)


# --- 登入 API ---
@app.post("/login", response_model=schemas.Token, tags=["Authentication"])
def login_for_access_token(db: Session = Depends(get_db), form_data: OAuth2PasswordRequestForm = Depends()):
    """
    處理使用者登入，成功後回傳 JWT Token。
    - 使用 OAuth2PasswordRequestForm，前端需用 form-data 格式傳送 `username` 和 `password`。
    - 驗證使用者帳號密碼。
    - 產生 JWT access token。
    """
    # 這裡可以加上驗證碼的後端驗證邏輯
    # ...

    user = security.authenticate_user(db, username=form_data.username, password=form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=security.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_access_token(data={"sub": user.username}, expires_delta=access_token_expires)
    return {"access_token": access_token, "token_type": "bearer"}


# --- Google 登入 API ---
@app.post("/auth/google", response_model=schemas.Token, tags=["Authentication"])
def login_with_google(token_data: schemas.GoogleToken, db: Session = Depends(get_db)):
    """
    處理 Google 登入。
    - 接收來自 Google 的 ID Token。
    - 驗證 Token 有效性。
    - 如果使用者是第一次登入，自動建立帳號。
    - 回傳內部的 JWT Token。
    """
    google_user_info = security.verify_google_token(token_data.credential)
    if not google_user_info:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid Google token",
        )

    # 檢查使用者是否已存在
    user = crud.get_user_by_google_id(db, google_id=google_user_info["sub"])

    # 如果使用者不存在，就用 Google 資訊建立新使用者
    if not user:
        # 檢查 email 是否已被註冊
        existing_user_by_email = crud.get_user_by_email(db, email=google_user_info["email"])
        if existing_user_by_email:
            # 如果 email 已被註冊，但沒有連結 Google ID，可以選擇在這裡引導使用者合併帳號
            # 目前為了簡單起見，我們先拋出錯誤
            raise HTTPException(
                status_code=400,
                detail="Email already registered with a different account. Please log in with your password.",
            )
        user = crud.create_user_from_google(db, user_info=google_user_info)

    # 產生 JWT token
    access_token_expires = timedelta(minutes=security.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_access_token(data={"sub": user.username}, expires_delta=access_token_expires)
    return {"access_token": access_token, "token_type": "bearer"}


# --- 票務系統 API 端點 ---


# 演唱會相關 API
@app.get("/api/concerts", tags=["Concerts"])
def get_concerts(
    page: int = 1,
    limit: int = 12,
    category: Optional[str] = None,
    search: Optional[str] = None,
    status: Optional[str] = None,
):
    """獲取演唱會列表，支援分頁、搜尋、分類篩選"""
    from app.json_database import get_json_database

    db = get_json_database()

    # 建立篩選條件
    filters = {}
    if status:
        filters["status"] = status

    # 獲取所有演唱會
    concerts = db.query("concerts", filters=filters)

    # 分類篩選
    if category:
        concerts = [c for c in concerts if category in c.get("categories", [])]

    # 搜尋篩選
    if search:
        search_lower = search.lower()
        concerts = [
            c
            for c in concerts
            if search_lower in c.get("name", "").lower()
            or search_lower in c.get("artist", "").lower()
            or search_lower in c.get("location", "").lower()
        ]

    # 分頁
    total = len(concerts)
    offset = (page - 1) * limit
    concerts = concerts[offset : offset + limit]

    return {
        "concerts": concerts,
        "total": total,
        "page": page,
        "limit": limit,
        "total_pages": (total + limit - 1) // limit,
    }


@app.get("/api/concerts/{concert_id}", tags=["Concerts"])
def get_concert_detail(concert_id: int):
    """獲取演唱會詳情"""
    from app.json_database import get_json_database

    db = get_json_database()
    concert = db.get_by_id("concerts", concert_id)

    if not concert:
        raise HTTPException(status_code=404, detail="演唱會不存在")

    return concert


@app.get("/api/categories", tags=["Concerts"])
def get_categories():
    """獲取所有音樂類別"""
    from app.data_initializer import get_all_categories

    categories = get_all_categories()
    return {"categories": categories}


@app.get("/api/hero-banners", tags=["Concerts"])
def get_hero_banners():
    """獲取主頁輪播海報"""
    from app.json_database import get_json_database

    db = get_json_database()
    banners = db.get_all("hero_banners")
    return {"banners": banners}


@app.get("/api/search", tags=["Concerts"])
def search_concerts(q: str):
    """搜尋演唱會"""
    from app.json_database import get_json_database

    db = get_json_database()

    if not q:
        return {"concerts": []}

    search_fields = ["name", "artist", "location", "description"]
    results = db.search("concerts", search_fields, q)

    return {"concerts": results}


# 訂單相關 API
@app.post("/api/orders", tags=["Orders"])
def create_order(order_request: schemas.CreateOrderRequest):
    """創建訂單"""
    import uuid
    from datetime import datetime

    from app.json_database import get_json_database

    db = get_json_database()

    # 獲取演唱會資訊
    concert = db.get_by_id("concerts", order_request.concert_id)
    if not concert:
        raise HTTPException(status_code=404, detail="演唱會不存在")

    # 獲取票種資訊
    ticket_type = None
    for tt in concert.get("ticketTypes", []):
        if tt.get("id") == order_request.ticket_type_id:
            ticket_type = tt
            break

    if not ticket_type:
        raise HTTPException(status_code=404, detail="票種不存在")

    # 檢查票券庫存
    if ticket_type.get("available", 0) < order_request.quantity:
        raise HTTPException(status_code=400, detail="票券庫存不足")

    # 創建訂單
    order_id = f"order-{uuid.uuid4().hex[:8]}"
    order = {
        "id": order_id,
        "user_id": order_request.user_id or "guest",
        "concert_id": order_request.concert_id,
        "concert_name": concert.get("name"),
        "customer_info": order_request.customer_info.dict(),
        "items": [
            {
                "ticket_type_id": ticket_type.get("id"),
                "ticket_type_name": ticket_type.get("name"),
                "price": ticket_type.get("price"),
                "quantity": order_request.quantity,
            }
        ],
        "total_amount": ticket_type.get("price") * order_request.quantity,
        "status": "pending",
        "payment_method": "",
        "created_at": datetime.now().isoformat(),
        "paid_at": None,
        "qr_code": None,
    }

    # 更新票券庫存
    for i, tt in enumerate(concert.get("ticketTypes", [])):
        if tt.get("id") == order_request.ticket_type_id:
            concert["ticketTypes"][i]["available"] -= order_request.quantity
            break

    # 保存訂單和更新後的演唱會資料
    db.insert("orders", order)
    db.update("concerts", order_request.concert_id, {"ticketTypes": concert.get("ticketTypes")})

    return {"order_id": order_id, "status": "success"}


@app.get("/api/orders/{order_id}", tags=["Orders"])
def get_order(order_id: str):
    """獲取訂單詳情"""
    from app.json_database import get_json_database

    db = get_json_database()
    order = db.get_by_id("orders", order_id)

    if not order:
        raise HTTPException(status_code=404, detail="訂單不存在")

    return order


@app.post("/api/orders/{order_id}/payment", tags=["Orders"])
def process_payment(order_id: str, payment_info: schemas.PaymentInfo):
    """處理訂單付款"""
    from datetime import datetime

    from app.json_database import get_json_database

    db = get_json_database()
    order = db.get_by_id("orders", order_id)

    if not order:
        raise HTTPException(status_code=404, detail="訂單不存在")

    if order.get("status") == "paid":
        raise HTTPException(status_code=400, detail="訂單已支付")

    # 更新訂單狀態
    updates = {
        "status": "paid",
        "payment_method": payment_info.payment_method,
        "paid_at": datetime.now().isoformat(),
        "qr_code": f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=SYNCTIX-{order_id}",
    }

    db.update("orders", order_id, updates)

    return {"status": "success", "message": "付款成功"}


# 用戶相關 API (票務系統)
@app.post("/api/auth/register", tags=["Auth"])
def register_synctix_user(user_data: schemas.UserRegistration):
    """用戶註冊 (票務系統)"""
    import hashlib
    import uuid
    from datetime import datetime

    from app.json_database import get_json_database

    db = get_json_database()

    # 檢查郵箱是否已被註冊
    existing_users = db.get_by_field("users", "email", user_data.email)
    if existing_users:
        raise HTTPException(status_code=400, detail="此郵箱已被註冊")

    # 創建新用戶
    user_id = f"user-{uuid.uuid4().hex[:8]}"
    new_user = {
        "id": user_id,
        "email": user_data.email,
        "password": hashlib.sha256(user_data.password.encode()).hexdigest(),
        "name": user_data.name,
        "phone": user_data.phone,
        "is_admin": False,
        "created_at": datetime.now().isoformat(),
    }

    db.insert("users", new_user)

    # 返回用戶資訊（不包含密碼）
    user_response = new_user.copy()
    user_response.pop("password")

    return user_response


@app.post("/api/auth/login", tags=["Auth"])
def login_user(login_data: schemas.UserLogin):
    """用戶登入 (票務系統)"""
    import hashlib

    from app.json_database import get_json_database

    db = get_json_database()

    # 檢查用戶是否存在
    users = db.get_by_field("users", "email", login_data.email)
    if not users:
        raise HTTPException(status_code=401, detail="郵箱或密碼錯誤")

    user = users[0]

    # 驗證密碼
    hashed_password = hashlib.sha256(login_data.password.encode()).hexdigest()
    if user.get("password") != hashed_password:
        raise HTTPException(status_code=401, detail="郵箱或密碼錯誤")

    # 返回用戶資訊（不包含密碼）
    user_response = user.copy()
    user_response.pop("password")

    return user_response


# 管理員相關 API
@app.get("/api/admin/dashboard", tags=["Admin"])
def get_admin_dashboard():
    """獲取管理員儀表板數據"""
    from app.json_database import get_json_database

    db = get_json_database()

    total_concerts = db.count("concerts")
    total_orders = db.count("orders")
    total_users = db.count("users", {"is_admin": False})  # 只計算一般用戶

    # 計算總收入
    paid_orders = db.query("orders", {"status": "paid"})
    total_revenue = sum(order["total_amount"] for order in paid_orders)

    # 最近訂單
    recent_orders = db.query("orders", sort_by="created_at", reverse=True, limit=10)

    # 熱門演唱會（按訂單數量）
    concert_orders = {}
    all_orders = db.get_all("orders")
    for order in all_orders:
        concert_id = order["concert_id"]
        if concert_id not in concert_orders:
            concert_orders[concert_id] = 0
        concert_orders[concert_id] += 1

    # 獲取熱門演唱會詳情
    popular_concerts = []
    for concert_id, order_count in sorted(concert_orders.items(), key=lambda x: x[1], reverse=True)[:5]:
        concert = db.get_by_id("concerts", concert_id)
        if concert:
            concert["order_count"] = order_count
            popular_concerts.append(concert)

    return {
        "stats": {
            "total_concerts": total_concerts,
            "total_orders": total_orders,
            "total_users": total_users,
            "total_revenue": total_revenue,
        },
        "recent_orders": recent_orders,
        "popular_concerts": popular_concerts,
    }


@app.get("/api/admin/concerts/manage", tags=["Admin"])
def get_manage_concerts():
    """獲取管理活動列表"""
    from app.json_database import get_json_database

    db = get_json_database()
    concerts = db.get_all("concerts")

    return {"concerts": concerts}


@app.post("/api/admin/concerts", tags=["Admin"])
def create_concert(concert_data: schemas.ConcertCreate):
    """創建新演唱會"""
    from app.json_database import get_json_database

    db = get_json_database()

    # 獲取下一個 ID
    all_concerts = db.get_all("concerts")
    next_id = max([c.get("id", 0) for c in all_concerts], default=0) + 1

    # 創建演唱會資料
    concert = concert_data.dict()
    concert["id"] = next_id

    db.insert("concerts", concert)

    return {"id": next_id, "status": "success"}


@app.put("/api/admin/concerts/{concert_id}", tags=["Admin"])
def update_concert(concert_id: int, concert_data: schemas.ConcertCreate):
    """更新演唱會資訊"""
    from app.json_database import get_json_database

    db = get_json_database()

    existing_concert = db.get_by_id("concerts", concert_id)
    if not existing_concert:
        raise HTTPException(status_code=404, detail="演唱會不存在")

    # 更新演唱會資料
    updates = concert_data.dict()
    db.update("concerts", concert_id, updates)

    return {"status": "success"}


@app.delete("/api/admin/concerts/{concert_id}", tags=["Admin"])
def delete_concert(concert_id: int):
    """刪除演唱會"""
    from app.json_database import get_json_database

    db = get_json_database()

    existing_concert = db.get_by_id("concerts", concert_id)
    if not existing_concert:
        raise HTTPException(status_code=404, detail="演唱會不存在")

    db.delete("concerts", concert_id)

    return {"status": "success"}


@app.get("/api/admin/orders", tags=["Admin"])
def get_all_orders():
    """獲取所有訂單"""
    from app.json_database import get_json_database

    db = get_json_database()
    orders = db.query("orders", sort_by="created_at", reverse=True)

    return {"orders": orders}


@app.get("/api/admin/users", tags=["Admin"])
def get_all_users():
    """獲取所有用戶"""
    from app.json_database import get_json_database

    db = get_json_database()
    users = db.get_all("users")

    # 移除密碼欄位
    for user in users:
        user.pop("password", None)

    return {"users": users}


def main():
    uvicorn.run(
        "main:app",  # 使用字符串導入路徑以支持 reload
        host="127.0.0.1",
        port=8000,
        reload=True,  # 開發模式下自動重載
        log_level="debug",
    )


if __name__ == "__main__":
    main()
