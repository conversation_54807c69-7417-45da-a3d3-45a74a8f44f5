import React, { useEffect, useRef } from 'react';
import { Box } from '@mui/material';
import { useTheme } from '../contexts/ThemeContext';

// 🎨 高性能粒子系統組件 - 模仿明日方舟官網效果
const ParticleSystem = () => {
  const { isDarkMode } = useTheme();
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const particlesRef = useRef([]);
  const mouseRef = useRef({ x: 0, y: 0 });

  // 粒子類別
  class Particle {
    constructor(canvas) {
      this.canvas = canvas;
      this.reset();
    }

    reset() {
      this.x = Math.random() * this.canvas.width;
      this.y = this.canvas.height + Math.random() * 100;
      this.size = Math.random() * 2 + 0.5;
      this.speedY = Math.random() * 1 + 0.2;
      this.speedX = (Math.random() - 0.5) * 0.3;
      this.opacity = Math.random() * 0.8 + 0.2;
      this.life = 1;
      this.decay = Math.random() * 0.005 + 0.002;
      this.color = isDarkMode ? 
        `rgba(0, 192, 255, ${this.opacity})` : 
        `rgba(25, 118, 210, ${this.opacity})`;
    }

    update(mouseX, mouseY) {
      // 基本移動
      this.y -= this.speedY;
      this.x += this.speedX;
      
      // 滑鼠互動效果
      const dx = mouseX - this.x;
      const dy = mouseY - this.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      
      if (distance < 100) {
        const force = (100 - distance) / 100;
        this.x -= (dx / distance) * force * 0.5;
        this.y -= (dy / distance) * force * 0.5;
      }
      
      // 生命週期
      this.life -= this.decay;
      
      if (this.y < -10 || this.life <= 0 || this.x < -10 || this.x > this.canvas.width + 10) {
        this.reset();
      }
    }

    draw(ctx) {
      ctx.save();
      ctx.globalAlpha = this.opacity * this.life;
      ctx.fillStyle = this.color;
      ctx.beginPath();
      ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
      ctx.fill();
      
      // 添加光暈效果
      ctx.globalAlpha = (this.opacity * this.life) * 0.3;
      ctx.beginPath();
      ctx.arc(this.x, this.y, this.size * 3, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.restore();
    }
  }

  // 連線效果
  const drawConnections = (ctx, particles) => {
    ctx.strokeStyle = isDarkMode ? 'rgba(0, 192, 255, 0.1)' : 'rgba(25, 118, 210, 0.1)';
    ctx.lineWidth = 0.5;
    
    for (let i = 0; i < particles.length; i++) {
      for (let j = i + 1; j < particles.length; j++) {
        const dx = particles[i].x - particles[j].x;
        const dy = particles[i].y - particles[j].y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < 120) {
          ctx.globalAlpha = (120 - distance) / 120 * 0.5;
          ctx.beginPath();
          ctx.moveTo(particles[i].x, particles[i].y);
          ctx.lineTo(particles[j].x, particles[j].y);
          ctx.stroke();
        }
      }
    }
  };

  // 滑鼠移動事件
  useEffect(() => {
    const handleMouseMove = (event) => {
      mouseRef.current.x = event.clientX;
      mouseRef.current.y = event.clientY;
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // 初始化畫布
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // 初始化粒子
    particlesRef.current = [];
    const particleCount = window.innerWidth < 768 ? 30 : 60; // 移動設備減少粒子數量
    
    for (let i = 0; i < particleCount; i++) {
      particlesRef.current.push(new Particle(canvas));
    }

    return () => {
      window.removeEventListener('resize', resizeCanvas);
    };
  }, []);

  // 動畫循環
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 更新和繪製粒子
      particlesRef.current.forEach(particle => {
        particle.update(mouseRef.current.x, mouseRef.current.y);
        particle.draw(ctx);
      });

      // 繪製連線效果
      drawConnections(ctx, particlesRef.current);

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isDarkMode]);

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: -3,
        pointerEvents: 'none',
      }}
    >
      <canvas
        ref={canvasRef}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
        }}
      />
    </Box>
  );
};

export default ParticleSystem;
