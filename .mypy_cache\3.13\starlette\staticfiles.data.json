{".class": "MypyFile", "_fullname": "starlette.staticfiles", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FileResponse": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.FileResponse", "kind": "Gdef"}, "HTTPException": {".class": "SymbolTableNode", "cross_ref": "starlette.exceptions.HTTPException", "kind": "Gdef"}, "Headers": {".class": "SymbolTableNode", "cross_ref": "starlette.datastructures.Headers", "kind": "Gdef"}, "NotModifiedResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["starlette.responses.Response"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.staticfiles.NotModifiedResponse", "name": "NotModifiedResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "starlette.staticfiles.NotModifiedResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.staticfiles", "mro": ["starlette.staticfiles.NotModifiedResponse", "starlette.responses.Response", "builtins.object"], "names": {".class": "SymbolTable", "NOT_MODIFIED_HEADERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "starlette.staticfiles.NotModifiedResponse.NOT_MODIFIED_HEADERS", "name": "NOT_MODIFIED_HEADERS", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "starlette.staticfiles.NotModifiedResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["starlette.staticfiles.NotModifiedResponse", "starlette.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NotModifiedResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.staticfiles.NotModifiedResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.staticfiles.NotModifiedResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PathLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "starlette.staticfiles.PathLike", "line": 19, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}}}, "Receive": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Receive", "kind": "Gdef"}, "RedirectResponse": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.RedirectResponse", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.Response", "kind": "Gdef"}, "Scope": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Scope", "kind": "Gdef"}, "Send": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Send", "kind": "Gdef"}, "StaticFiles": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.staticfiles.StaticFiles", "name": "StaticFiles", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "starlette.staticfiles.StaticFiles", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.staticfiles", "mro": ["starlette.staticfiles.StaticFiles", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "starlette.staticfiles.StaticFiles.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "arg_types": ["starlette.staticfiles.StaticFiles", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Receive"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of StaticFiles", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "directory", "packages", "html", "check_dir", "follow_symlink"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "starlette.staticfiles.StaticFiles.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "directory", "packages", "html", "check_dir", "follow_symlink"], "arg_types": ["starlette.staticfiles.StaticFiles", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.staticfiles.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StaticFiles", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "all_directories": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.staticfiles.StaticFiles.all_directories", "name": "all_directories", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.staticfiles.PathLike"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "check_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "starlette.staticfiles.StaticFiles.check_config", "name": "check_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.staticfiles.StaticFiles"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_config of StaticFiles", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config_checked": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.staticfiles.StaticFiles.config_checked", "name": "config_checked", "type": "builtins.bool"}}, "directory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.staticfiles.StaticFiles.directory", "name": "directory", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.staticfiles.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "file_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "full_path", "stat_result", "scope", "status_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "starlette.staticfiles.StaticFiles.file_response", "name": "file_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "full_path", "stat_result", "scope", "status_code"], "arg_types": ["starlette.staticfiles.StaticFiles", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.staticfiles.PathLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "os.stat_result"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "file_response of StaticFiles", "ret_type": "starlette.responses.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "follow_symlink": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.staticfiles.StaticFiles.follow_symlink", "name": "follow_symlink", "type": "builtins.bool"}}, "get_directories": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "directory", "packages"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "starlette.staticfiles.StaticFiles.get_directories", "name": "get_directories", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "directory", "packages"], "arg_types": ["starlette.staticfiles.StaticFiles", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.staticfiles.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_directories of StaticFiles", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.staticfiles.PathLike"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scope"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "starlette.staticfiles.StaticFiles.get_path", "name": "get_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scope"], "arg_types": ["starlette.staticfiles.StaticFiles", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_path of StaticFiles", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "path", "scope"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "starlette.staticfiles.StaticFiles.get_response", "name": "get_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "path", "scope"], "arg_types": ["starlette.staticfiles.StaticFiles", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_response of StaticFiles", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "starlette.responses.Response"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "html": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.staticfiles.StaticFiles.html", "name": "html", "type": "builtins.bool"}}, "is_not_modified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "response_headers", "request_headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "starlette.staticfiles.StaticFiles.is_not_modified", "name": "is_not_modified", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "response_headers", "request_headers"], "arg_types": ["starlette.staticfiles.StaticFiles", "starlette.datastructures.Headers", "starlette.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_not_modified of StaticFiles", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lookup_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "starlette.staticfiles.StaticFiles.lookup_path", "name": "lookup_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["starlette.staticfiles.StaticFiles", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lookup_path of StaticFiles", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "os.stat_result"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "packages": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.staticfiles.StaticFiles.packages", "name": "packages", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.staticfiles.StaticFiles.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.staticfiles.StaticFiles", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "URL": {".class": "SymbolTableNode", "cross_ref": "starlette.datastructures.URL", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.staticfiles.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.staticfiles.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.staticfiles.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.staticfiles.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.staticfiles.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.staticfiles.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "anyio": {".class": "SymbolTableNode", "cross_ref": "anyio", "kind": "Gdef"}, "errno": {".class": "SymbolTableNode", "cross_ref": "errno", "kind": "Gdef"}, "get_route_path": {".class": "SymbolTableNode", "cross_ref": "starlette._utils.get_route_path", "kind": "Gdef"}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parsedate": {".class": "SymbolTableNode", "cross_ref": "email.utils.parsedate", "kind": "Gdef"}, "stat": {".class": "SymbolTableNode", "cross_ref": "stat", "kind": "Gdef"}}, "path": "d:\\Repos\\gjunedu-platinum-2025\\frontend\\backend\\.venv\\Lib\\site-packages\\starlette\\staticfiles.py"}