<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Authentication Test</title>
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        background-color: #eef2f7;
        margin: 0;
        padding: 20px;
        box-sizing: border-box;
      }
      #main-container {
        background-color: #ffffff;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        text-align: center;
        width: 100%;
        max-width: 500px;
        box-sizing: border-box;
      }
      h1 {
        color: #2c3e50;
        margin-bottom: 25px;
        font-size: 2em;
      }
      h2 {
        color: #34495e;
        margin-top: 30px;
        margin-bottom: 15px;
        font-size: 1.5em;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
      }
      .form-group {
        margin-bottom: 15px;
        text-align: left;
      }
      .form-group label {
        display: block;
        margin-bottom: 5px;
        color: #555;
        font-weight: bold;
      }
      .form-group input[type="text"],
      .form-group input[type="email"],
      .form-group input[type="password"] {
        width: calc(100% - 20px);
        padding: 10px;
        border: 1px solid #ccc;
        border-radius: 6px;
        font-size: 1em;
        box-sizing: border-box;
      }
      button {
        background-color: #007bff;
        color: white;
        padding: 12px 25px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 1.1em;
        transition: background-color 0.3s ease;
        width: 100%;
        box-sizing: border-box;
        margin-top: 10px;
      }
      button:hover {
        background-color: #0056b3;
      }
      .google-login-section {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
      }
      #response {
        margin-top: 25px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        background-color: #f9f9f9;
        width: 100%;
        max-width: 450px;
        word-wrap: break-word;
        white-space: pre-wrap;
        text-align: left;
        font-family: "Courier New", Courier, monospace;
        font-size: 0.9em;
        box-sizing: border-box;
        min-height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333;
      }
      #response.success {
        border-color: #28a745;
        background-color: #e6ffe6;
        color: #1a6a2e;
      }
      #response.error {
        border-color: #dc3545;
        background-color: #ffe6e6;
        color: #8b0000;
      }
    </style>
  </head>
  <body>
    <div id="main-container">
      <h1>FastAPI Authentication Test</h1>

      <!-- Registration Section -->
      <h2>Register</h2>
      <form id="registrationForm">
        <div class="form-group">
          <label for="regUsername">Username:</label>
          <input type="text" id="regUsername" name="username" required />
        </div>
        <div class="form-group">
          <label for="regEmail">Email:</label>
          <input type="email" id="regEmail" name="email" required />
        </div>
        <div class="form-group">
          <label for="regPassword">Password:</label>
          <input type="password" id="regPassword" name="password" required />
        </div>
        <button type="submit">Register</button>
      </form>

      <!-- Login Section -->
      <h2>Login</h2>
      <form id="loginForm">
        <div class="form-group">
          <label for="loginUsername">Username:</label>
          <input type="text" id="loginUsername" name="username" required />
        </div>
        <div class="form-group">
          <label for="loginPassword">Password:</label>
          <input type="password" id="loginPassword" name="password" required />
        </div>
        <button type="submit">Login</button>
      </form>

      <!-- Google Login Section -->
      <div class="google-login-section">
        <h2>Login with Google</h2>
        <div
          id="g_id_onload"
          data-client_id="151842620307-gga2p9ilnc52aetbkvva2l1hqvcrk4oj.apps.googleusercontent.com"
          data-callback="handleCredentialResponse"
          data-auto_select="false"
        ></div>
        <div
          class="g_id_signin"
          data-type="standard"
          data-shape="rectangular"
          data-theme="outline"
          data-text="signin_with"
          data-size="large"
          data-logo_alignment="left"
        ></div>
      </div>

      <h2>Backend Response:</h2>
      <pre id="response">Waiting for action...</pre>
    </div>

    <!-- Google Platform Library -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>

    <script>
      const responseContainer = document.getElementById("response");

      function updateResponse(message, isSuccess = true) {
        responseContainer.textContent = message;
        responseContainer.className = ""; // Clear previous classes
        if (isSuccess) {
          responseContainer.classList.add("success");
        } else {
          responseContainer.classList.add("error");
        }
      }

      // --- Google Login ---
      async function handleCredentialResponse(response) {
        console.log("Encoded JWT ID token: " + response.credential);
        updateResponse("Sending Google token to backend...", true);

        try {
          const backendResponse = await fetch("/auth/google", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ credential: response.credential }),
          });

          const data = await backendResponse.json();

          if (backendResponse.ok) {
            localStorage.setItem("access_token", data.access_token);
            updateResponse("Google Login Successful! Redirecting...", true);
            window.location.href = "/dashboard";
          } else {
            updateResponse(
              "Google Login Failed:\n\n" + JSON.stringify(data, null, 2),
              false
            );
          }
        } catch (error) {
          console.error("Error sending token to backend:", error);
          updateResponse(
            "An error occurred during Google login: " + error.message,
            false
          );
        }
      }

      // --- Registration ---
      document
        .getElementById("registrationForm")
        .addEventListener("submit", async (event) => {
          event.preventDefault();
          updateResponse("Registering user...", true);

          const username = document.getElementById("regUsername").value;
          const email = document.getElementById("regEmail").value;
          const password = document.getElementById("regPassword").value;

          try {
            const backendResponse = await fetch("/register", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({ username, email, password }),
            });

            const data = await backendResponse.json();

            if (backendResponse.ok) {
              updateResponse(
                "Registration Successful!\n\n" + JSON.stringify(data, null, 2),
                true
              );
              document.getElementById("registrationForm").reset(); // Clear form
            } else {
              updateResponse(
                "Registration Failed:\n\n" + JSON.stringify(data, null, 2),
                false
              );
            }
          } catch (error) {
            console.error("Error during registration:", error);
            updateResponse(
              "An error occurred during registration: " + error.message,
              false
            );
          }
        });

      // --- Login ---
      document
        .getElementById("loginForm")
        .addEventListener("submit", async (event) => {
          event.preventDefault();
          updateResponse("Logging in...", true);

          const username = document.getElementById("loginUsername").value;
          const password = document.getElementById("loginPassword").value;

          // For traditional form-data submission
          const formData = new URLSearchParams();
          formData.append("username", username);
          formData.append("password", password);

          try {
            const backendResponse = await fetch("/login", {
              method: "POST",
              headers: {
                "Content-Type": "application/x-www-form-urlencoded",
              },
              body: formData.toString(),
            });

            const data = await backendResponse.json();

            if (backendResponse.ok) {
              localStorage.setItem("access_token", data.access_token);
              updateResponse("Login Successful! Redirecting...", true);
              window.location.href = "/dashboard";
            } else {
              updateResponse(
                "Login Failed:\n\n" + JSON.stringify(data, null, 2),
                false
              );
            }
          } catch (error) {
            console.error("Error during login:", error);
            updateResponse(
              "An error occurred during login: " + error.message,
              false
            );
          }
        });
    </script>
  </body>
</html>
